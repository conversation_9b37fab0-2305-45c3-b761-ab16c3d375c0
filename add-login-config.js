/**
 * 为所有HTML页面添加login-config.js引用
 * 此脚本会检查所有HTML文件，确保它们都正确引用了login-config.js
 */

const fs = require('fs');
const path = require('path');

// 获取所有 HTML 文件
const htmlFiles = fs.readdirSync('.').filter(file => file.endsWith('.html'));

console.log(`找到 ${htmlFiles.length} 个需要处理的 HTML 文件`);

// 遍历所有 HTML 文件
let modifiedCount = 0;
htmlFiles.forEach(file => {
    console.log(`处理文件: ${file}`);
    
    try {
        // 读取文件内容
        let content = fs.readFileSync(file, 'utf8');
        
        // 检查文件是否已经引用了login-config.js
        if (content.includes('src="js/login-config.js"')) {
            console.log(`文件 ${file} 已经引用了login-config.js，无需修改`);
            return;
        }
        
        // 在components.js之前添加login-config.js引用
        if (content.includes('src="js/components.js"')) {
            content = content.replace(
                '<script src="js/components.js"></script>', 
                '<script src="js/login-config.js"></script>\n<script src="js/components.js"></script>'
            );
            modifiedCount++;
            
            // 写入修改后的内容
            fs.writeFileSync(file, content, 'utf8');
            console.log(`成功修改文件: ${file}`);
        } else {
            console.log(`文件 ${file} 中未找到components.js引用，无法添加login-config.js引用`);
        }
    } catch (error) {
        console.error(`处理文件 ${file} 时发生错误:`, error.message);
    }
});

console.log(`完成! 成功修改了 ${modifiedCount} 个文件`);
