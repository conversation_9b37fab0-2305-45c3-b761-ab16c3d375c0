<!DOCTYPE html> <html lang="zh-CN"> <head> <meta charset="UTF-8"> <meta name="viewport" content="width=device-width, initial-scale=1.0"> <title>农资采购金融服务 - 智慧农业平台</title> <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet"> <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet"> <link rel="stylesheet" href="css/common.css"> <link rel="stylesheet" href="css/navbar.css"> <style>
:root {
--primary-color: #28a745;
--secondary-color: #6c757d;
--light-bg: #f8f9fa;
--card-shadow: 0 4px 6px rgba(0,0,0,0.1);
--transition: all 0.3s ease;
--supplies-color: #27ae60;
}
body {
background-color: var(--light-bg);
font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}
.supplies-header {
background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80');
background-size: cover;
background-position: center;
height: 300px;
color: white;
display: flex;
align-items: center;
margin-bottom: 30px;
border-radius: 10px;
}
.supplies-card {
background: white;
padding: 20px;
border-radius: 10px;
box-shadow: var(--card-shadow);
margin-bottom: 30px;
min-height: auto;
}
.stat-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 20px;
transition: var(--transition);
}
.stat-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.stat-icon {
font-size: 2rem;
color: var(--supplies-color);
margin-bottom: 10px;
}
.stat-value {
font-size: 1.5rem;
font-weight: bold;
color: var(--supplies-color);
margin-bottom: 5px;
}
.stat-label {
font-size: 0.9rem;
color: var(--secondary-color);
}
.filter-btn {
padding: 5px 15px;
border-radius: 20px;
background-color: white;
border: 1px solid #ddd;
margin-right: 10px;
margin-bottom: 10px;
cursor: pointer;
transition: var(--transition);
}
.filter-btn.active {
background-color: var(--primary-color);
color: white;
border-color: var(--primary-color);
}
.filter-btn:hover:not(.active) {
background-color: #f0f0f0;
}
.product-item {
background: var(--light-bg);
border-radius: 10px;
overflow: hidden;
margin-bottom: 20px;
transition: var(--transition);
min-height: auto;
}
.product-item:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.product-img {
width: 100%;
height: 200px;
object-fit: cover;
}
.product-info {
padding: 15px;
}
.product-title {
font-weight: bold;
font-size: 1.1rem;
margin-bottom: 5px;
display: flex;
justify-content: space-between;
align-items: center;
}
.product-badge {
display: inline-block;
padding: 2px 8px;
border-radius: 15px;
font-size: 0.7rem;
background-color: rgba(39, 174, 96, 0.1);
color: var(--supplies-color);
}
.product-desc {
color: var(--secondary-color);
margin-bottom: 10px;
font-size: 0.9rem;
height: 40px;
overflow: hidden;
}
.product-meta {
display: flex;
justify-content: space-between;
margin-bottom: 10px;
font-size: 0.9rem;
}
.product-price {
font-weight: bold;
color: var(--supplies-color);
}
.product-unit {
color: var(--secondary-color);
}
.product-features {
display: flex;
flex-wrap: wrap;
gap: 5px;
margin-bottom: 10px;
}
.product-feature {
display: inline-block;
padding: 2px 8px;
border-radius: 15px;
font-size: 0.7rem;
background-color: #f0f0f0;
color: var(--secondary-color);
}
.product-actions {
display: flex;
gap: 10px;
}
.finance-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 15px;
transition: var(--transition);
min-height: auto;
}
.finance-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.finance-header {
display: flex;
align-items: center;
margin-bottom: 15px;
}
.finance-icon {
width: 50px;
height: 50px;
border-radius: 10px;
background-color: var(--supplies-color);
color: white;
display: flex;
align-items: center;
justify-content: center;
font-size: 1.5rem;
margin-right: 15px;
flex-shrink: 0;
}
.finance-title {
font-weight: bold;
font-size: 1.1rem;
margin-bottom: 5px;
}
.finance-subtitle {
font-size: 0.9rem;
color: var(--secondary-color);
}
.finance-body {
margin-bottom: 15px;
}
.finance-description {
color: var(--secondary-color);
margin-bottom: 15px;
}
.finance-feature {
background-color: white;
padding: 10px;
border-radius: 5px;
margin-bottom: 10px;
}
.finance-feature-title {
font-weight: bold;
margin-bottom: 5px;
display: flex;
align-items: center;
}
.finance-feature-icon {
color: var(--supplies-color);
margin-right: 5px;
}
.finance-feature-text {
font-size: 0.9rem;
}
.finance-footer {
text-align: center;
}
.step-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 15px;
transition: var(--transition);
position: relative;
}
.step-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.step-header {
display: flex;
align-items: center;
margin-bottom: 15px;
}
.step-number {
width: 40px;
height: 40px;
border-radius: 50%;
background-color: var(--supplies-color);
color: white;
display: flex;
align-items: center;
justify-content: center;
font-weight: bold;
margin-right: 15px;
flex-shrink: 0;
}
.step-title {
font-weight: bold;
font-size: 1.1rem;
}
.step-body {
margin-bottom: 15px;
}
.step-description {
color: var(--secondary-color);
margin-bottom: 15px;
}
.step-connector {
position: absolute;
left: 20px;
top: 55px;
width: 2px;
height: calc(100% - 40px);
background-color: var(--supplies-color);
z-index: 0;
}
.faq-item {
margin-bottom: 15px;
}
.faq-question {
font-weight: bold;
margin-bottom: 5px;
cursor: pointer;
display: flex;
justify-content: space-between;
align-items: center;
}
.faq-answer {
color: var(--secondary-color);
display: none;
padding: 10px 0;
}
.faq-answer.active {
display: block;
}
.calculator-form {
background: var(--light-bg);
padding: 20px;
border-radius: 10px;
margin-bottom: 20px;
}
.calculator-result {
background: white;
padding: 15px;
border-radius: 10px;
margin-top: 20px;
border: 1px solid #ddd;
}
.result-item {
display: flex;
justify-content: space-between;
margin-bottom: 10px;
}
.result-label {
color: var(--secondary-color);
}
.result-value {
font-weight: bold;
}
.result-total {
font-size: 1.2rem;
font-weight: bold;
color: var(--supplies-color);
border-top: 1px solid #ddd;
padding-top: 10px;
margin-top: 10px;
}
.chart-container {
position: relative;
height: 300px;
width: 100%;
margin-bottom: 20px;
}
.nav-tabs .nav-link {
color: var(--secondary-color);
}
.nav-tabs .nav-link.active {
color: var(--supplies-color);
font-weight: bold;
}
.tab-content {
padding: 20px 0;
}
</style> </head> <body> <!-- 导航栏 -->
<div id="navbar-container"></div>
<!-- 主要内容 --> <div class="container py-5"> <div class="supplies-header"> <div class="container"> <h1>农资采购金融服务</h1> <p class="lead">提供种子、肥料、农药等农资产品的一站式采购服务，并配套灵活的金融支持方案，降低农业生产成本</p> <div class="mt-4"> <button class="btn btn-success btn-lg me-2"><i class="fas fa-shopping-cart me-2"></i>立即采购</button> <button class="btn btn-outline-light btn-lg"><i class="fas fa-calculator me-2"></i>金融方案计算</button> </div> </div> </div> <!-- 顶部统计卡片 --> <div class="row mb-4"> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-seedling stat-icon"></i> <div class="stat-value">1,280</div> <div class="stat-label">农资产品种类</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-users stat-icon"></i> <div class="stat-value">3,560</div> <div class="stat-label">服务农户数量</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-money-bill-wave stat-icon"></i> <div class="stat-value">2,500万</div> <div class="stat-label">金融支持额度</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-percentage stat-icon"></i> <div class="stat-value">15-30%</div> <div class="stat-label">采购成本节省</div> </div> </div> </div> <!-- 主要内容区域 --> <div class="row"> <!-- 左侧内容 --> <div class="col-md-8"> <!-- 农资产品区 --> <div class="supplies-card mb-4"> <div class="d-flex justify-content-between align-items-center mb-4"> <h4>热门农资产品</h4> <div class="input-group" style="width: 250px;"> <input type="text" class="form-control" placeholder="搜索农资产品"> <button class="btn btn-outline-secondary"><i class="fas fa-search"></i></button> </div> </div> <div class="mb-3"> <button class="filter-btn active">全部产品</button> <button class="filter-btn">种子类</button> <button class="filter-btn">肥料类</button> <button class="filter-btn">农药类</button> <button class="filter-btn">农膜类</button> <button class="filter-btn">其他类</button> </div> <div class="row"> <div class="col-md-6 mb-4"> <div class="product-item"> <img src="images/product1.jpg" alt="有机稻花香大米种子" class="product-img"> <div class="product-info"> <div class="product-title"> <span>有机稻花香大米种子</span> <span class="product-badge">热销</span> </div> <div class="product-desc">高产优质稻花香大米种子，稻花香浓郁，米糊细腻，口感好</div> <div class="product-meta"> <div class="product-price">128元</div> <div class="product-unit">规格：10公斤/袋</div> </div> <div class="product-features"> <span class="product-feature"><i class="fas fa-leaf"></i> 有机种植</span> <span class="product-feature"><i class="fas fa-certificate"></i> 品质保证</span> <span class="product-feature"><i class="fas fa-truck"></i> 包邮包送</span> </div> <div class="product-actions"> <button class="btn btn-primary w-100"><i class="fas fa-shopping-cart me-2"></i>加入采购单</button> </div> </div> </div> </div> <div class="col-md-6 mb-4"> <div class="product-item"> <img src="images/product2.jpg" alt="有机复合肥" class="product-img"> <div class="product-info"> <div class="product-title"> <span>有机复合肥</span> <span class="product-badge">特惠</span> </div> <div class="product-desc">高效有机复合肥，富含氮磷钾等多种元素，促进作物生长</div> <div class="product-meta"> <div class="product-price">198元</div> <div class="product-unit">规格：25公斤/袋</div> </div> <div class="product-features"> <span class="product-feature"><i class="fas fa-leaf"></i> 有机肥料</span> <span class="product-feature"><i class="fas fa-recycle"></i> 绿色环保</span> <span class="product-feature"><i class="fas fa-truck"></i> 包邮包送</span> </div> <div class="product-actions"> <button class="btn btn-primary w-100"><i class="fas fa-shopping-cart me-2"></i>加入采购单</button> </div> </div> </div> </div> <div class="col-md-6 mb-4"> <div class="product-item"> <img src="images/product3.jpg" alt="生物有机农药" class="product-img"> <div class="product-info"> <div class="product-title"> <span>生物有机农药</span> <span class="product-badge">新品</span> </div> <div class="product-desc">生物有机农药，无毒无害，对作物安全，高效防治病虫害</div> <div class="product-meta"> <div class="product-price">168元</div> <div class="product-unit">规格：5公斤/瓶</div> </div> <div class="product-features"> <span class="product-feature"><i class="fas fa-shield-alt"></i> 生物防治</span> <span class="product-feature"><i class="fas fa-recycle"></i> 绿色环保</span> <span class="product-feature"><i class="fas fa-truck"></i> 包邮包送</span> </div> <div class="product-actions"> <button class="btn btn-primary w-100"><i class="fas fa-shopping-cart me-2"></i>加入采购单</button> </div> </div> </div> </div> <div class="col-md-6 mb-4"> <div class="product-item"> <img src="images/product4.jpg" alt="生物降解膜" class="product-img"> <div class="product-info"> <div class="product-title"> <span>生物降解膜</span> <span class="product-badge">推荐</span> </div> <div class="product-desc">生物降解农膜，使用后可自然降解，不污染土壤，保护环境</div> <div class="product-meta"> <div class="product-price">258元</div> <div class="product-unit">规格：50米/卷</div> </div> <div class="product-features"> <span class="product-feature"><i class="fas fa-recycle"></i> 生物降解</span> <span class="product-feature"><i class="fas fa-leaf"></i> 环保材料</span> <span class="product-feature"><i class="fas fa-truck"></i> 包邮包送</span> </div> <div class="product-actions"> <button class="btn btn-primary w-100"><i class="fas fa-shopping-cart me-2"></i>加入采购单</button> </div> </div> </div> </div> </div> <div class="text-center mt-3"> <button class="btn btn-outline-primary">查看更多农资产品</button> </div> </div> <!-- 金融服务方案 --> <div class="supplies-card mb-4"> <h4 class="mb-4">金融服务方案</h4> <div class="row"> <div class="col-md-6 mb-4"> <div class="finance-card"> <div class="finance-header"> <div class="finance-icon"> <i class="fas fa-hand-holding-usd"></i> </div> <div> <div class="finance-title">农资采购贷款</div> <div class="finance-subtitle">专为农资采购提供的低息贷款</div> </div> </div> <div class="finance-body"> <div class="finance-description">
为农户提供专属的农资采购贷款，利率低，审批快，还款灵活，解决农户采购农资的资金压力。
</div> <div class="finance-feature"> <div class="finance-feature-title"> <i class="fas fa-percentage finance-feature-icon"></i> <span>低息率优惠</span> </div> <div class="finance-feature-text">年利率低至5.8%，远低于市场平均水平</div> </div> <div class="finance-feature"> <div class="finance-feature-title"> <i class="fas fa-bolt finance-feature-icon"></i> <span>快速审批</span> </div> <div class="finance-feature-text">最快24小时内完成审批，资金直接到账</div> </div> <div class="finance-feature"> <div class="finance-feature-title"> <i class="fas fa-calendar-alt finance-feature-icon"></i> <span>灵活还款</span> </div> <div class="finance-feature-text">支持分期还款，可根据农业生产周期制定还款计划</div> </div> </div> <div class="finance-footer"> <button class="btn btn-success">立即申请</button> </div> </div> </div> <div class="col-md-6 mb-4"> <div class="finance-card"> <div class="finance-header"> <div class="finance-icon"> <i class="fas fa-credit-card"></i> </div> <div> <div class="finance-title">农资信用卡</div> <div class="finance-subtitle">专为农资采购设计的信用卡</div> </div> </div> <div class="finance-body"> <div class="finance-description">
农资信用卡是为农户采购农资产品量身定制的信用卡，享受多项优惠和增值服务，让农资采购更灵活、更经济。
</div> <div class="finance-feature"> <div class="finance-feature-title"> <i class="fas fa-gift finance-feature-icon"></i> <span>采购折扣</span> </div> <div class="finance-feature-text">使用农资信用卡采购农资产品享受95折优惠</div> </div> <div class="finance-feature"> <div class="finance-feature-title"> <i class="fas fa-undo finance-feature-icon"></i> <span>免息期</span> </div> <div class="finance-feature-text">最长支持60天免息还款期，缓解资金压力</div> </div> <div class="finance-feature"> <div class="finance-feature-title"> <i class="fas fa-coins finance-feature-icon"></i> <span>积分返现</span> </div> <div class="finance-feature-text">每消费100元积1分，积分可兑换农资产品或现金</div> </div> </div> <div class="finance-footer"> <button class="btn btn-success">立即申请</button> </div> </div> </div> <div class="col-md-6 mb-4"> <div class="finance-card"> <div class="finance-header"> <div class="finance-icon"> <i class="fas fa-users"></i> </div> <div> <div class="finance-title">农户合作社采购</div> <div class="finance-subtitle">集体采购享受批发价</div> </div> </div> <div class="finance-body"> <div class="finance-description">
加入农户合作社，通过集体采购的方式，享受批发价格，显著降低农资采购成本，提高农业生产效益。
</div> <div class="finance-feature"> <div class="finance-feature-title"> <i class="fas fa-tags finance-feature-icon"></i> <span>批发价格</span> </div> <div class="finance-feature-text">直接享受批发价，比市场价低15%-30%</div> </div> <div class="finance-feature"> <div class="finance-feature-title"> <i class="fas fa-truck finance-feature-icon"></i> <span>统一配送</span> </div> <div class="finance-feature-text">统一配送到村，节省物流成本，提高配送效率</div> </div> <div class="finance-feature"> <div class="finance-feature-title"> <i class="fas fa-handshake finance-feature-icon"></i> <span>专业指导</span> </div> <div class="finance-feature-text">提供农资使用技术指导，提高农资使用效率</div> </div> </div> <div class="finance-footer"> <button class="btn btn-success">加入合作社</button> </div> </div> </div> <div class="col-md-6 mb-4"> <div class="finance-card"> <div class="finance-header"> <div class="finance-icon"> <i class="fas fa-file-invoice-dollar"></i> </div> <div> <div class="finance-title">农资订单融资</div> <div class="finance-subtitle">基于订单的融资服务</div> </div> </div> <div class="finance-body"> <div class="finance-description">
基于农产品销售订单，提供农资采购融资服务，解决农户生产过程中的资金需求，实现产销对接。
</div> <div class="finance-feature"> <div class="finance-feature-title"> <i class="fas fa-file-contract finance-feature-icon"></i> <span>订单融资</span> </div> <div class="finance-feature-text">凭销售订单可获得订单金额的70%的融资</div> </div> <div class="finance-feature"> <div class="finance-feature-title"> <i class="fas fa-shield-alt finance-feature-icon"></i> <span>收购保障</span> </div> <div class="finance-feature-text">提供农产品收购保障，降低农户销售风险</div> </div> <div class="finance-feature"> <div class="finance-feature-title"> <i class="fas fa-sync-alt finance-feature-icon"></i> <span>循环使用</span> </div> <div class="finance-feature-text">订单完成后可继续申请，实现资金循环使用</div> </div> </div> <div class="finance-footer"> <button class="btn btn-success">立即申请</button> </div> </div> </div> </div> </div> <!-- 申请流程 --> <div class="supplies-card mb-4"> <h4 class="mb-4">申请流程</h4> <div class="step-card"> <div class="step-header"> <div class="step-number">1</div> <div class="step-title">选择采购产品和金融方案</div> </div> <div class="step-body"> <div class="step-description">
浏览平台上的农资产品，选择需要的产品加入采购单，并选择适合的金融方案。
</div> </div> <div class="step-connector"></div> </div> <div class="step-card"> <div class="step-header"> <div class="step-number">2</div> <div class="step-title">提交申请资料</div> </div> <div class="step-body"> <div class="step-description">
填写申请表格，提供身份证、农户证、土地承包合同等资料，并提交审核。
</div> </div> <div class="step-connector"></div> </div> <div class="step-card"> <div class="step-header"> <div class="step-number">3</div> <div class="step-title">审核及额度核定</div> </div> <div class="step-body"> <div class="step-description">
平台审核申请资料，根据农户信用状况、种植规模等因素核定金融额度。
</div> </div> <div class="step-connector"></div> </div> <div class="step-card"> <div class="step-header"> <div class="step-number">4</div> <div class="step-title">签署协议及付款</div> </div> <div class="step-body"> <div class="step-description">
审核通过后，签署采购协议和金融服务协议，并根据选择的金融方案完成付款。
</div> </div> <div class="step-connector"></div> </div> <div class="step-card"> <div class="step-header"> <div class="step-number">5</div> <div class="step-title">农资配送及售后服务</div> </div> <div class="step-body"> <div class="step-description">
平台安排农资配送，并提供农资使用指导、技术支持等售后服务。
</div> </div> </div> </div> </div> <!-- 右侧内容 --> <div class="col-md-4"> <!-- 采购单 --> <div class="supplies-card"> <h4 class="mb-4">采购单</h4> <div class="alert alert-primary"> <i class="fas fa-info-circle me-2"></i>
您的采购单中有 <strong>3</strong> 件产品，总计 <strong>494元</strong> </div> <div class="list-group mb-3"> <div class="list-group-item d-flex justify-content-between align-items-center"> <div> <div class="fw-bold">有机稻花香大米种子</div> <div class="small text-secondary">128元 x 1袋</div> </div> <div> <span class="text-primary fw-bold">128元</span> <button class="btn btn-sm btn-outline-danger ms-2"><i class="fas fa-trash-alt"></i></button> </div> </div> <div class="list-group-item d-flex justify-content-between align-items-center"> <div> <div class="fw-bold">有机复合肥</div> <div class="small text-secondary">198元 x 1袋</div> </div> <div> <span class="text-primary fw-bold">198元</span> <button class="btn btn-sm btn-outline-danger ms-2"><i class="fas fa-trash-alt"></i></button> </div> </div> <div class="list-group-item d-flex justify-content-between align-items-center"> <div> <div class="fw-bold">生物有机农药</div> <div class="small text-secondary">168元 x 1瓶</div> </div> <div> <span class="text-primary fw-bold">168元</span> <button class="btn btn-sm btn-outline-danger ms-2"><i class="fas fa-trash-alt"></i></button> </div> </div> </div> <div class="d-grid gap-2"> <button class="btn btn-success"><i class="fas fa-shopping-cart me-2"></i>立即结算</button> <button class="btn btn-outline-primary"><i class="fas fa-calculator me-2"></i>金融方案计算</button> </div> </div> <!-- 金融方案计算器 --> <div class="supplies-card mt-4"> <h4 class="mb-4">金融方案计算器</h4> <div class="calculator-form"> <div class="mb-3"> <label class="form-label">采购金额（元）</label> <input type="number" class="form-control" value="494"> </div> <div class="mb-3"> <label class="form-label">金融方式</label> <select class="form-select"> <option>农资采购贷款</option> <option>农资信用卡</option> <option>农户合作社采购</option> <option>农资订单融资</option> </select> </div> <div class="mb-3"> <label class="form-label">还款期限（月）</label> <select class="form-select"> <option>3个月</option> <option>6个月</option> <option selected>12个月</option> <option>24个月</option> </select> </div> <div class="d-grid"> <button class="btn btn-primary">计算方案</button> </div> <div class="calculator-result"> <div class="result-item"> <div class="result-label">采购金额：</div> <div class="result-value">494元</div> </div> <div class="result-item"> <div class="result-label">利率：</div> <div class="result-value">5.8%/年</div> </div> <div class="result-item"> <div class="result-label">还款期限：</div> <div class="result-value">12个月</div> </div> <div class="result-item"> <div class="result-label">每月还款额：</div> <div class="result-value">42.35元</div> </div> <div class="result-item"> <div class="result-label">总还款额：</div> <div class="result-value">508.20元</div> </div> <div class="result-item"> <div class="result-label">总利息：</div> <div class="result-value">14.20元</div> </div> <div class="result-total"> <div class="result-label">预计节省：</div> <div class="result-value">148.20元 (30%)</div> </div> </div> </div> </div> <!-- 常见问题 --> <div class="supplies-card mt-4"> <h4 class="mb-4">常见问题</h4> <div class="faq-item"> <div class="faq-question" onclick="toggleFaq(this)"> <span>如何选择适合的金融方案？</span> <i class="fas fa-chevron-down"></i> </div> <div class="faq-answer">
选择金融方案需要考虑以下因素：1) 采购金额大小，小额采购可选择农资信用卡，大额采购可选择农资采购贷款；2) 还款能力和周期，可根据自身的农业生产周期选择适合的还款期限；3) 是否有固定的农产品销售渠道，如有可选择农资订单融资。您也可以使用我们的金融方案计算器进行比较。
</div> </div> <div class="faq-item"> <div class="faq-question" onclick="toggleFaq(this)"> <span>申请金融服务需要哪些材料？</span> <i class="fas fa-chevron-down"></i> </div> <div class="faq-answer">
申请金融服务通常需要以下材料：1) 身份证原件及复印件；2) 农户证或土地承包经营权证明；3) 近期农业生产经营情况说明；4) 近期银行流水或收支记录；5) 如有农产品销售订单，请提供订单合同。不同金融方案可能有特定的材料要求，具体请咨询客服人员。
</div> </div> <div class="faq-item"> <div class="faq-question" onclick="toggleFaq(this)"> <span>金融服务的审批时间是多久？</span> <i class="fas fa-chevron-down"></i> </div> <div class="faq-answer">
金融服务的审批时间因方案不同而有所差异。农资信用卡审批最快，通常可在24小时内完成；农资采购贷款和农资订单融资通常需要1-3个工作日；农户合作社采购需要等待合作社成员凑齐后统一采购，时间可能稍长。我们会尽量加快审批流程，确保您能及时获得所需的农资产品。
</div> </div> <div class="faq-item"> <div class="faq-question" onclick="toggleFaq(this)"> <span>如何加入农户合作社？</span> <i class="fas fa-chevron-down"></i> </div> <div class="faq-answer">
加入农户合作社的步骤很简单：1) 在平台上点击“加入合作社”按钮；2) 填写个人信息和农业生产情况；3) 提交身份证和农户证复印件；4) 等待审核通过；5) 缴纳合作社会费（如有）。加入后，您可以参与合作社组织的集体采购活动，享受批发价格和专业技术指导服务。
</div> </div> <div class="faq-item"> <div class="faq-question" onclick="toggleFaq(this)"> <span>农资产品的配送时间是多久？</span> <i class="fas fa-chevron-down"></i> </div> <div class="faq-answer">
农资产品的配送时间因地区和产品类型而异。一般情况下，发货后1-3个工作日内可送达。对于远程地区，可能需要更长时间。大宗采购或合作社采购可能需要等待集中配送。您可以在下单后通过系统查询配送进度，我们会确保在农业生产季节前及时送达。
</div> </div> </div> </div> </div> </div> <!-- 页脚 -->
<div id="footer-container"></div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="js/components.js"></script>
<script>
// 使用 common.js 加载导航栏

// 切换FAQ显示/隐藏
function toggleFaq(element) {
const answer = element.nextElementSibling;
const icon = element.querySelector('i');
if (answer.classList.contains('active')) {
answer.classList.remove('active');
icon.classList.remove('fa-chevron-up');
icon.classList.add('fa-chevron-down');
} else {
answer.classList.add('active');
icon.classList.remove('fa-chevron-down');
icon.classList.add('fa-chevron-up');
}
}
// 筛选按钮点击事件
document.querySelectorAll('.filter-btn').forEach(btn => {
btn.addEventListener('click', function() {
document.querySelectorAll('.filter-btn').forEach(b => {
b.classList.remove('active');
});
this.classList.add('active');
});
});
// 加入采购单按钮点击事件
document.querySelectorAll('.product-actions .btn').forEach(btn => {
btn.addEventListener('click', function() {
const productName = this.closest('.product-item').querySelector('.product-title span').textContent;
alert(`已将 ${productName} 加入采购单！`);
});
});
// 移除采购单商品按钮点击事件
document.querySelectorAll('.list-group-item .btn-outline-danger').forEach(btn => {
btn.addEventListener('click', function() {
const item = this.closest('.list-group-item');
const productName = item.querySelector('.fw-bold').textContent;
if (confirm(`确定要移除 ${productName} 吗？`)) {
item.remove();
alert(`已移除 ${productName}！`);
}
});
});
// 立即结算按钮点击事件
document.querySelector('.btn-success').addEventListener('click', function() {
alert('正在跳转到结算页面，请稍后...');
});
// 金融方案计算按钮点击事件
document.querySelector('.btn-outline-primary').addEventListener('click', function() {
document.querySelector('.supplies-card:nth-child(2)').scrollIntoView({ behavior: 'smooth' });
});
// 计算方案按钮点击事件
document.querySelector('.btn-primary').addEventListener('click', function() {
alert('已更新金融方案计算结果！');
});
// 金融服务申请按钮点击事件
document.querySelectorAll('.finance-footer .btn-success').forEach(btn => {
btn.addEventListener('click', function() {
const financeTitle = this.closest('.finance-card').querySelector('.finance-title').textContent;
alert(`正在申请 ${financeTitle}，请稍后...`);
});
});
</script> </body> </html>