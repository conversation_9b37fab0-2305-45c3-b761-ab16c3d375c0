<!DOCTYPE html> <html lang="zh-CN"> <head> <meta charset="UTF-8"> <meta name="viewport" content="width=device-width, initial-scale=1.0"> <title>农事预警系统 - 智慧农业平台</title> <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet"> <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet"> <link rel="stylesheet" href="css/common.css"> <link rel="stylesheet" href="css/navbar.css"> <script src="https://cdn.jsdelivr.net/npm/chart.js"></script> <style>
:root {
--primary-color: #28a745;
--secondary-color: #6c757d;
--light-bg: #f8f9fa;
--card-shadow: 0 4px 6px rgba(0,0,0,0.1);
--transition: all 0.3s ease;
--warning-color: #ffc107;
--danger-color: #dc3545;
--info-color: #0dcaf0;
}
body {
background-color: var(--light-bg);
font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}
.alert-header {
background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80');
background-size: cover;
background-position: center;
height: 200px;
color: white;
display: flex;
align-items: center;
margin-bottom: 30px;
border-radius: 10px;
}
.alert-card {
background: white;
padding: 20px;
border-radius: 10px;
box-shadow: var(--card-shadow);
margin-bottom: 30px;
min-height: auto;
}
.stat-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 20px;
transition: var(--transition);
}
.stat-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.stat-icon {
font-size: 2rem;
margin-bottom: 10px;
}
.stat-icon.warning {
color: var(--warning-color);
}
.stat-icon.danger {
color: var(--danger-color);
}
.stat-icon.info {
color: var(--info-color);
}
.stat-icon.success {
color: var(--primary-color);
}
.stat-value {
font-size: 1.5rem;
font-weight: bold;
margin-bottom: 5px;
}
.stat-label {
font-size: 0.9rem;
color: var(--secondary-color);
}
.chart-container {
position: relative;
height: 300px;
width: 100%;
}
.map-container {
height: 400px;
border-radius: 10px;
overflow: hidden;
margin-bottom: 20px;
}
#map {
min-height: auto;
width: 100%;
}
.filter-btn {
padding: 5px 15px;
border-radius: 20px;
background-color: white;
border: 1px solid #ddd;
margin-right: 10px;
margin-bottom: 10px;
cursor: pointer;
transition: var(--transition);
}
.filter-btn.active {
background-color: var(--primary-color);
color: white;
border-color: var(--primary-color);
}
.filter-btn:hover:not(.active) {
background-color: #f0f0f0;
}
.alert-item {
padding: 15px;
border-radius: 10px;
margin-bottom: 15px;
transition: var(--transition);
display: flex;
align-items: flex-start;
background-color: white;
border-left: 4px solid transparent;
}
.alert-item:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.alert-item.warning {
border-left-color: var(--warning-color);
}
.alert-item.danger {
border-left-color: var(--danger-color);
}
.alert-item.info {
border-left-color: var(--info-color);
}
.alert-icon {
width: 50px;
height: 50px;
border-radius: 50%;
display: flex;
align-items: center;
justify-content: center;
margin-right: 15px;
font-size: 1.5rem;
}
.alert-icon.warning {
background-color: rgba(255, 193, 7, 0.1);
color: var(--warning-color);
}
.alert-icon.danger {
background-color: rgba(220, 53, 69, 0.1);
color: var(--danger-color);
}
.alert-icon.info {
background-color: rgba(13, 202, 240, 0.1);
color: var(--info-color);
}
.alert-content {
flex: 1;
}
.alert-title {
font-weight: bold;
margin-bottom: 5px;
display: flex;
justify-content: space-between;
align-items: center;
}
.alert-time {
font-size: 0.8rem;
color: var(--secondary-color);
}
.alert-desc {
margin-bottom: 10px;
color: var(--secondary-color);
}
.alert-location {
font-size: 0.9rem;
margin-bottom: 10px;
}
.alert-status {
display: inline-block;
padding: 3px 8px;
border-radius: 15px;
font-size: 0.8rem;
margin-bottom: 10px;
}
.status-pending {
background-color: rgba(255, 193, 7, 0.1);
color: var(--warning-color);
}
.status-processing {
background-color: rgba(13, 202, 240, 0.1);
color: var(--info-color);
}
.status-resolved {
background-color: rgba(40, 167, 69, 0.1);
color: var(--primary-color);
}
.alert-actions {
display: flex;
gap: 10px;
}
.alert-detail-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 15px;
transition: var(--transition);
}
.alert-detail-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.alert-detail-title {
font-weight: bold;
margin-bottom: 10px;
display: flex;
align-items: center;
}
.alert-detail-icon {
margin-right: 10px;
}
.alert-detail-icon.warning {
color: var(--warning-color);
}
.alert-detail-icon.danger {
color: var(--danger-color);
}
.alert-detail-icon.info {
color: var(--info-color);
}
.alert-detail-value {
font-size: 1.5rem;
font-weight: bold;
margin-bottom: 5px;
}
.alert-detail-desc {
font-size: 0.9rem;
color: var(--secondary-color);
}
.alert-detail-status {
display: inline-block;
padding: 3px 8px;
border-radius: 15px;
font-size: 0.8rem;
margin-left: 10px;
}
.recommendation-card {
background-color: rgba(40, 167, 69, 0.1);
border-left: 4px solid var(--primary-color);
padding: 15px;
margin-bottom: 15px;
border-radius: 0 5px 5px 0;
}
.recommendation-title {
font-weight: bold;
margin-bottom: 10px;
display: flex;
align-items: center;
}
.recommendation-icon {
color: var(--primary-color);
margin-right: 10px;
}
.recommendation-content {
color: var(--secondary-color);
}
.history-item {
display: flex;
margin-bottom: 15px;
padding-bottom: 15px;
border-bottom: 1px solid #eee;
}
.history-icon {
width: 40px;
height: 40px;
background-color: var(--light-bg);
border-radius: 50%;
display: flex;
align-items: center;
justify-content: center;
margin-right: 15px;
color: var(--primary-color);
}
.history-content {
flex: 1;
}
.history-title {
font-weight: bold;
margin-bottom: 5px;
}
.history-time {
font-size: 0.9rem;
color: var(--secondary-color);
margin-bottom: 5px;
}
.history-desc {
font-size: 0.9rem;
}
.settings-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 15px;
transition: var(--transition);
}
.settings-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.settings-title {
font-weight: bold;
margin-bottom: 15px;
}
.settings-item {
margin-bottom: 15px;
}
.settings-label {
font-weight: bold;
margin-bottom: 5px;
}
.settings-desc {
font-size: 0.9rem;
color: var(--secondary-color);
margin-bottom: 10px;
}
.notification-method {
display: flex;
align-items: center;
margin-bottom: 10px;
}
.notification-icon {
width: 40px;
height: 40px;
background-color: white;
border-radius: 50%;
display: flex;
align-items: center;
justify-content: center;
margin-right: 15px;
color: var(--primary-color);
font-size: 1.2rem;
}
.notification-info {
flex: 1;
}
.notification-name {
font-weight: bold;
margin-bottom: 5px;
}
.notification-desc {
font-size: 0.9rem;
color: var(--secondary-color);
}
.notification-toggle {
margin-left: 15px;
}
</style> </head> <body> <!-- 导航栏 -->
<div id="navbar-container"></div>
<!-- 主要内容 --> <div class="container py-5"> <div class="alert-header"> <div class="container"> <h1>农事预警系统</h1> <p class="lead">实时监控农田环境数据，智能预警潜在风险，提供及时处理建议，保障农作物健康生长</p> </div> </div> <!-- 顶部统计卡片 --> <div class="row mb-4"> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-exclamation-triangle stat-icon warning"></i> <div class="stat-value">5</div> <div class="stat-label">当前预警</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-exclamation-circle stat-icon danger"></i> <div class="stat-value">2</div> <div class="stat-label">高级预警</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-check-circle stat-icon success"></i> <div class="stat-value">12</div> <div class="stat-label">已解决预警</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-info-circle stat-icon info"></i> <div class="stat-value">3</div> <div class="stat-label">待处理任务</div> </div> </div> </div> <!-- 主要内容区域 --> <div class="row"> <!-- 左侧内容 --> <div class="col-md-8"> <!-- 预警地图 --> <div class="alert-card"> <div class="d-flex justify-content-between align-items-center mb-4"> <h4>预警分布地图</h4> <div> <button class="filter-btn active">全部</button> <button class="filter-btn">干旱预警</button> <button class="filter-btn">病虫害预警</button> <button class="filter-btn">气象预警</button> </div> </div> <div class="map-container"> <div id="map"></div> </div> </div> <!-- 预警列表 --> <div class="alert-card"> <div class="d-flex justify-content-between align-items-center mb-4"> <h4>当前预警</h4> <div> <button class="btn btn-sm btn-outline-success me-2">全部</button> <button class="btn btn-sm btn-success me-2">未处理</button> <button class="btn btn-sm btn-outline-success">已处理</button> </div> </div> <!-- 预警项目1 --> <div class="alert-item danger"> <div class="alert-icon danger"> <i class="fas fa-exclamation-circle"></i> </div> <div class="alert-content"> <div class="alert-title"> <span>土壤湿度严重偏低</span> <span class="alert-time">2023-06-20 08:30</span> </div> <div class="alert-desc">区域A的土壤湿度低于25%，达到严重干旱标准，可能导致作物死亡。</div> <div class="alert-location">位置：区域A - 水稻田</div> <div class="alert-status status-pending">未处理</div> <div class="alert-actions"> <button class="btn btn-sm btn-outline-success">查看详情</button> <button class="btn btn-sm btn-danger">立即处理</button> </div> </div> </div> <!-- 预警项目2 --> <div class="alert-item danger"> <div class="alert-icon danger"> <i class="fas fa-bug"></i> </div> <div class="alert-content"> <div class="alert-title"> <span>稻飞虫发生率超过25%</span> <span class="alert-time">2023-06-19 16:45</span> </div> <div class="alert-desc">区域B的稻飞虫发生率超过25%，达到高风险标准，需要立即处理。</div> <div class="alert-location">位置：区域B - 水稻田</div> <div class="alert-status status-processing">处理中</div> <div class="alert-actions"> <button class="btn btn-sm btn-outline-success">查看详情</button> <button class="btn btn-sm btn-info">查看进度</button> </div> </div> </div> <!-- 预警项目3 --> <div class="alert-item warning"> <div class="alert-icon warning"> <i class="fas fa-cloud-rain"></i> </div> <div class="alert-content"> <div class="alert-title"> <span>强降雨预警</span> <span class="alert-time">2023-06-18 10:15</span> </div> <div class="alert-desc">未来48小时内可能有强降雨，预计降雨量50-80mm，请做好防涟准备。</div> <div class="alert-location">位置：全部区域</div> <div class="alert-status status-pending">未处理</div> <div class="alert-actions"> <button class="btn btn-sm btn-outline-success">查看详情</button> <button class="btn btn-sm btn-warning">查看防涟方案</button> </div> </div> </div> <!-- 预警项目4 --> <div class="alert-item warning"> <div class="alert-icon warning"> <i class="fas fa-thermometer-three-quarters"></i> </div> <div class="alert-content"> <div class="alert-title"> <span>温度异常预警</span> <span class="alert-time">2023-06-17 14:30</span> </div> <div class="alert-desc">区域C的土壤温度达到32°C，超过作物适宜生长温度，可能影响作物生长。</div> <div class="alert-location">位置：区域C - 蚕豆田</div> <div class="alert-status status-pending">未处理</div> <div class="alert-actions"> <button class="btn btn-sm btn-outline-success">查看详情</button> <button class="btn btn-sm btn-warning">查看降温方案</button> </div> </div> </div> <!-- 预警项目5 --> <div class="alert-item info"> <div class="alert-icon info"> <i class="fas fa-leaf"></i> </div> <div class="alert-content"> <div class="alert-title"> <span>养分不足预警</span> <span class="alert-time">2023-06-16 09:20</span> </div> <div class="alert-desc">区域D的土壤氮含量偏低，可能影响作物生长，建议追施氮肥。</div> <div class="alert-location">位置：区域D - 小麦田</div> <div class="alert-status status-resolved">已处理</div> <div class="alert-actions"> <button class="btn btn-sm btn-outline-success">查看详情</button> <button class="btn btn-sm btn-success">查看处理结果</button> </div> </div> </div> <div class="text-center mt-4"> <button class="btn btn-outline-success">加载更多</button> </div> </div> <!-- 预警趋势图表 --> <div class="alert-card"> <div class="d-flex justify-content-between align-items-center mb-4"> <h4>预警趋势分析</h4> <div> <button class="btn btn-sm btn-outline-success me-2">日</button> <button class="btn btn-sm btn-success me-2">周</button> <button class="btn btn-sm btn-outline-success">月</button> </div> </div> <div class="chart-container"> <canvas id="alertChart"></canvas> </div> </div> </div> <!-- 右侧内容 --> <div class="col-md-4"> <!-- 预警详情 --> <div class="alert-card"> <h4 class="mb-4">预警详情</h4> <div class="alert-detail-card"> <div class="alert-detail-title"> <i class="fas fa-exclamation-circle alert-detail-icon danger"></i> <span>土壤湿度严重偏低</span> <span class="alert-detail-status status-pending">未处理</span> </div> <div class="alert-detail-value">25%</div> <div class="alert-detail-desc">当前土壤湿度，低于适宜范围（40% - 60%）</div> <div class="mt-3"> <div class="d-flex justify-content-between mb-2"> <span>预警时间：</span> <span>2023-06-20 08:30</span> </div> <div class="d-flex justify-content-between mb-2"> <span>预警级别：</span> <span class="text-danger">高级预警</span> </div> <div class="d-flex justify-content-between mb-2"> <span>受影响区域：</span> <span>区域A - 水稻田</span> </div> <div class="d-flex justify-content-between mb-2"> <span>受影响面积：</span> <span>5些</span> </div> <div class="d-flex justify-content-between mb-2"> <span>可能损失：</span> <span class="text-danger">严重</span> </div> </div> </div> <!-- 处理建议 --> <div class="recommendation-card"> <div class="recommendation-title"> <i class="fas fa-lightbulb recommendation-icon"></i> <span>处理建议</span> </div> <div class="recommendation-content"> <p>建议立即开启灵溪系统，对A区块进行灵溪，持续时间45分钟，直到土壤湿度达到40%以上。</p> <div class="d-grid gap-2 mt-3"> <button class="btn btn-success">立即开始灵溪</button> <button class="btn btn-outline-secondary">查看其他方案</button> </div> </div> </div> </div> <!-- 预警设置 --> <div class="alert-card"> <h4 class="mb-4">预警设置</h4> <div class="settings-card"> <div class="settings-title">预警阈值设置</div> <div class="settings-item"> <div class="settings-label">土壤湿度预警阈值</div> <div class="settings-desc">设置土壤湿度低于多少时触发预警</div> <div class="d-flex align-items-center"> <input type="range" class="form-range flex-grow-1 me-2" min="20" max="40" step="5" value="30" id="soilMoistureThreshold"> <span id="soilMoistureValue">30%</span> </div> </div> <div class="settings-item"> <div class="settings-label">病虫害发生率预警阈值</div> <div class="settings-desc">设置病虫害发生率高于多少时触发预警</div> <div class="d-flex align-items-center"> <input type="range" class="form-range flex-grow-1 me-2" min="10" max="30" step="5" value="20" id="pestThreshold"> <span id="pestValue">20%</span> </div> </div> <div class="settings-item"> <div class="settings-label">气象预警设置</div> <div class="settings-desc">选择需要接收的气象预警类型</div> <div class="form-check mb-2"> <input class="form-check-input" type="checkbox" id="rainAlert" checked> <label class="form-check-label" for="rainAlert">降雨预警</label> </div> <div class="form-check mb-2"> <input class="form-check-input" type="checkbox" id="tempAlert" checked> <label class="form-check-label" for="tempAlert">温度预警</label> </div> <div class="form-check"> <input class="form-check-input" type="checkbox" id="windAlert" checked> <label class="form-check-label" for="windAlert">强风预警</label> </div> </div> </div> <div class="settings-card"> <div class="settings-title">通知方式设置</div> <div class="notification-method"> <div class="notification-icon"> <i class="fas fa-mobile-alt"></i> </div> <div class="notification-info"> <div class="notification-name">短信通知</div> <div class="notification-desc">将预警信息发送到您的手机</div> </div> <div class="notification-toggle form-check form-switch"> <input class="form-check-input" type="checkbox" id="smsNotification" checked> </div> </div> <div class="notification-method"> <div class="notification-icon"> <i class="fas fa-envelope"></i> </div> <div class="notification-info"> <div class="notification-name">邮件通知</div> <div class="notification-desc">将预警信息发送到您的邮箱</div> </div> <div class="notification-toggle form-check form-switch"> <input class="form-check-input" type="checkbox" id="emailNotification" checked> </div> </div> <div class="notification-method"> <div class="notification-icon"> <i class="fas fa-bell"></i> </div> <div class="notification-info"> <div class="notification-name">应用推送</div> <div class="notification-desc">在应用内推送预警信息</div> </div> <div class="notification-toggle form-check form-switch"> <input class="form-check-input" type="checkbox" id="appNotification" checked> </div> </div> <div class="notification-method"> <div class="notification-icon"> <i class="fas fa-volume-up"></i> </div> <div class="notification-info"> <div class="notification-name">语音通知</div> <div class="notification-desc">通过语音电话通知紧急预警</div> </div> <div class="notification-toggle form-check form-switch"> <input class="form-check-input" type="checkbox" id="voiceNotification"> </div> </div> </div> <div class="text-center mt-3"> <button class="btn btn-success">保存设置</button> </div> </div> <!-- 处理历史 --> <div class="alert-card"> <h4 class="mb-4">处理历史</h4> <div class="history-item"> <div class="history-icon"> <i class="fas fa-check-circle"></i> </div> <div class="history-content"> <div class="history-title">养分不足预警处理</div> <div class="history-time">2023-06-16 10:30</div> <div class="history-desc">对区域D进行了氮肥追施，每些用量15kg。</div> </div> </div> <div class="history-item"> <div class="history-icon"> <i class="fas fa-check-circle"></i> </div> <div class="history-content"> <div class="history-title">病虫害预警处理</div> <div class="history-time">2023-06-15 14:45</div> <div class="history-desc">对区域E进行了生物农药喷洒，防治稻灰飞虫。</div> </div> </div> <div class="history-item"> <div class="history-icon"> <i class="fas fa-check-circle"></i> </div> <div class="history-content"> <div class="history-title">干旱预警处理</div> <div class="history-time">2023-06-12 09:15</div> <div class="history-desc">对区域F进行了灵溪，持续时间45分钟。</div> </div> </div> </div> </div> </div> </div> <!-- 页脚 -->
<div id="footer-container"></div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="js/login-config.js"></script>
<script src="js/components.js"></script>
<script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script> <script>
// 使用 common.js 加载导航栏

// 初始化地图
var map = L.map('map').setView([30.7741, 120.7551], 14);
L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
attribution: '© OpenStreetMap contributors'
}).addTo(map);
// 添加预警标记
const alerts = [
{ id: 1, name: "土壤湿度严重偏低", position: [30.7741, 120.7551], type: "drought", status: "danger", value: "25%" },
{ id: 2, name: "稻飞虫发生率超过25%", position: [30.7841, 120.7651], type: "pest", status: "danger", value: "25%" },
{ id: 3, name: "强降雨预警", position: [30.7641, 120.7451], type: "weather", status: "warning", value: "50-80mm" },
{ id: 4, name: "温度异常预警", position: [30.7541, 120.7351], type: "weather", status: "warning", value: "32°C" },
{ id: 5, name: "养分不足预警", position: [30.7941, 120.7751], type: "nutrient", status: "info", value: "氮含量偏低" }
];
alerts.forEach(alert => {
// 设置图标颜色
let color = '#28a745';
if (alert.status === 'danger') {
color = '#dc3545';
} else if (alert.status === 'warning') {
color = '#ffc107';
} else if (alert.status === 'info') {
color = '#0dcaf0';
}
// 创建圆形区域标记
const circle = L.circle(alert.position, {
color: color,
fillColor: color,
fillOpacity: 0.3,
radius: 300
}).addTo(map);
// 添加点击事件
circle.bindPopup(`
<div class="text-center"> <h6>${alert.name}</h6> <p class="mb-1">类型: ${alert.type}</p> <p class="mb-1">状态: ${alert.status}</p> <p class="mb-1">当前值: ${alert.value}</p> <button class="btn btn-sm btn-success mt-2">查看详情</button> </div>
`);
});
// 初始化预警趋势图表
var ctx = document.getElementById('alertChart').getContext('2d');
var alertChart = new Chart(ctx, {
type: 'line',
data: {
labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
datasets: [
{
label: '干旱预警',
data: [1, 2, 2, 3, 3, 2, 1],
borderColor: '#ffc107',
backgroundColor: 'rgba(255, 193, 7, 0.1)',
tension: 0.4,
fill: true
},
{
label: '病虫害预警',
data: [0, 1, 1, 2, 2, 2, 2],
borderColor: '#dc3545',
backgroundColor: 'rgba(220, 53, 69, 0.1)',
tension: 0.4,
fill: true
},
{
label: '气象预警',
data: [1, 1, 0, 1, 1, 2, 2],
borderColor: '#0dcaf0',
backgroundColor: 'rgba(13, 202, 240, 0.1)',
tension: 0.4,
fill: true
}
]
},
options: {
responsive: true,
maintainAspectRatio: false,
plugins: {
legend: {
position: 'top',
},
tooltip: {
mode: 'index',
intersect: false
}
},
scales: {
y: {
beginAtZero: true,
max: 5
}
}
}
});
// 筛选按钮点击事件
document.querySelectorAll('.filter-btn').forEach(btn => {
btn.addEventListener('click', function() {
document.querySelectorAll('.filter-btn').forEach(b => {
b.classList.remove('active');
});
this.classList.add('active');
});
});
// 立即处理按钮点击事件
document.querySelector('.alert-item .btn-danger').addEventListener('click', function() {
alert('已发送处理指令，系统将自动开始灵溪！');
});
// 立即开始灵溪按钮点击事件
document.querySelector('.recommendation-card .btn-success').addEventListener('click', function() {
alert('已开始灵溪，预计完成时间45分钟！');
});
// 预警阈值设置事件
document.getElementById('soilMoistureThreshold').addEventListener('input', function() {
document.getElementById('soilMoistureValue').textContent = this.value + '%';
});
document.getElementById('pestThreshold').addEventListener('input', function() {
document.getElementById('pestValue').textContent = this.value + '%';
});
// 保存设置按钮点击事件
document.querySelector('.alert-card .btn-success').addEventListener('click', function() {
alert('设置已保存！');
});
</script>