<!DOCTYPE html> <html lang="zh-CN"> <head> <meta charset="UTF-8"> <meta name="viewport" content="width=device-width, initial-scale=1.0"> <title>AR虚拟试用功能 - 智慧农业平台</title> <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet"> <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet"> <link rel="stylesheet" href="css/common.css"> <link rel="stylesheet" href="css/navbar.css"> <style>
:root {
--primary-color: #28a745;
--secondary-color: #6c757d;
--light-bg: #f8f9fa;
--card-shadow: 0 4px 6px rgba(0,0,0,0.1);
--transition: all 0.3s ease;
--ar-color: #9b59b6;
}
body {
background-color: var(--light-bg);
font-family: 'PingFang SC', '<PERSON> YaHei', sans-serif;
}
.ar-header {
background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('https://images.unsplash.com/photo-1592982537447-7440770cbfc9?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80');
background-size: cover;
background-position: center;
height: 300px;
color: white;
display: flex;
align-items: center;
margin-bottom: 30px;
border-radius: 10px;
}
.ar-card {
background: white;
padding: 20px;
border-radius: 10px;
box-shadow: var(--card-shadow);
margin-bottom: 30px;
min-height: auto;
}
.stat-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 20px;
transition: var(--transition);
}
.stat-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.stat-icon {
font-size: 2rem;
color: var(--ar-color);
margin-bottom: 10px;
}
.stat-value {
font-size: 1.5rem;
font-weight: bold;
color: var(--ar-color);
margin-bottom: 5px;
}
.stat-label {
font-size: 0.9rem;
color: var(--secondary-color);
}
.filter-btn {
padding: 5px 15px;
border-radius: 20px;
background-color: white;
border: 1px solid #ddd;
margin-right: 10px;
margin-bottom: 10px;
cursor: pointer;
transition: var(--transition);
}
.filter-btn.active {
background-color: var(--primary-color);
color: white;
border-color: var(--primary-color);
}
.filter-btn:hover:not(.active) {
background-color: #f0f0f0;
}
.product-item {
background: var(--light-bg);
border-radius: 10px;
overflow: hidden;
margin-bottom: 20px;
transition: var(--transition);
min-height: auto;
}
.product-item:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.product-img {
width: 100%;
height: 200px;
object-fit: cover;
position: relative;
}
.ar-badge {
position: absolute;
top: 10px;
right: 10px;
background-color: var(--ar-color);
color: white;
padding: 5px 10px;
border-radius: 20px;
font-size: 0.8rem;
display: flex;
align-items: center;
}
.ar-badge i {
margin-right: 5px;
}
.product-info {
padding: 15px;
}
.product-title {
font-weight: bold;
font-size: 1.1rem;
margin-bottom: 5px;
display: flex;
justify-content: space-between;
align-items: center;
}
.product-badge {
display: inline-block;
padding: 2px 8px;
border-radius: 15px;
font-size: 0.7rem;
background-color: rgba(155, 89, 182, 0.1);
color: var(--ar-color);
}
.product-desc {
color: var(--secondary-color);
margin-bottom: 10px;
font-size: 0.9rem;
height: 40px;
overflow: hidden;
}
.product-meta {
display: flex;
justify-content: space-between;
margin-bottom: 10px;
font-size: 0.9rem;
}
.product-price {
font-weight: bold;
color: var(--ar-color);
}
.product-category {
color: var(--secondary-color);
display: flex;
align-items: center;
}
.product-category i {
margin-right: 5px;
}
.product-features {
display: flex;
flex-wrap: wrap;
gap: 5px;
margin-bottom: 10px;
}
.product-feature {
display: inline-block;
padding: 2px 8px;
border-radius: 15px;
font-size: 0.7rem;
background-color: #f0f0f0;
color: var(--secondary-color);
}
.product-actions {
display: flex;
gap: 10px;
}
.feature-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 15px;
transition: var(--transition);
min-height: auto;
}
.feature-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.feature-header {
display: flex;
align-items: center;
margin-bottom: 15px;
}
.feature-icon {
width: 50px;
height: 50px;
border-radius: 10px;
background-color: var(--ar-color);
color: white;
display: flex;
align-items: center;
justify-content: center;
font-size: 1.5rem;
margin-right: 15px;
flex-shrink: 0;
}
.feature-title {
font-weight: bold;
font-size: 1.1rem;
margin-bottom: 5px;
}
.feature-subtitle {
font-size: 0.9rem;
color: var(--secondary-color);
}
.feature-body {
margin-bottom: 15px;
}
.feature-description {
color: var(--secondary-color);
margin-bottom: 15px;
}
.step-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 15px;
transition: var(--transition);
position: relative;
}
.step-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.step-header {
display: flex;
align-items: center;
margin-bottom: 15px;
}
.step-number {
width: 40px;
height: 40px;
border-radius: 50%;
background-color: var(--ar-color);
color: white;
display: flex;
align-items: center;
justify-content: center;
font-weight: bold;
margin-right: 15px;
flex-shrink: 0;
}
.step-title {
font-weight: bold;
font-size: 1.1rem;
}
.step-body {
margin-bottom: 15px;
}
.step-description {
color: var(--secondary-color);
margin-bottom: 15px;
}
.step-connector {
position: absolute;
left: 20px;
top: 55px;
width: 2px;
height: calc(100% - 40px);
background-color: var(--ar-color);
z-index: 0;
}
.faq-item {
margin-bottom: 15px;
}
.faq-question {
font-weight: bold;
margin-bottom: 5px;
cursor: pointer;
display: flex;
justify-content: space-between;
align-items: center;
}
.faq-answer {
color: var(--secondary-color);
display: none;
padding: 10px 0;
}
.faq-answer.active {
display: block;
}
.review-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 15px;
transition: var(--transition);
}
.review-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.review-header {
display: flex;
align-items: center;
margin-bottom: 10px;
}
.review-avatar {
width: 40px;
height: 40px;
border-radius: 50%;
object-fit: cover;
margin-right: 10px;
}
.review-user {
flex: 1;
}
.review-name {
font-weight: bold;
margin-bottom: 0;
}
.review-date {
font-size: 0.8rem;
color: var(--secondary-color);
}
.review-rating {
color: #f1c40f;
}
.review-content {
margin-bottom: 10px;
}
.review-product {
font-size: 0.8rem;
color: var(--secondary-color);
font-style: italic;
}
.ar-preview-container {
position: relative;
height: 400px;
width: 100%;
border-radius: 10px;
overflow: hidden;
margin-bottom: 20px;
background-color: #000;
}
.ar-preview-img {
width: 100%;
min-height: auto;
object-fit: cover;
opacity: 0.8;
}
.ar-preview-overlay {
position: absolute;
top: 0;
left: 0;
right: 0;
bottom: 0;
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
color: white;
text-align: center;
padding: 20px;
}
.ar-preview-title {
font-size: 1.5rem;
font-weight: bold;
margin-bottom: 10px;
}
.ar-preview-desc {
font-size: 1rem;
margin-bottom: 20px;
max-width: 600px;
}
.ar-controls {
display: flex;
gap: 10px;
margin-top: 20px;
}
.ar-control-btn {
width: 50px;
height: 50px;
border-radius: 50%;
background-color: rgba(255, 255, 255, 0.2);
color: white;
display: flex;
align-items: center;
justify-content: center;
font-size: 1.2rem;
cursor: pointer;
transition: var(--transition);
}
.ar-control-btn:hover {
background-color: rgba(255, 255, 255, 0.4);
}
.device-compatibility {
display: flex;
gap: 15px;
margin-top: 20px;
justify-content: center;
}
.device-item {
display: flex;
flex-direction: column;
align-items: center;
color: white;
}
.device-icon {
font-size: 1.5rem;
margin-bottom: 5px;
}
.device-name {
font-size: 0.8rem;
}
.chart-container {
position: relative;
height: 300px;
width: 100%;
margin-bottom: 20px;
}
.nav-tabs .nav-link {
color: var(--secondary-color);
}
.nav-tabs .nav-link.active {
color: var(--ar-color);
font-weight: bold;
}
.tab-content {
padding: 20px 0;
}
</style> </head> <body> <!-- 导航栏 -->
<div id="navbar-container"></div>
<!-- 主要内容 --> <div class="container py-5"> <div class="ar-header"> <div class="container"> <h1>AR虚拟试用功能</h1> <p class="lead">通过增强现实技术，在购买前虚拟体验农机设备和农资产品，直观了解产品特性和使用效果，提高购买决策准确性</p> <div class="mt-4"> <button class="btn btn-success btn-lg me-2"><i class="fas fa-vr-cardboard me-2"></i>立即体验AR</button> <button class="btn btn-outline-light btn-lg"><i class="fas fa-download me-2"></i>下载AR应用</button> </div> </div> </div> <!-- 顶部统计卡片 --> <div class="row mb-4"> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-vr-cardboard stat-icon"></i> <div class="stat-value">3,560</div> <div class="stat-label">AR试用次数</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-mobile-alt stat-icon"></i> <div class="stat-value">12,800</div> <div class="stat-label">APP下载量</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-tractor stat-icon"></i> <div class="stat-value">256</div> <div class="stat-label">AR产品数量</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-percentage stat-icon"></i> <div class="stat-value">35%</div> <div class="stat-label">转化率提升</div> </div> </div> </div> <!-- 主要内容区域 --> <div class="row"> <!-- 左侧内容 --> <div class="col-md-8"> <!-- AR虚拟试用演示 --> <div class="ar-card mb-4"> <h4 class="mb-4">AR虚拟试用演示</h4> <div class="ar-preview-container"> <img src="images/ar-preview.jpg" alt="AR虚拟试用" class="ar-preview-img"> <div class="ar-preview-overlay"> <div class="ar-preview-title">智能无人驿驴机 AR虚拟试用</div> <div class="ar-preview-desc">通过AR技术，在您的环境中虚拟放置和操作设备，直观了解设备尺寸、功能和操作方式</div> <button class="btn btn-success btn-lg"><i class="fas fa-play me-2"></i>开始虚拟试用</button> <div class="ar-controls"> <div class="ar-control-btn"><i class="fas fa-sync-alt"></i></div> <div class="ar-control-btn"><i class="fas fa-search-plus"></i></div> <div class="ar-control-btn"><i class="fas fa-search-minus"></i></div> <div class="ar-control-btn"><i class="fas fa-arrows-alt"></i></div> <div class="ar-control-btn"><i class="fas fa-cog"></i></div> </div> <div class="device-compatibility"> <div class="device-item"> <div class="device-icon"><i class="fas fa-mobile-alt"></i></div> <div class="device-name">iOS 11+</div> </div> <div class="device-item"> <div class="device-icon"><i class="fab fa-android"></i></div> <div class="device-name">Android 8.0+</div> </div> <div class="device-item"> <div class="device-icon"><i class="fas fa-tablet-alt"></i></div> <div class="device-name">iPad</div> </div> <div class="device-item"> <div class="device-icon"><i class="fas fa-vr-cardboard"></i></div> <div class="device-name">AR眼镜</div> </div> </div> </div> </div> </div> <!-- AR产品列表 --> <div class="ar-card mb-4"> <div class="d-flex justify-content-between align-items-center mb-4"> <h4>AR虚拟试用产品</h4> <div class="input-group" style="width: 250px;"> <input type="text" class="form-control" placeholder="搜索产品"> <button class="btn btn-outline-secondary"><i class="fas fa-search"></i></button> </div> </div> <div class="mb-3"> <button class="filter-btn active">全部产品</button> <button class="filter-btn">农机设备</button> <button class="filter-btn">农资产品</button> <button class="filter-btn">智能设备</button> <button class="filter-btn">新品上市</button> </div> <div class="row"> <div class="col-md-6 mb-4"> <div class="product-item"> <div class="position-relative"> <img src="images/ar-product1.jpg" alt="智能无人驿驴机" class="product-img"> <div class="ar-badge"> <i class="fas fa-vr-cardboard"></i> <span>AR可用</span> </div> </div> <div class="product-info"> <div class="product-title"> <span>智能无人驿驴机</span> <span class="product-badge">热门</span> </div> <div class="product-desc">智能无人驿驴机，支持GPS定位和自动导航，适用于各类田间作业</div> <div class="product-meta"> <div class="product-price">24,500元</div> <div class="product-category"><i class="fas fa-tractor"></i> 农机设备</div> </div> <div class="product-features"> <span class="product-feature"><i class="fas fa-wifi"></i> 远程控制</span> <span class="product-feature"><i class="fas fa-battery-full"></i> 电池续舨12小时</span> <span class="product-feature"><i class="fas fa-map"></i> GPS定位</span> </div> <div class="product-actions"> <button class="btn btn-primary w-100"><i class="fas fa-vr-cardboard me-2"></i>AR虚拟试用</button> </div> </div> </div> </div> <div class="col-md-6 mb-4"> <div class="product-item"> <div class="position-relative"> <img src="images/ar-product2.jpg" alt="智能播种机" class="product-img"> <div class="ar-badge"> <i class="fas fa-vr-cardboard"></i> <span>AR可用</span> </div> </div> <div class="product-info"> <div class="product-title"> <span>智能播种机</span> <span class="product-badge">新品</span> </div> <div class="product-desc">高精度智能播种机，支持变量播种和精准定位，提高播种效率</div> <div class="product-meta"> <div class="product-price">16,800元</div> <div class="product-category"><i class="fas fa-tractor"></i> 农机设备</div> </div> <div class="product-features"> <span class="product-feature"><i class="fas fa-seedling"></i> 变量播种</span> <span class="product-feature"><i class="fas fa-tachometer-alt"></i> 高效率</span> <span class="product-feature"><i class="fas fa-cog"></i> 智能控制</span> </div> <div class="product-actions"> <button class="btn btn-primary w-100"><i class="fas fa-vr-cardboard me-2"></i>AR虚拟试用</button> </div> </div> </div> </div> <div class="col-md-6 mb-4"> <div class="product-item"> <div class="position-relative"> <img src="images/ar-product3.jpg" alt="有机复合肥" class="product-img"> <div class="ar-badge"> <i class="fas fa-vr-cardboard"></i> <span>AR可用</span> </div> </div> <div class="product-info"> <div class="product-title"> <span>有机复合肥</span> <span class="product-badge">推荐</span> </div> <div class="product-desc">高效有机复合肥，富含氮磷钾等多种元素，促进作物生长</div> <div class="product-meta"> <div class="product-price">198元/袋</div> <div class="product-category"><i class="fas fa-leaf"></i> 农资产品</div> </div> <div class="product-features"> <span class="product-feature"><i class="fas fa-leaf"></i> 有机肥料</span> <span class="product-feature"><i class="fas fa-recycle"></i> 绿色环保</span> <span class="product-feature"><i class="fas fa-seedling"></i> 高效养分</span> </div> <div class="product-actions"> <button class="btn btn-primary w-100"><i class="fas fa-vr-cardboard me-2"></i>AR虚拟试用</button> </div> </div> </div> </div> <div class="col-md-6 mb-4"> <div class="product-item"> <div class="position-relative"> <img src="images/ar-product4.jpg" alt="智能土壤监测仪" class="product-img"> <div class="ar-badge"> <i class="fas fa-vr-cardboard"></i> <span>AR可用</span> </div> </div> <div class="product-info"> <div class="product-title"> <span>智能土壤监测仪</span> <span class="product-badge">新品</span> </div> <div class="product-desc">智能土壤监测仪，实时监测土壤湿度、养分、pH值等数据，支持手机查看</div> <div class="product-meta"> <div class="product-price">1,280元</div> <div class="product-category"><i class="fas fa-microchip"></i> 智能设备</div> </div> <div class="product-features"> <span class="product-feature"><i class="fas fa-wifi"></i> 无线连接</span> <span class="product-feature"><i class="fas fa-mobile-alt"></i> APP控制</span> <span class="product-feature"><i class="fas fa-chart-line"></i> 数据分析</span> </div> <div class="product-actions"> <button class="btn btn-primary w-100"><i class="fas fa-vr-cardboard me-2"></i>AR虚拟试用</button> </div> </div> </div> </div> </div> <div class="text-center mt-3"> <button class="btn btn-outline-primary">查看更多产品</button> </div> </div> <!-- AR功能介绍 --> <div class="ar-card mb-4"> <h4 class="mb-4">AR虚拟试用功能</h4> <div class="row"> <div class="col-md-6 mb-4"> <div class="feature-card"> <div class="feature-header"> <div class="feature-icon"> <i class="fas fa-cube"></i> </div> <div> <div class="feature-title">3D模型展示</div> <div class="feature-subtitle">真实还原产品外观</div> </div> </div> <div class="feature-body"> <div class="feature-description">
通过高精度的3D模型，真实还原产品的外观、尺寸和细节。您可以从任意角度查看产品，放大细节，了解产品的外观设计和物理尺寸。
</div> </div> </div> </div> <div class="col-md-6 mb-4"> <div class="feature-card"> <div class="feature-header"> <div class="feature-icon"> <i class="fas fa-vr-cardboard"></i> </div> <div> <div class="feature-title">虚拟放置</div> <div class="feature-subtitle">在真实环境中放置产品</div> </div> </div> <div class="feature-body"> <div class="feature-description">
将产品虚拟放置在您的真实环境中，直观感受产品在实际空间中的大小和占用空间。这对于大型农机设备尤为重要，帮助您判断设备是否适合您的作业环境。
</div> </div> </div> </div> <div class="col-md-6 mb-4"> <div class="feature-card"> <div class="feature-header"> <div class="feature-icon"> <i class="fas fa-cogs"></i> </div> <div> <div class="feature-title">功能模拟</div> <div class="feature-subtitle">虚拟体验产品功能</div> </div> </div> <div class="feature-body"> <div class="feature-description">
虚拟体验产品的各项功能和操作方式。对于农机设备，您可以模拟操作设备的各个功能模块；对于农资产品，您可以虚拟查看产品的使用效果和应用场景。
</div> </div> </div> </div> <div class="col-md-6 mb-4"> <div class="feature-card"> <div class="feature-header"> <div class="feature-icon"> <i class="fas fa-chart-bar"></i> </div> <div> <div class="feature-title">数据可视化</div> <div class="feature-subtitle">直观展示产品数据</div> </div> </div> <div class="feature-body"> <div class="feature-description">
通过AR增强现实技术，直观展示产品的性能数据、技术参数和使用效果。例如，对于农药和肥料，可以虚拟展示其在作物生长周期中的效果和数据变化。
</div> </div> </div> </div> </div> </div> <!-- AR使用步骤 --> <div class="ar-card mb-4"> <h4 class="mb-4">AR虚拟试用步骤</h4> <div class="step-card"> <div class="step-header"> <div class="step-number">1</div> <div class="step-title">下载AR应用</div> </div> <div class="step-body"> <div class="step-description">
从应用商店下载“智慧农业AR”应用，支持iOS和Android系统。您也可以扫描网站上的二维码直接下载安装。
</div> </div> <div class="step-connector"></div> </div> <div class="step-card"> <div class="step-header"> <div class="step-number">2</div> <div class="step-title">选择产品</div> </div> <div class="step-body"> <div class="step-description">
打开应用后，浏览或搜索您感兴趣的产品。所有支持AR虚拟试用的产品都标有AR标识。点击产品进入详情页面。
</div> </div> <div class="step-connector"></div> </div> <div class="step-card"> <div class="step-header"> <div class="step-number">3</div> <div class="step-title">启动AR模式</div> </div> <div class="step-body"> <div class="step-description">
在产品详情页面中，点击“AR虚拟试用”按钮。系统将请求摄像头权限，请允许以使用AR功能。
</div> </div> <div class="step-connector"></div> </div> <div class="step-card"> <div class="step-header"> <div class="step-number">4</div> <div class="step-title">扫描环境</div> </div> <div class="step-body"> <div class="step-description">
按照屏幕提示，缓慢移动手机扫描您的环境。这将帮助AR系统识别平面和空间，以便准确放置虚拟产品。
</div> </div> <div class="step-connector"></div> </div> <div class="step-card"> <div class="step-header"> <div class="step-number">5</div> <div class="step-title">交互体验</div> </div> <div class="step-body"> <div class="step-description">
产品虚拟模型放置在您的环境中后，您可以通过屏幕上的控制按钮进行交互。可以旋转、缩放、移动产品，查看产品细节，模拟产品功能等。
</div> </div> </div> </div> </div> <!-- 右侧内容 --> <div class="col-md-4"> <!-- 下载AR应用 --> <div class="ar-card"> <h4 class="mb-4">下载AR应用</h4> <div class="text-center mb-4"> <img src="images/ar-app-qrcode.jpg" alt="AR应用二维码" style="width: 200px; height: 200px;"> </div> <div class="d-grid gap-3"> <a href="#" class="btn btn-primary"><i class="fab fa-apple me-2"></i>App Store下载</a> <a href="#" class="btn btn-success"><i class="fab fa-android me-2"></i>Google Play下载</a> <a href="#" class="btn btn-secondary"><i class="fab fa-weixin me-2"></i>微信小程序版本</a> </div> <div class="alert alert-info mt-3"> <i class="fas fa-info-circle me-2"></i>
扫描二维码或点击上方按钮下载应用，体验AR虚拟试用功能。
</div> </div> <!-- 用户评价 --> <div class="ar-card mt-4"> <div class="d-flex justify-content-between align-items-center mb-4"> <h4>用户评价</h4> <div> <span class="me-2">总评分：</span> <span class="review-rating"> <i class="fas fa-star"></i> <i class="fas fa-star"></i> <i class="fas fa-star"></i> <i class="fas fa-star"></i> <i class="fas fa-star-half-alt"></i> <span class="ms-1">4.6</span> </span> </div> </div> <div class="review-card"> <div class="review-header"> <img src="images/user1.jpg" alt="用户头像" class="review-avatar"> <div class="review-user"> <h6 class="review-name">王农民</h6> <div class="review-date">2023-06-15</div> </div> <div class="review-rating"> <i class="fas fa-star"></i> <i class="fas fa-star"></i> <i class="fas fa-star"></i> <i class="fas fa-star"></i> <i class="fas fa-star"></i> </div> </div> <div class="review-content">
AR虚拟试用功能太棒了！我在购买无人驿驴机前使用了这个功能，可以直观地看到设备在我的农田中的大小和占用空间，帮我确认了设备是否适合我的田地。这比光看参数和图片直观多了！
</div> <div class="review-product">试用产品：智能无人驿驴机</div> </div> <div class="review-card"> <div class="review-header"> <img src="images/user2.jpg" alt="用户头像" class="review-avatar"> <div class="review-user"> <h6 class="review-name">李农民</h6> <div class="review-date">2023-06-10</div> </div> <div class="review-rating"> <i class="fas fa-star"></i> <i class="fas fa-star"></i> <i class="fas fa-star"></i> <i class="fas fa-star"></i> <i class="fas fa-star-half-alt"></i> </div> </div> <div class="review-content">
使用AR功能试用了有机复合肥的效果，可以直观地看到不同时期作物的生长情况对比，非常直观。不过AR应用有时候会卡顿，希望能优化一下性能。总的来说还是很实用的功能，帮我做出了更好的采购决策。
</div> <div class="review-product">试用产品：有机复合肥</div> </div> <div class="review-card"> <div class="review-header"> <img src="images/user3.jpg" alt="用户头像" class="review-avatar"> <div class="review-user"> <h6 class="review-name">张农民</h6> <div class="review-date">2023-06-05</div> </div> <div class="review-rating"> <i class="fas fa-star"></i> <i class="fas fa-star"></i> <i class="fas fa-star"></i> <i class="fas fa-star"></i> <i class="fas fa-star"></i> </div> </div> <div class="review-content">
智能土壤监测仪的AR试用功能很棒，可以直观地看到设备如何工作，以及数据如何在手机APP上显示。这种虚拟体验让我在购买前就对产品有了清晰的认识，非常有帮助。强烈推荐所有农民在购买前先使用AR功能试用一下！
</div> <div class="review-product">试用产品：智能土壤监测仪</div> </div> </div> <!-- 常见问题 --> <div class="ar-card mt-4"> <h4 class="mb-4">常见问题</h4> <div class="faq-item"> <div class="faq-question" onclick="toggleFaq(this)"> <span>哪些设备支持AR虚拟试用？</span> <i class="fas fa-chevron-down"></i> </div> <div class="faq-answer">
我们的AR虚拟试用功能支持大多数现代智能手机和平板设备。对于iOS设备，需要iPhone 6s及以上型号，运行iOS 11及以上系统；对于Android设备，需要支持ARCore的手机，一般是运行Android 8.0及以上系统的中高端手机。此外，我们还支持各种AR眼镜和VR设备，如果您使用特定设备有疑问，请联系我们的客服团队。
</div> </div> <div class="faq-item"> <div class="faq-question" onclick="toggleFaq(this)"> <span>AR虚拟试用需要网络连接吗？</span> <i class="fas fa-chevron-down"></i> </div> <div class="faq-answer">
是的，首次使用AR虚拟试用功能时需要网络连接，因为应用需要下载产品的3D模型和相关数据。下载后，部分产品的AR模型将缓存在您的设备上，可以在离线状态下使用。但是，一些高级功能，如实时数据可视化和产品效果模拟，可能仍然需要网络连接。我们建议在使用AR功能时连接到Wi-Fi网络，以获得最佳体验。
</div> </div> <div class="faq-item"> <div class="faq-question" onclick="toggleFaq(this)"> <span>AR虚拟试用的准确度如何？</span> <i class="fas fa-chevron-down"></i> </div> <div class="faq-answer">
我们的AR虚拟试用功能采用高精度的3D模型和先进的AR技术，可以非常准确地还原产品的外观、尺寸和比例。对于农机设备，尺寸精度可达到实际尺寸的98%以上。对于农资产品的效果模拟，我们基于大量实验数据和用户反馈进行建模，可以相对准确地反映产品在不同条件下的使用效果。不过，请注意，虚拟效果仅供参考，实际效果可能因地域、土壤、气候等因素而有所差异。
</div> </div> <div class="faq-item"> <div class="faq-question" onclick="toggleFaq(this)"> <span>如何获得最佳的AR体验？</span> <i class="fas fa-chevron-down"></i> </div> <div class="faq-answer">
要获得最佳的AR体验，请注意以下几点：1) 在光线充足的环境中使用AR功能，避免强光和过暗的环境；2) 确保您的手机摄像头清洁，不要遮挡摄像头；3) 扫描环境时，缓慢移动手机，让系统有足够的时间识别环境；4) 选择有足够空间的平面区域放置虚拟产品，尤其是大型农机设备；5) 确保您的设备电量充足，因为AR功能比较耗电。如果您遇到任何问题，可以在应用中查看帮助指南或联系我们的客服团队。
</div> </div> <div class="faq-item"> <div class="faq-question" onclick="toggleFaq(this)"> <span>我可以在AR中比较不同产品吗？</span> <i class="fas fa-chevron-down"></i> </div> <div class="faq-answer">
是的，我们的AR应用支持产品比较功能。您可以同时在AR环境中放置最多三个产品，直观地比较它们的尺寸、外观和功能特点。对于农资产品，您还可以比较不同产品在作物生长周期中的效果差异。这一功能在应用的“产品比较”模块中可以找到，或者在产品详情页面中点击“添加到比较”按钮。
</div> </div> </div> <!-- 设备兼容性 --> <div class="ar-card mt-4"> <h4 class="mb-4">设备兼容性</h4> <div class="table-responsive"> <table class="table table-bordered"> <thead class="table-light"> <tr> <th>平台</th> <th>最低要求</th> <th>推荐配置</th> </tr> </thead> <tbody> <tr> <td><i class="fab fa-apple me-2"></i>iOS</td> <td>iPhone 6s及以上<br>iOS 11+</td> <td>iPhone X及以上<br>iOS 13+</td> </tr> <tr> <td><i class="fab fa-android me-2"></i>Android</td> <td>支持ARCore的设备<br>Android 8.0+</td> <td>高端机型<br>Android 10.0+</td> </tr> <tr> <td><i class="fas fa-tablet-alt me-2"></i>平板</td> <td>iPad (5代)及以上<br>支持ARCore的Android平板</td> <td>iPad Pro<br>高端 Android平板</td> </tr> <tr> <td><i class="fas fa-vr-cardboard me-2"></i>AR设备</td> <td>Microsoft HoloLens<br>Magic Leap One</td> <td>HoloLens 2<br>Magic Leap 2</td> </tr> </tbody> </table> </div> <div class="alert alert-warning mt-3"> <i class="fas fa-exclamation-triangle me-2"></i>
注意：不同设备的AR体验效果可能有所差异。如果您的设备不在上述列表中，请联系我们的客服团队。
</div> </div> </div> </div> </div> <!-- 页脚 -->
<div id="footer-container"></div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="js/login-config.js"></script>
<script src="js/components.js"></script>
<script>
// 使用 common.js 加载导航栏

// 切换FAQ显示/隐藏
function toggleFaq(element) {
const answer = element.nextElementSibling;
const icon = element.querySelector('i');
if (answer.classList.contains('active')) {
answer.classList.remove('active');
icon.classList.remove('fa-chevron-up');
icon.classList.add('fa-chevron-down');
} else {
answer.classList.add('active');
icon.classList.remove('fa-chevron-down');
icon.classList.add('fa-chevron-up');
}
}
// 筛选按钮点击事件
document.querySelectorAll('.filter-btn').forEach(btn => {
btn.addEventListener('click', function() {
document.querySelectorAll('.filter-btn').forEach(b => {
b.classList.remove('active');
});
this.classList.add('active');
});
});
// AR虚拟试用按钮点击事件
document.querySelectorAll('.product-actions .btn').forEach(btn => {
btn.addEventListener('click', function() {
const productName = this.closest('.product-item').querySelector('.product-title span').textContent;
alert(`正在启动 ${productName} 的AR虚拟试用，请允许摄像头权限。`);
});
});
// 开始虚拟试用按钮点击事件
document.querySelector('.ar-preview-overlay .btn-success').addEventListener('click', function() {
alert('正在启动AR虚拟试用，请允许摄像头权限。');
});
// AR控制按钮点击事件
document.querySelectorAll('.ar-control-btn').forEach(btn => {
btn.addEventListener('click', function() {
const icon = this.querySelector('i').className;
let action = '';
if (icon.includes('sync-alt')) action = '旋转';
else if (icon.includes('search-plus')) action = '放大';
else if (icon.includes('search-minus')) action = '缩小';
else if (icon.includes('arrows-alt')) action = '移动';
else if (icon.includes('cog')) action = '设置';
alert(`执行${action}操作`);
});
});
// 立即体验AR按钮点击事件
document.querySelector('.btn-success.btn-lg').addEventListener('click', function() {
document.querySelector('.ar-preview-container').scrollIntoView({ behavior: 'smooth' });
});
// 下载AR应用按钮点击事件
document.querySelector('.btn-outline-light.btn-lg').addEventListener('click', function() {
document.querySelector('.ar-card:nth-child(1)').scrollIntoView({ behavior: 'smooth' });
});
// 查看更多产品按钮点击事件
document.querySelector('.btn-outline-primary').addEventListener('click', function() {
alert('正在加载更多产品，请稍后...');
});

// 确保导航栏在页面加载完成后可见
window.onload = function() {
    // 确保导航栏可见
    const navbarContainer = document.getElementById('navbar-container');
    if (navbarContainer) {
        navbarContainer.style.display = '';
    }
};
</script> </body> </html>