<!DOCTYPE html> <html lang="zh-CN"> <head> <meta charset="UTF-8"> <meta name="viewport" content="width=device-width, initial-scale=1.0"> <title>关键节点区块链存证 - 智慧农业平台</title> <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet"> <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet"> <link rel="stylesheet" href="css/common.css"> <link rel="stylesheet" href="css/navbar.css"> <style>
:root {
--primary-color: #28a745;
--secondary-color: #6c757d;
--light-bg: #f8f9fa;
--card-shadow: 0 4px 6px rgba(0,0,0,0.1);
--transition: all 0.3s ease;
--blockchain-color: #6610f2;
}
body {
background-color: var(--light-bg);
font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}
.blockchain-header {
background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80');
background-size: cover;
background-position: center;
height: 200px;
color: white;
display: flex;
align-items: center;
margin-bottom: 30px;
border-radius: 10px;
}
.blockchain-card {
background: white;
padding: 20px;
border-radius: 10px;
box-shadow: var(--card-shadow);
margin-bottom: 30px;
min-height: auto;
}
.stat-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 20px;
transition: var(--transition);
}
.stat-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.stat-icon {
font-size: 2rem;
color: var(--blockchain-color);
margin-bottom: 10px;
}
.stat-value {
font-size: 2rem;
font-weight: bold;
color: var(--blockchain-color);
margin-bottom: 5px;
}
.stat-label {
font-size: 0.9rem;
color: var(--secondary-color);
}
.proof-item {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 15px;
transition: var(--transition);
border-left: 4px solid var(--blockchain-color);
}
.proof-item:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.proof-header {
display: flex;
justify-content: space-between;
align-items: center;
margin-bottom: 10px;
}
.proof-title {
font-weight: bold;
display: flex;
align-items: center;
}
.proof-icon {
margin-right: 10px;
font-size: 1.2rem;
color: var(--blockchain-color);
}
.proof-time {
font-size: 0.9rem;
color: var(--secondary-color);
}
.proof-content {
margin-bottom: 10px;
color: var(--secondary-color);
}
.proof-hash {
font-family: monospace;
background-color: #f0f0f0;
padding: 5px 10px;
border-radius: 5px;
font-size: 0.9rem;
word-break: break-all;
margin-bottom: 10px;
}
.proof-actions {
display: flex;
gap: 10px;
}
.filter-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 20px;
}
.filter-title {
font-weight: bold;
margin-bottom: 10px;
}
.filter-options {
display: flex;
flex-wrap: wrap;
gap: 10px;
margin-bottom: 10px;
}
.filter-option {
display: inline-block;
padding: 5px 10px;
border-radius: 20px;
font-size: 0.9rem;
background-color: white;
color: var(--secondary-color);
cursor: pointer;
transition: var(--transition);
}
.filter-option:hover {
background-color: var(--blockchain-color);
color: white;
}
.filter-option.active {
background-color: var(--blockchain-color);
color: white;
}
.chart-container {
height: 300px;
width: 100%;
margin-bottom: 20px;
}
.nav-tabs .nav-link {
color: var(--secondary-color);
}
.nav-tabs .nav-link.active {
color: var(--blockchain-color);
font-weight: bold;
}
.tab-content {
padding: 20px 0;
}
.blockchain-badge {
display: inline-block;
padding: 5px 10px;
border-radius: 20px;
font-size: 0.8rem;
margin-right: 10px;
background-color: var(--blockchain-color);
color: white;
}
.timeline {
position: relative;
padding-left: 30px;
margin-bottom: 20px;
}
.timeline-line {
position: absolute;
left: 15px;
top: 0;
bottom: 0;
width: 2px;
background-color: var(--blockchain-color);
}
.timeline-item {
position: relative;
margin-bottom: 20px;
}
.timeline-dot {
position: absolute;
left: -30px;
top: 0;
width: 20px;
height: 20px;
border-radius: 50%;
background-color: var(--blockchain-color);
border: 3px solid white;
}
.timeline-date {
font-weight: bold;
margin-bottom: 5px;
color: var(--blockchain-color);
}
.timeline-content {
background-color: white;
padding: 15px;
border-radius: 10px;
box-shadow: var(--card-shadow);
}
.timeline-title {
font-weight: bold;
margin-bottom: 10px;
}
.timeline-desc {
color: var(--secondary-color);
font-size: 0.9rem;
}
.qr-code {
width: 150px;
height: 150px;
margin: 0 auto 20px;
background-color: white;
padding: 10px;
border-radius: 10px;
box-shadow: var(--card-shadow);
}
.qr-code img {
width: 100%;
min-height: auto;
object-fit: contain;
}
</style> </head> <body> <!-- 导航栏 -->
<div id="navbar-container"></div>
<!-- 主要内容 --> <div class="container py-5"> <div class="blockchain-header"> <div class="container"> <h1>关键节点区块链存证</h1> <p class="lead">利用区块链技术记录农产品生产全过程关键节点数据，保障食品安全，提升消费者信任</p> </div> </div> <!-- 顶部统计卡片 --> <div class="row mb-4"> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-cubes stat-icon"></i> <div class="stat-value">1,256</div> <div class="stat-label">区块链存证总数</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-seedling stat-icon"></i> <div class="stat-value">328</div> <div class="stat-label">种植环节存证</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-truck stat-icon"></i> <div class="stat-value">452</div> <div class="stat-label">物流环节存证</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-certificate stat-icon"></i> <div class="stat-value">476</div> <div class="stat-label">认证环节存证</div> </div> </div> </div> <!-- 主要内容区域 --> <div class="row"> <!-- 左侧内容 --> <div class="col-md-8"> <!-- 存证列表 --> <div class="blockchain-card mb-4"> <h4 class="mb-4">区块链存证记录</h4> <div class="proof-item"> <div class="proof-header"> <div class="proof-title"> <i class="fas fa-seedling proof-icon"></i>
水稻种植 - 播种记录
</div> <div class="proof-time">2023-10-15 08:30</div> </div> <div class="proof-content">
A区块水稻播种完成，品种：南粳46，种子批次：SJ202304，播种面积：50亩，播种密度：15kg/亩。
</div> <div class="proof-hash">
Hash: 0x7f83b1657ff1fc53b92dc18148a1d65dfc2d4b1fa3d677284addd200126d9069
</div> <div class="proof-actions"> <button class="btn btn-sm btn-primary" style="background-color: #6610f2; border-color: #6610f2;">查看详情</button> <button class="btn btn-sm btn-outline-secondary">验证</button> </div> </div> <div class="proof-item"> <div class="proof-header"> <div class="proof-title"> <i class="fas fa-tint proof-icon"></i>
水稻种植 - 灌溉记录
</div> <div class="proof-time">2023-10-14 15:45</div> </div> <div class="proof-content">
A区块水稻第一次灌溉完成，灌溉水源：地下水，灌溉量：30m³/亩，灌溉时长：2小时。
</div> <div class="proof-hash">
Hash: 0x3f4fa19c2bb3a5a3583c14dca29e7f85e0f6a07f5a7d212f1982bc7e436cfca2
</div> <div class="proof-actions"> <button class="btn btn-sm btn-primary" style="background-color: #6610f2; border-color: #6610f2;">查看详情</button> <button class="btn btn-sm btn-outline-secondary">验证</button> </div> </div> <div class="proof-item"> <div class="proof-header"> <div class="proof-title"> <i class="fas fa-flask proof-icon"></i>
水稻种植 - 施肥记录
</div> <div class="proof-time">2023-10-13 09:20</div> </div> <div class="proof-content">
A区块水稻第一次施肥完成，肥料类型：有机复合肥，肥料批次：HF202305，施肥量：25kg/亩。
</div> <div class="proof-hash">
Hash: 0x5d7f6a7a30f7ff591c8a51c276a1e5d2a4e4ce2ee6af3c1a9dae1ca0d18bcedf
</div> <div class="proof-actions"> <button class="btn btn-sm btn-primary" style="background-color: #6610f2; border-color: #6610f2;">查看详情</button> <button class="btn btn-sm btn-outline-secondary">验证</button> </div> </div> <div class="proof-item"> <div class="proof-header"> <div class="proof-title"> <i class="fas fa-bug proof-icon"></i>
水稻种植 - 病虫害防治
</div> <div class="proof-time">2023-10-12 14:10</div> </div> <div class="proof-content">
A区块水稻第一次病虫害防治完成，防治对象：稻飞虱，药剂类型：生物农药，药剂批次：NY202306，用量：0.5kg/亩。
</div> <div class="proof-hash">
Hash: 0x7d87c5ea75d2fa915c43e0b711a1f01a1c0dfd8c2f1c9e2e3642d3e5c4c6e9c2
</div> <div class="proof-actions"> <button class="btn btn-sm btn-primary" style="background-color: #6610f2; border-color: #6610f2;">查看详情</button> <button class="btn btn-sm btn-outline-secondary">验证</button> </div> </div> <div class="proof-item"> <div class="proof-header"> <div class="proof-title"> <i class="fas fa-thermometer-half proof-icon"></i>
水稻种植 - 环境监测
</div> <div class="proof-time">2023-10-11 10:00</div> </div> <div class="proof-content">
A区块水稻生长环境监测数据，温度：28.5°C，湿度：65%，光照：856 W/m²，土壤湿度：42%，土壤pH值：6.8。
</div> <div class="proof-hash">
Hash: 0x9f86d081884c7d659a2feaa0c55ad015a3bf4f1b2b0b822cd15d6c15b0f00a08
</div> <div class="proof-actions"> <button class="btn btn-sm btn-primary" style="background-color: #6610f2; border-color: #6610f2;">查看详情</button> <button class="btn btn-sm btn-outline-secondary">验证</button> </div> </div> <div class="text-center mt-4"> <button class="btn btn-outline-primary" style="border-color: #6610f2; color: #6610f2;">查看更多存证记录</button> </div> </div> <!-- 区块链溯源 --> <div class="blockchain-card mb-4"> <h4 class="mb-4">区块链溯源时间线</h4> <div class="timeline"> <div class="timeline-line"></div> <div class="timeline-item"> <div class="timeline-dot"></div> <div class="timeline-date">2023-10-15</div> <div class="timeline-content"> <div class="timeline-title">播种</div> <div class="timeline-desc">
A区块水稻播种完成，品种：南粳46，种子批次：SJ202304，播种面积：50亩，播种密度：15kg/亩。
</div> </div> </div> <div class="timeline-item"> <div class="timeline-dot"></div> <div class="timeline-date">2023-10-14</div> <div class="timeline-content"> <div class="timeline-title">灌溉</div> <div class="timeline-desc">
A区块水稻第一次灌溉完成，灌溉水源：地下水，灌溉量：30m³/亩，灌溉时长：2小时。
</div> </div> </div> <div class="timeline-item"> <div class="timeline-dot"></div> <div class="timeline-date">2023-10-13</div> <div class="timeline-content"> <div class="timeline-title">施肥</div> <div class="timeline-desc">
A区块水稻第一次施肥完成，肥料类型：有机复合肥，肥料批次：HF202305，施肥量：25kg/亩。
</div> </div> </div> <div class="timeline-item"> <div class="timeline-dot"></div> <div class="timeline-date">2023-10-12</div> <div class="timeline-content"> <div class="timeline-title">病虫害防治</div> <div class="timeline-desc">
A区块水稻第一次病虫害防治完成，防治对象：稻飞虱，药剂类型：生物农药，药剂批次：NY202306，用量：0.5kg/亩。
</div> </div> </div> <div class="timeline-item"> <div class="timeline-dot"></div> <div class="timeline-date">2023-10-11</div> <div class="timeline-content"> <div class="timeline-title">环境监测</div> <div class="timeline-desc">
A区块水稻生长环境监测数据，温度：28.5°C，湿度：65%，光照：856 W/m²，土壤湿度：42%，土壤pH值：6.8。
</div> </div> </div> </div> </div> </div> <!-- 右侧内容 --> <div class="col-md-4"> <!-- 区块链存证查询 --> <div class="blockchain-card"> <h4 class="mb-4">存证查询</h4> <div class="mb-3"> <label class="form-label">存证哈希值</label> <input type="text" class="form-control" placeholder="输入存证哈希值"> </div> <div class="mb-3"> <label class="form-label">或扫描二维码</label> <div class="qr-code"> <img src="images/blockchain-qr.png" alt="区块链存证二维码"> </div> </div> <div class="d-grid"> <button class="btn btn-primary" style="background-color: #6610f2; border-color: #6610f2;">查询存证</button> </div> </div> <!-- 筛选器 --> <div class="blockchain-card mt-4"> <h4 class="mb-4">存证筛选</h4> <div class="filter-card"> <div class="filter-title">存证类型</div> <div class="filter-options"> <div class="filter-option active">全部</div> <div class="filter-option">种植环节</div> <div class="filter-option">加工环节</div> <div class="filter-option">物流环节</div> <div class="filter-option">认证环节</div> </div> </div> <div class="filter-card"> <div class="filter-title">区块选择</div> <div class="filter-options"> <div class="filter-option active">全部</div> <div class="filter-option">A区块</div> <div class="filter-option">B区块</div> <div class="filter-option">C区块</div> <div class="filter-option">D区块</div> </div> </div> <div class="filter-card"> <div class="filter-title">时间范围</div> <div class="filter-options"> <div class="filter-option active">全部</div> <div class="filter-option">今天</div> <div class="filter-option">本周</div> <div class="filter-option">本月</div> <div class="filter-option">自定义</div> </div> </div> <div class="d-grid mt-3"> <button class="btn btn-primary" style="background-color: #6610f2; border-color: #6610f2;">应用筛选</button> </div> </div> <!-- 区块链存证说明 --> <div class="blockchain-card mt-4"> <h4 class="mb-4">区块链存证说明</h4> <div class="mb-3"> <h5>什么是区块链存证？</h5> <p>区块链存证是利用区块链技术的不可篡改、可追溯等特性，将农产品生产全过程的关键节点数据记录在区块链上，形成可信的数据证明。</p> </div> <div class="mb-3"> <h5>存证的作用</h5> <ul> <li>保障农产品质量安全</li> <li>提升消费者信任度</li> <li>实现农产品全程可追溯</li> <li>防止假冒伪劣产品</li> <li>提升农产品品牌价值</li> </ul> </div> <div class="mb-3"> <h5>如何验证存证？</h5> <p>消费者可以通过扫描产品包装上的二维码，或在本平台输入存证哈希值，查询农产品的全程生产数据，验证产品的真实性和安全性。</p> </div> </div> </div> </div> </div> <!-- 页脚 -->
<div id="footer-container"></div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="js/login-config.js"></script>
<script src="js/components.js"></script>
<script>
// 使用 common.js 加载导航栏

// 筛选选项点击事件
document.querySelectorAll('.filter-option').forEach(option => {
option.addEventListener('click', function() {
const parent = this.parentElement;
parent.querySelectorAll('.filter-option').forEach(opt => {
opt.classList.remove('active');
});
this.classList.add('active');
});
});
// 应用筛选按钮点击事件
document.querySelectorAll('.btn-primary').forEach(btn => {
if (btn.textContent.trim() === '应用筛选') {
btn.addEventListener('click', function() {
alert('正在应用筛选条件，请稍后...');
});
}
});
// 查询存证按钮点击事件
document.querySelectorAll('.btn-primary').forEach(btn => {
if (btn.textContent.trim() === '查询存证') {
btn.addEventListener('click', function() {
alert('正在查询存证信息，请稍后...');
});
}
});
// 查看详情按钮点击事件
document.querySelectorAll('.proof-actions .btn-primary').forEach(btn => {
btn.addEventListener('click', function() {
const proofTitle = this.closest('.proof-item').querySelector('.proof-title').textContent.trim();
alert(`正在查看存证详情：${proofTitle}，请稍后...`);
});
});
// 验证按钮点击事件
document.querySelectorAll('.proof-actions .btn-outline-secondary').forEach(btn => {
btn.addEventListener('click', function() {
const proofHash = this.closest('.proof-item').querySelector('.proof-hash').textContent.trim().replace('Hash: ', '');
alert(`正在验证存证哈希值：${proofHash}，请稍后...`);
setTimeout(() => {
alert('验证成功！存证信息真实有效。');
}, 1000);
});
});
// 查看更多存证记录按钮点击事件
document.querySelector('.btn-outline-primary').addEventListener('click', function() {
alert('正在加载更多存证记录，请稍后...');
});

// 确保导航栏在页面加载完成后可见
window.onload = function() {
    // 确保导航栏可见
    const navbarContainer = document.getElementById('navbar-container');
    if (navbarContainer) {
        navbarContainer.style.display = '';
    }
};
</script> </body> </html>
