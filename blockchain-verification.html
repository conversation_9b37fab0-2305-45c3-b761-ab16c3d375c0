<!DOCTYPE html> <html lang="zh-CN"> <head> <meta charset="UTF-8"> <meta name="viewport" content="width=device-width, initial-scale=1.0"> <title>关键节点区块链存证 - 智慧农业平台</title> <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet"> <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet"> <link rel="stylesheet" href="css/common.css"> <link rel="stylesheet" href="css/navbar.css"> <style>
:root {
--primary-color: #28a745;
--secondary-color: #6c757d;
--light-bg: #f8f9fa;
--card-shadow: 0 4px 6px rgba(0,0,0,0.1);
--transition: all 0.3s ease;
--blockchain-color: #3498db;
}
body {
background-color: var(--light-bg);
font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}
.blockchain-header {
background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80');
background-size: cover;
background-position: center;
height: 200px;
color: white;
display: flex;
align-items: center;
margin-bottom: 30px;
border-radius: 10px;
}
.blockchain-card {
background: white;
padding: 20px;
border-radius: 10px;
box-shadow: var(--card-shadow);
margin-bottom: 30px;
min-height: auto;
}
.stat-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 20px;
transition: var(--transition);
}
.stat-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.stat-icon {
font-size: 2rem;
color: var(--blockchain-color);
margin-bottom: 10px;
}
.stat-value {
font-size: 1.5rem;
font-weight: bold;
color: var(--blockchain-color);
margin-bottom: 5px;
}
.stat-label {
font-size: 0.9rem;
color: var(--secondary-color);
}
.verification-form {
background: var(--light-bg);
padding: 20px;
border-radius: 10px;
margin-bottom: 20px;
}
.verification-result {
display: none;
margin-top: 20px;
padding: 20px;
border-radius: 10px;
}
.verification-success {
background-color: rgba(40, 167, 69, 0.1);
border-left: 4px solid var(--primary-color);
}
.verification-error {
background-color: rgba(220, 53, 69, 0.1);
border-left: 4px solid #dc3545;
}
.verification-title {
font-weight: bold;
margin-bottom: 15px;
display: flex;
align-items: center;
}
.verification-icon {
margin-right: 10px;
}
.verification-icon.success {
color: var(--primary-color);
}
.verification-icon.error {
color: #dc3545;
}
.verification-content {
margin-bottom: 15px;
}
.verification-details {
background-color: white;
padding: 15px;
border-radius: 5px;
margin-top: 15px;
}
.detail-item {
display: flex;
margin-bottom: 10px;
}
.detail-label {
font-weight: bold;
width: 150px;
flex-shrink: 0;
}
.detail-value {
flex: 1;
word-break: break-all;
}
.blockchain-node {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 15px;
transition: var(--transition);
position: relative;
}
.blockchain-node:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.blockchain-node::before {
content: '';
position: absolute;
top: 50%;
left: -20px;
width: 20px;
height: 2px;
background-color: var(--blockchain-color);
display: none;
}
.blockchain-node:not(:first-child)::before {
display: block;
}
.node-title {
font-weight: bold;
margin-bottom: 10px;
display: flex;
align-items: center;
justify-content: space-between;
}
.node-icon {
color: var(--blockchain-color);
margin-right: 10px;
}
.node-time {
font-size: 0.9rem;
color: var(--secondary-color);
}
.node-content {
margin-bottom: 10px;
}
.node-hash {
font-family: monospace;
font-size: 0.8rem;
background-color: #f0f0f0;
padding: 5px;
border-radius: 3px;
overflow: hidden;
text-overflow: ellipsis;
white-space: nowrap;
}
.node-status {
display: inline-block;
padding: 3px 8px;
border-radius: 15px;
font-size: 0.8rem;
margin-left: 10px;
}
.status-verified {
background-color: rgba(40, 167, 69, 0.1);
color: var(--primary-color);
}
.certificate-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 15px;
transition: var(--transition);
border: 1px solid #ddd;
}
.certificate-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.certificate-header {
text-align: center;
margin-bottom: 20px;
padding-bottom: 20px;
border-bottom: 1px solid #ddd;
}
.certificate-logo {
max-width: 100px;
margin-bottom: 10px;
}
.certificate-title {
font-weight: bold;
font-size: 1.5rem;
margin-bottom: 5px;
}
.certificate-subtitle {
color: var(--secondary-color);
}
.certificate-body {
margin-bottom: 20px;
}
.certificate-item {
margin-bottom: 15px;
}
.certificate-label {
font-weight: bold;
margin-bottom: 5px;
}
.certificate-value {
color: var(--secondary-color);
}
.certificate-footer {
display: flex;
justify-content: space-between;
align-items: center;
margin-top: 20px;
padding-top: 20px;
border-top: 1px solid #ddd;
}
.certificate-qr {
width: 80px;
height: 80px;
background-color: white;
padding: 5px;
border-radius: 5px;
}
.certificate-verification {
text-align: right;
}
.certificate-verification-text {
font-size: 0.9rem;
color: var(--secondary-color);
margin-bottom: 5px;
}
.certificate-verification-id {
font-family: monospace;
font-size: 0.8rem;
}
.history-item {
display: flex;
margin-bottom: 15px;
padding-bottom: 15px;
border-bottom: 1px solid #eee;
}
.history-icon {
width: 40px;
height: 40px;
background-color: var(--light-bg);
border-radius: 50%;
display: flex;
align-items: center;
justify-content: center;
margin-right: 15px;
color: var(--blockchain-color);
}
.history-content {
flex: 1;
}
.history-title {
font-weight: bold;
margin-bottom: 5px;
}
.history-time {
font-size: 0.9rem;
color: var(--secondary-color);
margin-bottom: 5px;
}
.history-desc {
font-size: 0.9rem;
}
.blockchain-info-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 15px;
}
.blockchain-info-title {
font-weight: bold;
margin-bottom: 10px;
display: flex;
align-items: center;
}
.blockchain-info-icon {
color: var(--blockchain-color);
margin-right: 10px;
}
.blockchain-info-content {
color: var(--secondary-color);
}
.blockchain-steps {
margin-top: 15px;
}
.blockchain-step {
display: flex;
margin-bottom: 10px;
}
.step-number {
width: 25px;
height: 25px;
background-color: var(--blockchain-color);
color: white;
border-radius: 50%;
display: flex;
align-items: center;
justify-content: center;
margin-right: 10px;
font-size: 0.8rem;
font-weight: bold;
}
.step-content {
flex: 1;
}
</style> </head> <body> <!-- 导航栏 -->
<div id="navbar-container"></div>
<!-- 主要内容 --> <div class="container py-5"> <div class="blockchain-header"> <div class="container"> <h1>关键节点区块链存证</h1> <p class="lead">利用区块链技术，为农产品生产关键节点提供不可篡改的存证记录，保障产品溯源真实可信</p> </div> </div> <!-- 顶部统计卡片 --> <div class="row mb-4"> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-cubes stat-icon"></i> <div class="stat-value">12,568</div> <div class="stat-label">存证总数</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-certificate stat-icon"></i> <div class="stat-value">5,823</div> <div class="stat-label">溯源证书数</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-check-circle stat-icon"></i> <div class="stat-value">100%</div> <div class="stat-label">验证准确率</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-link stat-icon"></i> <div class="stat-value">8</div> <div class="stat-label">关键节点数</div> </div> </div> </div> <!-- 主要内容区域 --> <div class="row"> <!-- 左侧内容 --> <div class="col-md-8"> <!-- 区块链验证 --> <div class="blockchain-card"> <h4 class="mb-4">区块链验证</h4> <div class="verification-form"> <div class="mb-3"> <label for="verificationCode" class="form-label">输入溯源码或扫描二维码进行验证</label> <div class="input-group"> <input type="text" class="form-control" id="verificationCode" placeholder="输入溯源码"> <button class="btn btn-primary" type="button" id="scanQrCode"><i class="fas fa-qrcode"></i> 扫描</button> <button class="btn btn-success" type="button" id="verifyCode">验证</button> </div> <div class="form-text">示例溯源码：BLK-2023-06-15-A001</div> </div> </div> <!-- 验证成功结果 --> <div class="verification-result verification-success" id="successResult"> <div class="verification-title"> <i class="fas fa-check-circle verification-icon success"></i> <span>验证成功</span> </div> <div class="verification-content"> <p>此溯源码对应的产品信息已在区块链上存证，信息真实可靠。</p> </div> <div class="verification-details"> <div class="detail-item"> <div class="detail-label">产品名称：</div> <div class="detail-value">有机稻花香大米</div> </div> <div class="detail-item"> <div class="detail-label">生产基地：</div> <div class="detail-value">浙江省嘉兴市水稻基地</div> </div> <div class="detail-item"> <div class="detail-label">生产日期：</div> <div class="detail-value">2023-06-15</div> </div> <div class="detail-item"> <div class="detail-label">批次编号：</div> <div class="detail-value">A001</div> </div> <div class="detail-item"> <div class="detail-label">区块链存证时间：</div> <div class="detail-value">2023-06-15 14:30:25</div> </div> <div class="detail-item"> <div class="detail-label">区块哈希值：</div> <div class="detail-value">0x8f7d8b6c5a4e3d2c1b0a9f8e7d6c5b4a3f2e1d0c9b8a7f6e5d4c3b2a1f0e9d8</div> </div> </div> <div class="text-center mt-3"> <button class="btn btn-success" id="viewCertificate">查看溯源证书</button> <button class="btn btn-outline-primary" id="viewBlockchain">查看区块链记录</button> </div> </div> <!-- 验证失败结果 --> <div class="verification-result verification-error" id="errorResult"> <div class="verification-title"> <i class="fas fa-times-circle verification-icon error"></i> <span>验证失败</span> </div> <div class="verification-content"> <p>无法验证此溯源码，可能的原因：</p> <ul> <li>溯源码不存在或输入错误</li> <li>产品信息尚未在区块链上存证</li> <li>溯源码已过期或失效</li> </ul> </div> <div class="text-center mt-3"> <button class="btn btn-primary" id="tryAgain">重新验证</button> <button class="btn btn-outline-secondary" id="contactSupport">联系客服</button> </div> </div> </div> <!-- 区块链记录 --> <div class="blockchain-card"> <h4 class="mb-4">区块链记录</h4> <div class="blockchain-node"> <div class="node-title"> <div> <i class="fas fa-seedling node-icon"></i> <span>种子采购</span> <span class="node-status status-verified">已验证</span> </div> <div class="node-time">2023-03-10 08:30:15</div> </div> <div class="node-content"> <p>采购有机稻花香种子，批次号：SD-2023-001，供应商：浙江农业种子有限公司</p> </div> <div class="node-hash">区块哈希：0x1a2b3c4d5e6f7g8h9i0j1k2l3m4n5o6p7q8r9s0t1u2v3w4x5y6z7a8b9c0d</div> </div> <div class="blockchain-node"> <div class="node-title"> <div> <i class="fas fa-tractor node-icon"></i> <span>播种种植</span> <span class="node-status status-verified">已验证</span> </div> <div class="node-time">2023-04-15 09:45:30</div> </div> <div class="node-content"> <p>在A区块进行有机稻花香种植，面积50些，种子用量150kg</p> </div> <div class="node-hash">区块哈希：0x2b3c4d5e6f7g8h9i0j1k2l3m4n5o6p7q8r9s0t1u2v3w4x5y6z7a8b9c0d1e</div> </div> <div class="blockchain-node"> <div class="node-title"> <div> <i class="fas fa-prescription-bottle node-icon"></i> <span>施肥记录</span> <span class="node-status status-verified">已验证</span> </div> <div class="node-time">2023-05-20 10:15:45</div> </div> <div class="node-content"> <p>对A区块进行有机肥施用，肥料类型：有机复合肥，用量：500kg</p> </div> <div class="node-hash">区块哈希：0x3c4d5e6f7g8h9i0j1k2l3m4n5o6p7q8r9s0t1u2v3w4x5y6z7a8b9c0d1e2f</div> </div> <div class="blockchain-node"> <div class="node-title"> <div> <i class="fas fa-bug node-icon"></i> <span>病虫害防治</span> <span class="node-status status-verified">已验证</span> </div> <div class="node-time">2023-05-30 14:20:10</div> </div> <div class="node-content"> <p>对A区块进行病虫害防治，使用生物农药《苯菌素》，用量：50ml/些</p> </div> <div class="node-hash">区块哈希：0x4d5e6f7g8h9i0j1k2l3m4n5o6p7q8r9s0t1u2v3w4x5y6z7a8b9c0d1e2f3g</div> </div> <div class="blockchain-node"> <div class="node-title"> <div> <i class="fas fa-tint node-icon"></i> <span>灵溪记录</span> <span class="node-status status-verified">已验证</span> </div> <div class="node-time">2023-06-05 08:50:25</div> </div> <div class="node-content"> <p>对A区块进行灵溪，灵溪时间45分钟，灵溪量：120m³</p> </div> <div class="node-hash">区块哈希：0x5e6f7g8h9i0j1k2l3m4n5o6p7q8r9s0t1u2v3w4x5y6z7a8b9c0d1e2f3g4h</div> </div> <div class="blockchain-node"> <div class="node-title"> <div> <i class="fas fa-clipboard-check node-icon"></i> <span>质量检测</span> <span class="node-status status-verified">已验证</span> </div> <div class="node-time">2023-06-10 11:30:40</div> </div> <div class="node-content"> <p>对A区块产品进行质量检测，检测项目：农药残留、重金属、营养成分，检测结果：合格</p> </div> <div class="node-hash">区块哈希：0x6f7g8h9i0j1k2l3m4n5o6p7q8r9s0t1u2v3w4x5y6z7a8b9c0d1e2f3g4h5i</div> </div> <div class="blockchain-node"> <div class="node-title"> <div> <i class="fas fa-cut node-icon"></i> <span>收获加工</span> <span class="node-status status-verified">已验证</span> </div> <div class="node-time">2023-06-15 09:15:35</div> </div> <div class="node-content"> <p>对A区块产品进行收获加工，收获量：5000kg，加工方式：粉碎、筛选、包装</p> </div> <div class="node-hash">区块哈希：0x7g8h9i0j1k2l3m4n5o6p7q8r9s0t1u2v3w4x5y6z7a8b9c0d1e2f3g4h5i6j</div> </div> <div class="blockchain-node"> <div class="node-title"> <div> <i class="fas fa-certificate node-icon"></i> <span>溯源证书生成</span> <span class="node-status status-verified">已验证</span> </div> <div class="node-time">2023-06-15 14:30:25</div> </div> <div class="node-content"> <p>生成溯源证书，溯源码：BLK-2023-06-15-A001，产品名称：有机稻花香大米</p> </div> <div class="node-hash">区块哈希：0x8f7d8b6c5a4e3d2c1b0a9f8e7d6c5b4a3f2e1d0c9b8a7f6e5d4c3b2a1f0e9d8</div> </div> </div> </div> <!-- 右侧内容 --> <div class="col-md-4"> <!-- 溯源证书 --> <div class="blockchain-card"> <h4 class="mb-4">溯源证书</h4> <div class="certificate-card"> <div class="certificate-header"> <img src="images/logo.png" alt="智慧农业平台" class="certificate-logo"> <div class="certificate-title">区块链溯源证书</div> <div class="certificate-subtitle">不可篡改的农产品身份证明</div> </div> <div class="certificate-body"> <div class="certificate-item"> <div class="certificate-label">产品名称</div> <div class="certificate-value">有机稻花香大米</div> </div> <div class="certificate-item"> <div class="certificate-label">生产基地</div> <div class="certificate-value">浙江省嘉兴市水稻基地</div> </div> <div class="certificate-item"> <div class="certificate-label">生产日期</div> <div class="certificate-value">2023-06-15</div> </div> <div class="certificate-item"> <div class="certificate-label">批次编号</div> <div class="certificate-value">A001</div> </div> <div class="certificate-item"> <div class="certificate-label">认证类型</div> <div class="certificate-value">有机认证</div> </div> <div class="certificate-item"> <div class="certificate-label">认证机构</div> <div class="certificate-value">中国质量认证中心</div> </div> <div class="certificate-item"> <div class="certificate-label">认证日期</div> <div class="certificate-value">2023-06-15</div> </div> <div class="certificate-item"> <div class="certificate-label">有效期至</div> <div class="certificate-value">2024-06-14</div> </div> </div> <div class="certificate-footer"> <img src="images/qr-code.png" alt="溯源码" class="certificate-qr"> <div class="certificate-verification"> <div class="certificate-verification-text">溯源码</div> <div class="certificate-verification-id">BLK-2023-06-15-A001</div> </div> </div> </div> <div class="text-center mt-3"> <button class="btn btn-success" id="downloadCertificate">下载证书</button> <button class="btn btn-outline-primary" id="shareCertificate">分享证书</button> </div> </div> <!-- 区块链信息 --> <div class="blockchain-card"> <h4 class="mb-4">区块链信息</h4> <div class="blockchain-info-card"> <div class="blockchain-info-title"> <i class="fas fa-info-circle blockchain-info-icon"></i> <span>什么是区块链存证？</span> </div> <div class="blockchain-info-content"> <p>区块链存证是利用区块链技术的去中心化、不可篡改、可追溯等特性，将农产品生产全过程的关键节点信息记录在区块链上，实现信息的真实性和可靠性。</p> </div> </div> <div class="blockchain-info-card"> <div class="blockchain-info-title"> <i class="fas fa-question-circle blockchain-info-icon"></i> <span>如何验证真伪？</span> </div> <div class="blockchain-info-content"> <p>您可以通过以下方式验证产品真伪：</p> <div class="blockchain-steps"> <div class="blockchain-step"> <div class="step-number">1</div> <div class="step-content">扫描产品包装上的溯源码二维码</div> </div> <div class="blockchain-step"> <div class="step-number">2</div> <div class="step-content">在验证页面输入溯源码</div> </div> <div class="blockchain-step"> <div class="step-number">3</div> <div class="step-content">查看区块链存证记录和溯源证书</div> </div> </div> </div> </div> </div> <!-- 存证历史 --> <div class="blockchain-card"> <h4 class="mb-4">存证历史</h4> <div class="history-item"> <div class="history-icon"> <i class="fas fa-certificate"></i> </div> <div class="history-content"> <div class="history-title">有机稻花香大米</div> <div class="history-time">2023-06-15 14:30:25</div> <div class="history-desc">生成溯源证书，溯源码：BLK-2023-06-15-A001</div> </div> </div> <div class="history-item"> <div class="history-icon"> <i class="fas fa-certificate"></i> </div> <div class="history-content"> <div class="history-title">有机黄瓜</div> <div class="history-time">2023-06-10 11:45:30</div> <div class="history-desc">生成溯源证书，溯源码：BLK-2023-06-10-B002</div> </div> </div> <div class="history-item"> <div class="history-icon"> <i class="fas fa-certificate"></i> </div> <div class="history-content"> <div class="history-title">有机番茄</div> <div class="history-time">2023-06-05 09:20:15</div> <div class="history-desc">生成溯源证书，溯源码：BLK-2023-06-05-C003</div> </div> </div> </div> </div> </div> </div> <!-- 页脚 -->
<div id="footer-container"></div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="js/login-config.js"></script>
<script src="js/components.js"></script>
<script>
// 使用 common.js 加载导航栏

// 验证按钮点击事件
document.getElementById('verifyCode').addEventListener('click', function() {
const verificationCode = document.getElementById('verificationCode').value.trim();
if (verificationCode === 'BLK-2023-06-15-A001') {
// 模拟验证成功
document.getElementById('successResult').style.display = 'block';
document.getElementById('errorResult').style.display = 'none';
} else {
// 模拟验证失败
document.getElementById('successResult').style.display = 'none';
document.getElementById('errorResult').style.display = 'block';
}
});
// 扫描二维码按钮点击事件
document.getElementById('scanQrCode').addEventListener('click', function() {
alert('请允许打开相机进行扫描');
// 实际应用中应该调用相机扫描功能
});
// 重新验证按钮点击事件
document.getElementById('tryAgain').addEventListener('click', function() {
document.getElementById('successResult').style.display = 'none';
document.getElementById('errorResult').style.display = 'none';
document.getElementById('verificationCode').value = '';
document.getElementById('verificationCode').focus();
});
// 联系客服按钮点击事件
document.getElementById('contactSupport').addEventListener('click', function() {
alert('正在连接客服，请稍后...');
});
// 查看溯源证书按钮点击事件
document.getElementById('viewCertificate').addEventListener('click', function() {
// 滚动到溯源证书区域
document.querySelector('.certificate-card').scrollIntoView({ behavior: 'smooth' });
});
// 查看区块链记录按钮点击事件
document.getElementById('viewBlockchain').addEventListener('click', function() {
// 滚动到区块链记录区域
document.querySelector('.blockchain-node').scrollIntoView({ behavior: 'smooth' });
});
// 下载证书按钮点击事件
document.getElementById('downloadCertificate').addEventListener('click', function() {
alert('正在下载溯源证书...');
// 实际应用中应该生成PDF并下载
});
// 分享证书按钮点击事件
document.getElementById('shareCertificate').addEventListener('click', function() {
alert('请选择分享方式：微信、微博、QQ...');
// 实际应用中应该调用分享 API
});
// 初始化页面时隐藏验证结果
document.getElementById('successResult').style.display = 'none';
document.getElementById('errorResult').style.display = 'none';
// 模拟验证码输入事件
document.getElementById('verificationCode').addEventListener('keypress', function(e) {
if (e.key === 'Enter') {
document.getElementById('verifyCode').click();
}
});
</script>