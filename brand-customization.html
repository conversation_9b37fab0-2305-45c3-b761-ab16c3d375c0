<!DOCTYPE html> <html lang="zh-CN"> <head> <meta charset="UTF-8"> <meta name="viewport" content="width=device-width, initial-scale=1.0"> <title>品牌定制 - 智慧农业平台</title> <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet"> <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet"> <link rel="stylesheet" href="css/common.css"> <link rel="stylesheet" href="css/navbar.css"> <style>
:root {
--primary-color: #28a745;
--secondary-color: #6c757d;
--light-bg: #f8f9fa;
--card-shadow: 0 4px 6px rgba(0,0,0,0.1);
--transition: all 0.3s ease;
}
body {
background-color: var(--light-bg);
font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}
.customization-header {
background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80');
background-size: cover;
background-position: center;
height: 300px;
color: white;
display: flex;
align-items: center;
margin-bottom: 30px;
border-radius: 10px;
}
.customization-card {
background: white;
padding: 30px;
border-radius: 10px;
box-shadow: var(--card-shadow);
margin-bottom: 30px;
}
.step-indicator {
display: flex;
justify-content: space-between;
margin-bottom: 30px;
}
.step {
flex: 1;
text-align: center;
padding: 15px;
position: relative;
}
.step::after {
content: '';
position: absolute;
top: 50%;
right: 0;
width: 100%;
height: 2px;
background-color: #ddd;
transform: translateY(-50%);
z-index: -1;
}
.step:last-child::after {
display: none;
}
.step-number {
width: 40px;
height: 40px;
background-color: #ddd;
border-radius: 50%;
display: flex;
align-items: center;
justify-content: center;
margin: 0 auto 10px;
font-weight: bold;
color: white;
position: relative;
z-index: 1;
}
.step.active .step-number {
background-color: var(--primary-color);
}
.step.completed .step-number {
background-color: var(--primary-color);
}
.step-title {
font-size: 0.9rem;
color: var(--secondary-color);
}
.step.active .step-title {
color: var(--primary-color);
font-weight: bold;
}
.product-card {
background: white;
border-radius: 10px;
box-shadow: var(--card-shadow);
margin-bottom: 20px;
overflow: hidden;
transition: var(--transition);
}
.product-card:hover {
transform: translateY(-5px);
}
.product-image {
height: 200px;
object-fit: cover;
width: 100%;
}
.product-info {
padding: 15px;
}
.product-title {
font-size: 1.1rem;
font-weight: bold;
margin-bottom: 10px;
}
.product-price {
color: var(--primary-color);
font-weight: bold;
font-size: 1.2rem;
margin-bottom: 10px;
}
.product-description {
color: var(--secondary-color);
margin-bottom: 15px;
}
.product-features {
display: flex;
flex-wrap: wrap;
gap: 10px;
margin-bottom: 15px;
}
.product-feature {
background: var(--light-bg);
padding: 5px 10px;
border-radius: 15px;
font-size: 0.8rem;
color: var(--secondary-color);
}
.product-actions {
display: flex;
justify-content: space-between;
}
.customization-option {
margin-bottom: 20px;
}
.option-title {
font-weight: bold;
margin-bottom: 10px;
}
.option-items {
display: flex;
flex-wrap: wrap;
gap: 10px;
margin-bottom: 15px;
}
.option-item {
padding: 8px 15px;
border-radius: 20px;
background-color: var(--light-bg);
border: 1px solid #ddd;
cursor: pointer;
transition: var(--transition);
}
.option-item.active {
background-color: var(--primary-color);
color: white;
border-color: var(--primary-color);
}
.option-item:hover:not(.active) {
background-color: #f0f0f0;
}
.color-option {
width: 30px;
height: 30px;
border-radius: 50%;
cursor: pointer;
transition: var(--transition);
margin-right: 10px;
border: 2px solid transparent;
}
.color-option.active {
border-color: var(--primary-color);
transform: scale(1.2);
}
.color-option:hover:not(.active) {
transform: scale(1.1);
}
.preview-container {
background: var(--light-bg);
padding: 20px;
border-radius: 10px;
margin-bottom: 20px;
}
.preview-title {
font-weight: bold;
margin-bottom: 15px;
}
.preview-image {
width: 100%;
border-radius: 10px;
margin-bottom: 15px;
}
.preview-details {
margin-bottom: 15px;
}
.preview-detail {
display: flex;
margin-bottom: 10px;
}
.preview-detail-label {
min-width: 100px;
font-weight: bold;
}
.preview-detail-value {
flex: 1;
}
.summary-item {
display: flex;
justify-content: space-between;
margin-bottom: 10px;
padding-bottom: 10px;
border-bottom: 1px solid #eee;
}
.summary-total {
font-weight: bold;
font-size: 1.2rem;
color: var(--primary-color);
}
.case-card {
background: white;
border-radius: 10px;
box-shadow: var(--card-shadow);
margin-bottom: 20px;
overflow: hidden;
transition: var(--transition);
}
.case-card:hover {
transform: translateY(-5px);
}
.case-image {
height: 200px;
object-fit: cover;
width: 100%;
}
.case-info {
padding: 15px;
}
.case-title {
font-size: 1.1rem;
font-weight: bold;
margin-bottom: 10px;
}
.case-description {
color: var(--secondary-color);
margin-bottom: 15px;
}
.logo-upload {
border: 2px dashed #ddd;
border-radius: 10px;
padding: 20px;
text-align: center;
margin-bottom: 20px;
cursor: pointer;
transition: var(--transition);
}
.logo-upload:hover {
border-color: var(--primary-color);
}
.logo-upload-icon {
font-size: 3rem;
color: var(--secondary-color);
margin-bottom: 15px;
}
.logo-preview {
max-width: 200px;
max-height: 200px;
margin: 0 auto 15px;
display: none;
}
</style> </head> <body> <!-- 导航栏 -->
<div id="navbar-container"></div>
<!-- 主要内容 --> <div class="container py-5"> <div class="customization-header"> <div class="container"> <h1>品牌联名定制</h1> <p class="lead">为您的企业、活动或个人定制专属农产品，打造独特品牌体验</p> </div> </div> <!-- 定制步骤 --> <div class="customization-card"> <h3 class="mb-4">定制流程</h3> <div class="step-indicator"> <div class="step active"> <div class="step-number">1</div> <div class="step-title">选择产品</div> </div> <div class="step"> <div class="step-number">2</div> <div class="step-title">定制选项</div> </div> <div class="step"> <div class="step-number">3</div> <div class="step-title">上传标识</div> </div> <div class="step"> <div class="step-number">4</div> <div class="step-title">预览确认</div> </div> <div class="step"> <div class="step-number">5</div> <div class="step-title">提交订单</div> </div> </div> </div> <!-- 产品选择 --> <div class="customization-card"> <div class="d-flex justify-content-between align-items-center mb-4"> <h3>选择产品</h3> <div> <button class="btn btn-outline-success me-2">筛选</button> <button class="btn btn-outline-secondary">排序</button> </div> </div> <div class="row"> <!-- 产品1 --> <div class="col-md-4"> <div class="product-card"> <img src="images/product1.jpg" alt="产品图片" class="product-image"> <div class="product-info"> <div class="product-title">有机稻花香大米</div> <div class="product-price">¥188/盒 起</div> <div class="product-description">来自浙江嘉兴的优质有机大米，口感香糊，营养丰富</div> <div class="product-features"> <span class="product-feature">有机认证</span> <span class="product-feature">精美礼盒</span> <span class="product-feature">可定制</span> </div> <div class="product-actions"> <button class="btn btn-sm btn-outline-secondary">查看详情</button> <button class="btn btn-sm btn-success">选择定制</button> </div> </div> </div> </div> <!-- 产品2 --> <div class="col-md-4"> <div class="product-card"> <img src="images/product2.jpg" alt="产品图片" class="product-image"> <div class="product-info"> <div class="product-title">精选有机茶叶礼盒</div> <div class="product-price">¥288/盒 起</div> <div class="product-description">来自浙江杭州西湖龙井茶园的高山有机茶叶，香气浓郁</div> <div class="product-features"> <span class="product-feature">有机认证</span> <span class="product-feature">高端礼盒</span> <span class="product-feature">可定制</span> </div> <div class="product-actions"> <button class="btn btn-sm btn-outline-secondary">查看详情</button> <button class="btn btn-sm btn-success">选择定制</button> </div> </div> </div> </div> <!-- 产品3 --> <div class="col-md-4"> <div class="product-card"> <img src="images/product3.jpg" alt="产品图片" class="product-image"> <div class="product-info"> <div class="product-title">新鲜水果礼盒</div> <div class="product-price">¥258/盒 起</div> <div class="product-description">精选应季新鲜水果，包含进口水果和国产高端水果，新鲜送达</div> <div class="product-features"> <span class="product-feature">绿色认证</span> <span class="product-feature">精美礼盒</span> <span class="product-feature">可定制</span> </div> <div class="product-actions"> <button class="btn btn-sm btn-outline-secondary">查看详情</button> <button class="btn btn-sm btn-success">选择定制</button> </div> </div> </div> </div> <!-- 产品4 --> <div class="col-md-4"> <div class="product-card"> <img src="images/product4.jpg" alt="产品图片" class="product-image"> <div class="product-info"> <div class="product-title">有机蔬菜礼盒</div> <div class="product-price">¥168/盒 起</div> <div class="product-description">精选应季有机蔬菜，无农药残留，新鲜配送，适合健康礼品</div> <div class="product-features"> <span class="product-feature">有机认证</span> <span class="product-feature">现代包装</span> <span class="product-feature">可定制</span> </div> <div class="product-actions"> <button class="btn btn-sm btn-outline-secondary">查看详情</button> <button class="btn btn-sm btn-success">选择定制</button> </div> </div> </div> </div> <!-- 产品5 --> <div class="col-md-4"> <div class="product-card"> <img src="images/product5.jpg" alt="产品图片" class="product-image"> <div class="product-info"> <div class="product-title">特色坚果礼盒</div> <div class="product-price">¥328/盒 起</div> <div class="product-description">精选各类高端坚果，包含核桃、松子、开心果等，适合商务送礼</div> <div class="product-features"> <span class="product-feature">绿色认证</span> <span class="product-feature">高端礼盒</span> <span class="product-feature">可定制</span> </div> <div class="product-actions"> <button class="btn btn-sm btn-outline-secondary">查看详情</button> <button class="btn btn-sm btn-success">选择定制</button> </div> </div> </div> </div> <!-- 产品6 --> <div class="col-md-4"> <div class="product-card"> <img src="images/product6.jpg" alt="产品图片" class="product-image"> <div class="product-info"> <div class="product-title">精选蜂蜜礼盒</div> <div class="product-price">¥238/盒 起</div> <div class="product-description">精选天然野生蜂蜜，包含椰花蜂蜜、洁白蜂蜜等多种口味</div> <div class="product-features"> <span class="product-feature">有机认证</span> <span class="product-feature">现代包装</span> <span class="product-feature">可定制</span> </div> <div class="product-actions"> <button class="btn btn-sm btn-outline-secondary">查看详情</button> <button class="btn btn-sm btn-success">选择定制</button> </div> </div> </div> </div> </div> <div class="text-center mt-4"> <button class="btn btn-outline-success">加载更多</button> </div> </div> <!-- 定制选项 --> <div class="customization-card"> <h3 class="mb-4">定制选项</h3> <div class="row"> <div class="col-md-6"> <div class="product-card mb-4"> <img src="images/product1.jpg" alt="产品图片" class="product-image"> <div class="product-info"> <div class="product-title">有机稻花香大米</div> <div class="product-price">¥188/盒 起</div> <div class="product-description">来自浙江嘉兴的优质有机大米，口感香糊，营养丰富</div> </div> </div> <!-- 定制选项 --> <div class="customization-option"> <div class="option-title">礼盒规格</div> <div class="option-items"> <div class="option-item active">2.5kg</div> <div class="option-item">5kg</div> <div class="option-item">10kg</div> </div> </div> <div class="customization-option"> <div class="option-title">礼盒样式</div> <div class="option-items"> <div class="option-item active">传统中式</div> <div class="option-item">现代简约</div> <div class="option-item">商务典雅</div> <div class="option-item">节日喜庆</div> </div> </div> <div class="customization-option"> <div class="option-title">礼盒颜色</div> <div class="d-flex"> <div class="color-option active" style="background-color: #8B4513;"></div> <div class="color-option" style="background-color: #CD5C5C;"></div> <div class="color-option" style="background-color: #4682B4;"></div> <div class="color-option" style="background-color: #2E8B57;"></div> <div class="color-option" style="background-color: #4B0082;"></div> <div class="color-option" style="background-color: #000000;"></div> </div> </div> <div class="customization-option"> <div class="option-title">定制数量</div> <div class="input-group mb-3"> <button class="btn btn-outline-secondary" type="button" id="decrease">-</button> <input type="number" class="form-control text-center" value="50" min="30" id="quantity"> <button class="btn btn-outline-secondary" type="button" id="increase">+</button> </div> <div class="form-text">最小订购数量：30盒</div> </div> <div class="customization-option"> <div class="option-title">定制内容</div> <div class="form-check mb-2"> <input class="form-check-input" type="checkbox" id="customLogo" checked> <label class="form-check-label" for="customLogo">
定制公司标志
</label> </div> <div class="form-check mb-2"> <input class="form-check-input" type="checkbox" id="customGreeting" checked> <label class="form-check-label" for="customGreeting">
定制祝福语
</label> </div> <div class="form-check mb-2"> <input class="form-check-input" type="checkbox" id="customCard"> <label class="form-check-label" for="customCard">
定制贺卡
</label> </div> <div class="form-check"> <input class="form-check-input" type="checkbox" id="customPackaging"> <label class="form-check-label" for="customPackaging">
定制外包装
</label> </div> </div> <div class="customization-option"> <div class="option-title">定制祝福语</div> <div class="mb-3"> <textarea class="form-control" rows="3" placeholder="请输入您的祝福语，最多100字">感谢您的支持与信任，祝您事业赢发，幸福安康！</textarea> </div> </div> <div class="logo-upload" id="logoUpload"> <i class="fas fa-cloud-upload-alt logo-upload-icon"></i> <p>点击上传公司标志（建议使用PNG格式透明背景图片）</p> <img src="" alt="标志预览" class="logo-preview" id="logoPreview"> <input type="file" id="logoFile" style="display: none;" accept="image/*"> </div> </div> <div class="col-md-6"> <!-- 预览 --> <div class="preview-container"> <div class="preview-title">定制预览</div> <img src="images/preview1.jpg" alt="预览图片" class="preview-image"> <div class="preview-details"> <div class="preview-detail"> <div class="preview-detail-label">产品名称：</div> <div class="preview-detail-value">有机稻花香大米</div> </div> <div class="preview-detail"> <div class="preview-detail-label">礼盒规格：</div> <div class="preview-detail-value">2.5kg</div> </div> <div class="preview-detail"> <div class="preview-detail-label">礼盒样式：</div> <div class="preview-detail-value">传统中式</div> </div> <div class="preview-detail"> <div class="preview-detail-label">礼盒颜色：</div> <div class="preview-detail-value">木色</div> </div> <div class="preview-detail"> <div class="preview-detail-label">定制数量：</div> <div class="preview-detail-value">50盒</div> </div> <div class="preview-detail"> <div class="preview-detail-label">定制内容：</div> <div class="preview-detail-value">公司标志、祝福语</div> </div> <div class="preview-detail"> <div class="preview-detail-label">祝福语：</div> <div class="preview-detail-value">感谢您的支持与信任，祝您事业赢发，幸福安康！</div> </div> </div> </div> <!-- 订单摘要 --> <div class="preview-container"> <div class="preview-title">订单摘要</div> <div class="summary-item"> <div>产品单价</div> <div>¥188/盒</div> </div> <div class="summary-item"> <div>定制数量</div> <div>50盒</div> </div> <div class="summary-item"> <div>定制费用</div> <div>¥1,000</div> </div> <div class="summary-item"> <div>小计</div> <div>¥10,400</div> </div> <div class="summary-item"> <div>运费</div> <div>¥500</div> </div> <div class="summary-item"> <div>总计</div> <div class="summary-total">¥10,900</div> </div> <div class="d-grid gap-2 mt-4"> <button class="btn btn-success">提交定制订单</button> <button class="btn btn-outline-secondary">保存定制方案</button> </div> </div> </div> </div> </div> <!-- 成功案例 --> <div class="customization-card"> <h3 class="mb-4">成功案例</h3> <div class="row"> <!-- 案例1 --> <div class="col-md-4"> <div class="case-card"> <img src="images/case1.jpg" alt="案例图片" class="case-image"> <div class="case-info"> <div class="case-title">阿里巴巴员工福利定制</div> <div class="case-description">为阿里巴巴定制的员工福利礼盒，包含有机大米、茶叶和坚果，共计定制了5000份。</div> </div> </div> </div> <!-- 案例2 --> <div class="col-md-4"> <div class="case-card"> <img src="images/case2.jpg" alt="案例图片" class="case-image"> <div class="case-info"> <div class="case-title">华为商务礼品定制</div> <div class="case-description">为华为定制的高端商务礼品，采用高端包装设计，包含精选有机茶叶和蜂蜜。</div> </div> </div> </div> <!-- 案例3 --> <div class="col-md-4"> <div class="case-card"> <img src="images/case3.jpg" alt="案例图片" class="case-image"> <div class="case-info"> <div class="case-title">腾讯节日礼品定制</div> <div class="case-description">为腾讯定制的中秋节日礼品，采用现代简约风格，包含有机水果和坚果礼盒。</div> </div> </div> </div> </div> </div> </div> <!-- 页脚 -->
<div id="footer-container"></div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="js/login-config.js"></script>
<script src="js/components.js"></script>
<script>
// 使用 common.js 加载导航栏

// 初始化页面
document.addEventListener('DOMContentLoaded', function() {
// 定制选项点击事件
document.querySelectorAll('.option-item').forEach(item => {
item.addEventListener('click', function() {
const parent = this.parentElement;
parent.querySelectorAll('.option-item').forEach(opt => {
opt.classList.remove('active');
});
this.classList.add('active');
// 更新预览信息
updatePreview();
});
});
// 颜色选项点击事件
document.querySelectorAll('.color-option').forEach(color => {
color.addEventListener('click', function() {
document.querySelectorAll('.color-option').forEach(c => {
c.classList.remove('active');
});
this.classList.add('active');
// 更新预览信息
updatePreview();
});
});
// 数量增减按钮
document.getElementById('decrease').addEventListener('click', function() {
const quantityInput = document.getElementById('quantity');
const currentValue = parseInt(quantityInput.value);
if (currentValue > parseInt(quantityInput.min)) {
quantityInput.value = currentValue - 1;
updatePreview();
}
});
document.getElementById('increase').addEventListener('click', function() {
const quantityInput = document.getElementById('quantity');
quantityInput.value = parseInt(quantityInput.value) + 1;
updatePreview();
});
document.getElementById('quantity').addEventListener('change', function() {
updatePreview();
});
// 定制内容复选框事件
document.querySelectorAll('.form-check-input').forEach(checkbox => {
checkbox.addEventListener('change', function() {
updatePreview();
});
});
// 标志上传
document.getElementById('logoUpload').addEventListener('click', function() {
document.getElementById('logoFile').click();
});
document.getElementById('logoFile').addEventListener('change', function(e) {
if (e.target.files && e.target.files[0]) {
const reader = new FileReader();
reader.onload = function(event) {
const logoPreview = document.getElementById('logoPreview');
logoPreview.src = event.target.result;
logoPreview.style.display = 'block';
}
reader.readAsDataURL(e.target.files[0]);
}
});
// 选择定制按钮点击事件
document.querySelectorAll('.product-actions .btn-success').forEach(btn => {
btn.addEventListener('click', function() {
const productCard = this.closest('.product-card');
const productTitle = productCard.querySelector('.product-title').textContent;
const productPrice = productCard.querySelector('.product-price').textContent;
const productImage = productCard.querySelector('.product-image').src;
// 这里应该有切换到定制选项页面的逻辑
alert(`已选择定制产品：${productTitle}`);
});
});
// 提交定制订单按钮点击事件
document.querySelector('.preview-container .btn-success').addEventListener('click', function() {
alert('定制订单已提交，我们的客服人员将尽快与您联系确认详情！');
});
// 保存定制方案按钮点击事件
document.querySelector('.preview-container .btn-outline-secondary').addEventListener('click', function() {
alert('定制方案已保存，您可以在个人中心查看和编辑！');
});
});
// 更新预览信息
function updatePreview() {
// 获取当前选择的选项
const size = document.querySelector('.customization-option:nth-child(1) .option-item.active').textContent;
const style = document.querySelector('.customization-option:nth-child(2) .option-item.active').textContent;
const colorOption = document.querySelector('.color-option.active');
const colorName = getColorName(colorOption.style.backgroundColor);
const quantity = document.getElementById('quantity').value;
// 获取定制内容
const customContents = [];
if (document.getElementById('customLogo').checked) customContents.push('公司标志');
if (document.getElementById('customGreeting').checked) customContents.push('祝福语');
if (document.getElementById('customCard').checked) customContents.push('贺卡');
if (document.getElementById('customPackaging').checked) customContents.push('外包装');
// 更新预览信息
document.querySelector('.preview-detail:nth-child(2) .preview-detail-value').textContent = size;
document.querySelector('.preview-detail:nth-child(3) .preview-detail-value').textContent = style;
document.querySelector('.preview-detail:nth-child(4) .preview-detail-value').textContent = colorName;
document.querySelector('.preview-detail:nth-child(5) .preview-detail-value').textContent = quantity + '盒';
document.querySelector('.preview-detail:nth-child(6) .preview-detail-value').textContent = customContents.join('、');
// 更新订单摘要
document.querySelector('.summary-item:nth-child(2) .summary-item div:last-child').textContent = quantity + '盒';
// 计算小计
const unitPrice = 188;
const customFee = 1000;
const shipping = 500;
const subtotal = unitPrice * parseInt(quantity) + customFee;
const total = subtotal + shipping;
document.querySelector('.summary-item:nth-child(4) div:last-child').textContent = '¥' + subtotal.toLocaleString();
document.querySelector('.summary-total').textContent = '¥' + total.toLocaleString();
}
// 获取颜色名称
function getColorName(rgbColor) {
const colorMap = {
'rgb(139, 69, 19)': '木色',
'rgb(205, 92, 92)': '红色',
'rgb(70, 130, 180)': '蓝色',
'rgb(46, 139, 87)': '绿色',
'rgb(75, 0, 130)': '紫色',
'rgb(0, 0, 0)': '黑色'
};
return colorMap[rgbColor] || '木色';
}

// 确保导航栏在页面加载完成后可见
window.onload = function() {
    // 确保导航栏可见
    const navbarContainer = document.getElementById('navbar-container');
    if (navbarContainer) {
        navbarContainer.style.display = '';
    }
};
</script>