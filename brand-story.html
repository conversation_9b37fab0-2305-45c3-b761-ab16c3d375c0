<!DOCTYPE html> <html lang="zh-CN"> <head> <meta charset="UTF-8"> <meta name="viewport" content="width=device-width, initial-scale=1.0"> <title>品牌故事 - 智慧农业平台</title> <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet"> <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet"> <link rel="stylesheet" href="css/common.css"> <link rel="stylesheet" href="css/navbar.css"> <style>
:root {
--primary-color: #28a745;
--secondary-color: #6c757d;
--light-bg: #f8f9fa;
--card-shadow: 0 4px 6px rgba(0,0,0,0.1);
--transition: all 0.3s ease;
}
body {
background-color: var(--light-bg);
font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}
.story-header {
background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80');
background-size: cover;
background-position: center;
height: 400px;
color: white;
display: flex;
align-items: center;
margin-bottom: 30px;
border-radius: 10px;
}
.story-card {
background: white;
padding: 30px;
border-radius: 10px;
box-shadow: var(--card-shadow);
margin-bottom: 30px;
}
.story-image {
width: 100%;
border-radius: 10px;
margin-bottom: 20px;
}
.story-quote {
font-style: italic;
font-size: 1.2rem;
color: var(--secondary-color);
padding: 20px;
border-left: 4px solid var(--primary-color);
margin: 20px 0;
background-color: rgba(40, 167, 69, 0.05);
}
.story-section {
margin-bottom: 30px;
}
.story-section-title {
font-size: 1.5rem;
font-weight: bold;
margin-bottom: 15px;
color: var(--primary-color);
}
.story-section-content {
line-height: 1.8;
color: #333;
}
.timeline {
position: relative;
padding: 20px 0;
}
.timeline::before {
content: '';
position: absolute;
top: 0;
bottom: 0;
left: 50px;
width: 2px;
background-color: var(--primary-color);
}
.timeline-item {
position: relative;
padding-left: 80px;
margin-bottom: 30px;
}
.timeline-date {
position: absolute;
left: 0;
width: 50px;
text-align: center;
background-color: var(--primary-color);
color: white;
padding: 5px;
border-radius: 5px;
font-size: 0.8rem;
}
.timeline-content {
background-color: var(--light-bg);
padding: 15px;
border-radius: 10px;
position: relative;
}
.timeline-content::before {
content: '';
position: absolute;
top: 15px;
left: -10px;
width: 0;
height: 0;
border-top: 10px solid transparent;
border-bottom: 10px solid transparent;
border-right: 10px solid var(--light-bg);
}
.timeline-title {
font-weight: bold;
margin-bottom: 10px;
}
.timeline-description {
color: var(--secondary-color);
}
.founder-card {
display: flex;
align-items: center;
margin-bottom: 20px;
}
.founder-avatar {
width: 100px;
height: 100px;
border-radius: 50%;
object-fit: cover;
margin-right: 20px;
}
.founder-info {
flex: 1;
}
.founder-name {
font-weight: bold;
font-size: 1.2rem;
margin-bottom: 5px;
}
.founder-role {
color: var(--secondary-color);
margin-bottom: 10px;
}
.founder-bio {
color: #333;
line-height: 1.6;
}
.video-container {
position: relative;
padding-bottom: 56.25%; /* 16:9 */
height: 0;
overflow: hidden;
border-radius: 10px;
margin-bottom: 20px;
}
.video-container iframe {
position: absolute;
top: 0;
left: 0;
width: 100%;
height: 100%;
}
.value-card {
background-color: var(--light-bg);
padding: 20px;
border-radius: 10px;
margin-bottom: 20px;
text-align: center;
transition: var(--transition);
}
.value-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.value-icon {
font-size: 2rem;
color: var(--primary-color);
margin-bottom: 15px;
}
.value-title {
font-weight: bold;
margin-bottom: 10px;
}
.value-description {
color: var(--secondary-color);
}
.gallery {
display: grid;
grid-template-columns: repeat(3, 1fr);
gap: 15px;
margin-bottom: 20px;
}
.gallery-item {
border-radius: 10px;
overflow: hidden;
cursor: pointer;
transition: var(--transition);
}
.gallery-item:hover {
transform: scale(1.05);
}
.gallery-image {
width: 100%;
height: 200px;
object-fit: cover;
}
</style> </head> <body> <!-- 导航栏 -->
<div id="navbar-container"></div>
<!-- 主要内容 --> <div class="container py-5"> <div class="story-header"> <div class="container"> <h1 class="display-4">我们的品牌故事</h1> <p class="lead">从田间到餐桌，我们坚持传递自然、健康、可持续的农业理念</p> </div> </div> <!-- 品牌介绍 --> <div class="story-card"> <div class="row"> <div class="col-md-6"> <img src="images/brand-story1.jpg" alt="品牌故事" class="story-image"> </div> <div class="col-md-6"> <h2 class="mb-4">我们的使命</h2> <div class="story-quote">
"让每一位消费者都能享用到安全、健康、有机的农产品，同时让每一位农民都能获得公平的回报。"
</div> <p>智慧农业平台成立于2015年，是一家致力于推动农业数字化转型的科技企业。我们通过整合土地资源、农业技术和市场需求，打造从田间到餐桌的全产业链服务体系，为消费者提供优质安全的农产品，为农民创造更高的经济价值。</p> <p>我们相信，农业不仅是一种生产方式，更是一种生活态度。我们希望通过我们的努力，让更多人了解农业、尊重农业、热爱农业，共同建设一个更加健康、可持续的未来。</p> </div> </div> </div> <!-- 创始人专栏 --> <div class="story-card"> <h2 class="mb-4">创始人专栏</h2> <div class="founder-card"> <img src="images/founder.jpg" alt="创始人" class="founder-avatar"> <div class="founder-info"> <div class="founder-name">张明</div> <div class="founder-role">创始人兼CEO</div> <div class="founder-bio">
张明，浙江大学农学博士，曾在国际农业研究中心工作多年，拥有丰富的农业科研和管理经验。2015年，他带领团队创立了智慧农业平台，致力于将现代科技与传统农业相结合，推动中国农业的数字化转型。
</div> </div> </div> <div class="story-section"> <div class="story-section-title">创业初心</div> <div class="story-section-content"> <p>我出生在浙江的一个农村家庭，从小就对农业有着深厚的感情。在国外学习和工作期间，我看到了发达国家现代农业的发展模式，也深刻认识到中国农业面临的挑战和机遇。</p> <p>回国后，我发现中国的农业存在着诸多问题：小农经济效率低下，农产品质量参差不齐，农民收入增长缓慢，年轻人不愿意从事农业生产……这些问题让我深感忧虑，也激发了我的创业热情。</p> <p>我相信，通过科技的力量，我们可以改变传统农业的生产方式，提高农业生产效率，保障农产品质量安全，增加农民收入，吸引更多年轻人回归农业。这就是我创立智慧农业平台的初心。</p> </div> </div> <div class="story-section"> <div class="story-section-title">未来展望</div> <div class="story-section-content"> <p>展望未来，我们将继续坚持"科技赋能农业"的理念，不断创新和探索。我们计划在全国建立更多的智慧农业基地，推广更多的优质农产品品牌，服务更多的农民和消费者。</p> <p>我们也将积极参与乡村振兴战略，为中国农业的现代化发展贡献力量。我相信，在不久的将来，中国的农业将迎来一个全新的时代，而我们智慧农业平台将成为这个时代的引领者和推动者。</p> </div> </div> </div> <!-- 品牌发展历程 --> <div class="story-card"> <h2 class="mb-4">品牌发展历程</h2> <div class="timeline"> <div class="timeline-item"> <div class="timeline-date">2015</div> <div class="timeline-content"> <div class="timeline-title">品牌创立</div> <div class="timeline-description">
智慧农业平台正式成立，开始在浙江省嘉兴市建立第一个智慧农业基地。
</div> </div> </div> <div class="timeline-item"> <div class="timeline-date">2016</div> <div class="timeline-content"> <div class="timeline-title">技术突破</div> <div class="timeline-description">
研发出智能灌溉系统和农作物生长监测系统，大幅提高了农业生产效率。
</div> </div> </div> <div class="timeline-item"> <div class="timeline-date">2017</div> <div class="timeline-content"> <div class="timeline-title">品牌扩张</div> <div class="timeline-description">
在江苏、安徽等地建立新的智慧农业基地，产品线扩展到水稻、蔬菜、水果等多个品类。
</div> </div> </div> <div class="timeline-item"> <div class="timeline-date">2018</div> <div class="timeline-content"> <div class="timeline-title">获得认证</div> <div class="timeline-description">
所有基地通过有机认证和绿色食品认证，产品质量得到权威保障。
</div> </div> </div> <div class="timeline-item"> <div class="timeline-date">2019</div> <div class="timeline-content"> <div class="timeline-title">线上平台</div> <div class="timeline-description">
推出智慧农业在线平台，实现土地租赁、农产品销售、农业服务等全方位线上化。
</div> </div> </div> <div class="timeline-item"> <div class="timeline-date">2020</div> <div class="timeline-content"> <div class="timeline-title">抗疫贡献</div> <div class="timeline-description">
疫情期间保障农产品供应，向医护人员捐赠有机蔬菜，彰显企业社会责任。
</div> </div> </div> <div class="timeline-item"> <div class="timeline-date">2021</div> <div class="timeline-content"> <div class="timeline-title">品牌升级</div> <div class="timeline-description">
推出高端有机农产品品牌"田园臻品"，进入高端农产品市场。
</div> </div> </div> <div class="timeline-item"> <div class="timeline-date">2022</div> <div class="timeline-content"> <div class="timeline-title">国际合作</div> <div class="timeline-description">
与国际农业研究机构合作，引进先进农业技术和管理经验。
</div> </div> </div> <div class="timeline-item"> <div class="timeline-date">2023</div> <div class="timeline-content"> <div class="timeline-title">全面升级</div> <div class="timeline-description">
推出智慧农业平台2.0版本，整合六大模块，提供更全面的农业数字化解决方案。
</div> </div> </div> </div> </div> <!-- 品牌视频 --> <div class="story-card"> <h2 class="mb-4">品牌视频</h2> <div class="video-container"> <iframe src="https://www.youtube.com/embed/dQw4w9WgXcQ" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe> </div> <p class="text-center text-muted">《从田间到餐桌：智慧农业的探索之旅》</p> </div> <!-- 品牌价值观 --> <div class="story-card"> <h2 class="mb-4">品牌价值观</h2> <div class="row"> <div class="col-md-4"> <div class="value-card"> <i class="fas fa-leaf value-icon"></i> <div class="value-title">自然与健康</div> <div class="value-description">
我们尊重自然规律，采用有机种植方式，为消费者提供健康安全的农产品。
</div> </div> </div> <div class="col-md-4"> <div class="value-card"> <i class="fas fa-handshake value-icon"></i> <div class="value-title">公平与共赢</div> <div class="value-description">
我们追求与农民、消费者、合作伙伴的公平合作，实现多方共赢。
</div> </div> </div> <div class="col-md-4"> <div class="value-card"> <i class="fas fa-lightbulb value-icon"></i> <div class="value-title">创新与进步</div> <div class="value-description">
我们不断创新技术和模式，推动农业的数字化转型和可持续发展。
</div> </div> </div> </div> </div> <!-- 品牌图片展示 --> <div class="story-card"> <h2 class="mb-4">品牌图片展示</h2> <div class="gallery"> <div class="gallery-item"> <img src="images/gallery1.jpg" alt="农场图片" class="gallery-image"> </div> <div class="gallery-item"> <img src="images/gallery2.jpg" alt="农场图片" class="gallery-image"> </div> <div class="gallery-item"> <img src="images/gallery3.jpg" alt="农场图片" class="gallery-image"> </div> <div class="gallery-item"> <img src="images/gallery4.jpg" alt="农场图片" class="gallery-image"> </div> <div class="gallery-item"> <img src="images/gallery5.jpg" alt="农场图片" class="gallery-image"> </div> <div class="gallery-item"> <img src="images/gallery6.jpg" alt="农场图片" class="gallery-image"> </div> </div> </div> <!-- 联系我们 --> <div class="story-card"> <h2 class="mb-4">联系我们</h2> <div class="row"> <div class="col-md-6"> <p><i class="fas fa-map-marker-alt me-2 text-success"></i> 地址：浙江省杭州市西湖区文三路188号</p> <p><i class="fas fa-phone me-2 text-success"></i> 电话：0571-88888888</p> <p><i class="fas fa-envelope me-2 text-success"></i> 邮箱：<EMAIL></p> <p><i class="fas fa-globe me-2 text-success"></i> 网站：www.smartagri.com</p> </div> <div class="col-md-6"> <p>关注我们的社交媒体：</p> <div class="d-flex gap-3"> <a href="#" class="text-success fs-3"><i class="fab fa-weixin"></i></a> <a href="#" class="text-success fs-3"><i class="fab fa-weibo"></i></a> <a href="#" class="text-success fs-3"><i class="fab fa-tiktok"></i></a> <a href="#" class="text-success fs-3"><i class="fab fa-youtube"></i></a> </div> </div> </div> </div> </div> <!-- 页脚 -->
<div id="footer-container"></div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="js/login-config.js"></script>
<script src="js/components.js"></script>

 <script>
// 使用 common.js 加载导航栏
// 图片点击放大效果
document.querySelectorAll('.gallery-item').forEach(item => {
item.addEventListener('click', function() {
const imgSrc = this.querySelector('img').src;
// 这里可以实现图片放大查看的功能
// 例如使用模态框或者lightbox插件
alert('图片查看功能即将上线');
});
});
</script> </body> </html>
