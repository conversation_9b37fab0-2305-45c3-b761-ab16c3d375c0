<!DOCTYPE html> <html lang="zh-CN"> <head> <meta charset="UTF-8"> <meta name="viewport" content="width=device-width, initial-scale=1.0"> <title>认证系统 - 智慧农业平台</title> <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet"> <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet"> <link rel="stylesheet" href="css/common.css"> <link rel="stylesheet" href="css/navbar.css"> <style>
:root {
--primary-color: #28a745;
--secondary-color: #6c757d;
--light-bg: #f8f9fa;
--card-shadow: 0 4px 6px rgba(0,0,0,0.1);
--transition: all 0.3s ease;
}
body {
background-color: var(--light-bg);
font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}
.certification-header {
background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80');
background-size: cover;
background-position: center;
height: 300px;
color: white;
display: flex;
align-items: center;
margin-bottom: 30px;
border-radius: 10px;
}
.certification-card {
background: white;
padding: 30px;
border-radius: 10px;
box-shadow: var(--card-shadow);
margin-bottom: 30px;
}
.certificate-row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
}
.certificate-col {
    width: 50%;
    padding: 0 15px;
    margin-bottom: 30px;
}
.certificate-item {
    display: flex;
    height: 100%;
    padding: 20px;
    border-radius: 10px;
    background-color: var(--light-bg);
    transition: var(--transition);
}
.certificate-item:hover {
    box-shadow: var(--card-shadow);
    transform: translateY(-5px);
}
.certificate-icon {
    width: 60px;
    height: 60px;
    background-color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    color: var(--primary-color);
    font-size: 1.5rem;
    flex-shrink: 0;
}
.certificate-content {
    flex: 1;
}
.certificate-title {
font-size: 1.1rem;
font-weight: bold;
margin-bottom: 5px;
color: var(--primary-color);
}
.certificate-description {
color: var(--secondary-color);
margin-bottom: 10px;
line-height: 1.4;
font-size: 0.85rem;
}
.certificate-status {
display: inline-block;
padding: 3px 8px;
border-radius: 12px;
font-size: 0.75rem;
margin-bottom: 10px;
}
.status-valid {
background-color: rgba(40, 167, 69, 0.1);
color: var(--primary-color);
border: 1px solid var(--primary-color);
}
.status-expired {
background-color: rgba(220, 53, 69, 0.1);
color: #dc3545;
border: 1px solid #dc3545;
}
.status-pending {
background-color: rgba(255, 193, 7, 0.1);
color: #ffc107;
border: 1px solid #ffc107;
}
.certificate-detail {
display: flex;
margin-bottom: 8px;
flex-wrap: wrap;
align-items: center;
}
.certificate-detail-label {
min-width: 80px;
font-weight: bold;
color: var(--secondary-color);
flex-shrink: 0;
margin-right: 5px;
font-size: 0.9rem;
}
.certificate-detail-value {
flex: 1;
font-weight: 500;
font-size: 0.9rem;
}
.certificate-actions {
margin-top: 10px;
}
.certificate-actions .btn {
font-size: 0.75rem;
padding: 0.25rem 0.5rem;
}
.certificate-image {
width: 100%;
border-radius: 10px;
margin-bottom: 20px;
}
.verification-card {
background: var(--light-bg);
padding: 20px;
border-radius: 10px;
margin-bottom: 20px;
}
.verification-title {
font-weight: bold;
margin-bottom: 15px;
}
.verification-form {
margin-bottom: 15px;
}
.verification-result {
padding: 15px;
border-radius: 10px;
margin-top: 15px;
display: none;
}
.verification-success {
background-color: rgba(40, 167, 69, 0.1);
border: 1px solid var(--primary-color);
}
.verification-error {
background-color: rgba(220, 53, 69, 0.1);
border: 1px solid #dc3545;
}
.step-indicator {
display: flex;
justify-content: space-between;
margin-bottom: 30px;
}
.step {
flex: 1;
text-align: center;
padding: 15px;
position: relative;
}
.step::after {
content: '';
position: absolute;
top: 50%;
right: 0;
width: 100%;
height: 2px;
background-color: #ddd;
transform: translateY(-50%);
z-index: -1;
}
.step:last-child::after {
display: none;
}
.step-number {
width: 40px;
height: 40px;
background-color: #ddd;
border-radius: 50%;
display: flex;
align-items: center;
justify-content: center;
margin: 0 auto 10px;
font-weight: bold;
color: white;
position: relative;
z-index: 1;
}
.step.active .step-number {
background-color: var(--primary-color);
}
.step.completed .step-number {
background-color: var(--primary-color);
}
.step-title {
font-size: 0.9rem;
color: var(--secondary-color);
}
.step.active .step-title {
color: var(--primary-color);
font-weight: bold;
}
.reminder-card {
background-color: rgba(255, 193, 7, 0.1);
border-left: 4px solid #ffc107;
padding: 15px;
margin-bottom: 20px;
border-radius: 0 5px 5px 0;
}
.reminder-title {
font-weight: bold;
margin-bottom: 10px;
display: flex;
align-items: center;
}
.reminder-icon {
color: #ffc107;
margin-right: 10px;
}
.reminder-content {
color: var(--secondary-color);
}
.reminder-actions {
margin-top: 15px;
}
</style> </head> <body>
<!-- 导航栏 -->
<div id="navbar-container"></div>
<!-- 主要内容 --> <div class="container py-5"> <div class="certification-header"> <div class="container"> <h1>认证系统</h1> <p class="lead">管理和查询您的农产品认证，提升产品价值和消费者信任</p> </div> </div> <!-- 认证概览 --> <div class="certification-card"> <div class="d-flex justify-content-between align-items-center mb-4"> <h3>认证概览</h3> <button class="btn btn-success"><i class="fas fa-plus me-2"></i> 申请新认证</button> </div> <div class="row mb-4"> <div class="col-md-3"> <div class="card text-center p-3"> <div class="fs-1 text-success mb-2">5</div> <div>有效认证</div> </div> </div> <div class="col-md-3"> <div class="card text-center p-3"> <div class="fs-1 text-warning mb-2">1</div> <div>即将到期</div> </div> </div> <div class="col-md-3"> <div class="card text-center p-3"> <div class="fs-1 text-danger mb-2">2</div> <div>已过期</div> </div> </div> <div class="col-md-3"> <div class="card text-center p-3"> <div class="fs-1 text-info mb-2">1</div> <div>审核中</div> </div> </div> </div> </div> <!-- 认证列表 --> <div class="certification-card">
<h3 class="mb-4">我的认证</h3>
<div class="certificate-row">
    <!-- 有机认证 -->
    <div class="certificate-col">
        <div class="certificate-item">
            <div class="certificate-icon">
                <i class="fas fa-leaf"></i>
            </div>
            <div class="certificate-content">
                <div class="certificate-title">有机食品认证</div>
                <div class="certificate-description">证明产品符合有机农业生产标准，不使用化学合成的农药、化肥、生长调节剂等物质</div>
                <div class="certificate-status status-valid">有效</div>
                <div class="certificate-detail">
                    <div class="certificate-detail-label">认证编号：</div>
                    <div class="certificate-detail-value">ORG-**********</div>
                </div>
                <div class="certificate-detail">
                    <div class="certificate-detail-label">认证机构：</div>
                    <div class="certificate-detail-value">中国有机食品认证中心</div>
                </div>
                <div class="certificate-detail">
                    <div class="certificate-detail-label">认证日期：</div>
                    <div class="certificate-detail-value">2023-01-10</div>
                </div>
                <div class="certificate-detail">
                    <div class="certificate-detail-label">有效期至：</div>
                    <div class="certificate-detail-value">2026-01-09</div>
                </div>
                <div class="certificate-actions">
                    <button class="btn btn-sm btn-outline-success me-1">查看详情</button>
                    <button class="btn btn-sm btn-outline-secondary me-1">下载证书</button>
                    <button class="btn btn-sm btn-outline-primary">续期申请</button>
                </div>
            </div>
        </div>
    </div>
    <!-- 绿色食品认证 -->
    <div class="certificate-col">
        <div class="certificate-item">
            <div class="certificate-icon">
                <i class="fas fa-seedling"></i>
            </div>
            <div class="certificate-content">
                <div class="certificate-title">绿色食品认证</div>
                <div class="certificate-description">证明产品符合绿色食品标准，安全、优质、营养</div>
                <div class="certificate-status status-valid">有效</div>
                <div class="certificate-detail">
                    <div class="certificate-detail-label">认证编号：</div>
                    <div class="certificate-detail-value">GF-**********</div>
                </div>
                <div class="certificate-detail">
                    <div class="certificate-detail-label">认证机构：</div>
                    <div class="certificate-detail-value">中国绿色食品发展中心</div>
                </div>
                <div class="certificate-detail">
                    <div class="certificate-detail-label">认证日期：</div>
                    <div class="certificate-detail-value">2022-12-05</div>
                </div>
                <div class="certificate-detail">
                    <div class="certificate-detail-label">有效期至：</div>
                    <div class="certificate-detail-value">2025-12-04</div>
                </div>
                <div class="certificate-actions">
                    <button class="btn btn-sm btn-outline-success me-1">查看详情</button>
                    <button class="btn btn-sm btn-outline-secondary me-1">下载证书</button>
                    <button class="btn btn-sm btn-outline-primary">续期申请</button>
                </div>
            </div>
        </div>
    </div>
    <!-- 无公害农产品认证 -->
    <div class="certificate-col">
        <div class="certificate-item">
            <div class="certificate-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="certificate-content">
                <div class="certificate-title">无公害农产品认证</div>
                <div class="certificate-description">证明产品符合无公害农产品标准，安全、卫生</div>
                <div class="certificate-status status-warning">即将到期</div>
                <div class="certificate-detail">
                    <div class="certificate-detail-label">认证编号：</div>
                    <div class="certificate-detail-value">WGH-CN-2021089</div>
                </div>
                <div class="certificate-detail">
                    <div class="certificate-detail-label">认证机构：</div>
                    <div class="certificate-detail-value">中国农产品质量安全中心</div>
                </div>
                <div class="certificate-detail">
                    <div class="certificate-detail-label">认证日期：</div>
                    <div class="certificate-detail-value">2021-06-15</div>
                </div>
                <div class="certificate-detail">
                    <div class="certificate-detail-label">有效期至：</div>
                    <div class="certificate-detail-value">2023-06-14</div>
                </div>
                <div class="certificate-actions">
                    <button class="btn btn-sm btn-outline-success me-1">查看详情</button>
                    <button class="btn btn-sm btn-outline-secondary me-1">下载证书</button>
                    <button class="btn btn-sm btn-outline-primary">续期申请</button>
                </div>
            </div>
        </div>
    </div>
    <!-- 地理标志产品认证 -->
    <div class="certificate-col">
        <div class="certificate-item">
            <div class="certificate-icon">
                <i class="fas fa-map-marker-alt"></i>
            </div>
            <div class="certificate-content">
                <div class="certificate-title">地理标志产品认证</div>
                <div class="certificate-description">证明产品原产于特定地域，具有特定品质和声誉</div>
                <div class="certificate-status status-expired">已过期</div>
                <div class="certificate-detail">
                    <div class="certificate-detail-label">认证编号：</div>
                    <div class="certificate-detail-value">GI-**********</div>
                </div>
                <div class="certificate-detail">
                    <div class="certificate-detail-label">认证机构：</div>
                    <div class="certificate-detail-value">国家知识产权局</div>
                </div>
                <div class="certificate-detail">
                    <div class="certificate-detail-label">认证日期：</div>
                    <div class="certificate-detail-value">2020-03-20</div>
                </div>
                <div class="certificate-detail">
                    <div class="certificate-detail-label">有效期至：</div>
                    <div class="certificate-detail-value">2022-03-19</div>
                </div>
                <div class="certificate-actions">
                    <button class="btn btn-sm btn-outline-success me-1">查看详情</button>
                    <button class="btn btn-sm btn-outline-secondary me-1">下载证书</button>
                    <button class="btn btn-sm btn-outline-primary">重新申请</button>
                </div>
            </div>
        </div>
    </div>
    <!-- 良好农业规范认证 -->
    <div class="certificate-col">
        <div class="certificate-item">
            <div class="certificate-icon">
                <i class="fas fa-tractor"></i>
            </div>
            <div class="certificate-content">
                <div class="certificate-title">良好农业规范认证 (GAP)</div>
                <div class="certificate-description">证明农产品生产过程符合良好农业规范，保障食品安全和环境保护</div>
                <div class="certificate-status status-pending">审核中</div>
                <div class="certificate-detail">
                    <div class="certificate-detail-label">申请编号：</div>
                    <div class="certificate-detail-value">GAP-CN-2023045</div>
                </div>
                <div class="certificate-detail">
                    <div class="certificate-detail-label">认证机构：</div>
                    <div class="certificate-detail-value">中国质量认证中心</div>
                </div>
                <div class="certificate-detail">
                    <div class="certificate-detail-label">申请日期：</div>
                    <div class="certificate-detail-value">2023-05-10</div>
                </div>
                <div class="certificate-detail">
                    <div class="certificate-detail-label">预计完成：</div>
                    <div class="certificate-detail-value">2023-07-10</div>
                </div>
                <div class="certificate-actions">
                    <button class="btn btn-sm btn-outline-success me-1">查看进度</button>
                    <button class="btn btn-sm btn-outline-secondary me-1">补充材料</button>
                    <button class="btn btn-sm btn-outline-danger">取消申请</button>
                </div>
            </div>
        </div>
    </div>
    <!-- 空白位置，保持偶数列 -->
    <div class="certificate-col"></div>
</div>
</div>

<!-- 认证申请 --> <div class="certification-card"> <h3 class="mb-4">申请新认证</h3> <div class="step-indicator mb-5"> <div class="step active"> <div class="step-number">1</div> <div class="step-title">选择认证类型</div> </div> <div class="step"> <div class="step-number">2</div> <div class="step-title">填写基本信息</div> </div> <div class="step"> <div class="step-number">3</div> <div class="step-title">上传材料</div> </div> <div class="step"> <div class="step-number">4</div> <div class="step-title">现场检查</div> </div> <div class="step"> <div class="step-number">5</div> <div class="step-title">审核发证</div> </div> </div> <div class="row mb-4"> <div class="col-md-4 mb-3"> <div class="card h-100"> <div class="card-body text-center"> <i class="fas fa-leaf fa-3x text-success mb-3"></i> <h5 class="card-title">有机食品认证</h5> <p class="card-text">证明产品符合有机农业生产标准</p> <button class="btn btn-outline-success">申请认证</button> </div> </div> </div> <div class="col-md-4 mb-3"> <div class="card h-100"> <div class="card-body text-center"> <i class="fas fa-seedling fa-3x text-success mb-3"></i> <h5 class="card-title">绿色食品认证</h5> <p class="card-text">证明产品符合绿色食品标准</p> <button class="btn btn-outline-success">申请认证</button> </div> </div> </div> <div class="col-md-4 mb-3"> <div class="card h-100"> <div class="card-body text-center"> <i class="fas fa-check-circle fa-3x text-success mb-3"></i> <h5 class="card-title">无公害农产品认证</h5> <p class="card-text">证明产品符合无公害农产品标准</p> <button class="btn btn-outline-success">申请认证</button> </div> </div> </div> <div class="col-md-4 mb-3"> <div class="card h-100"> <div class="card-body text-center"> <i class="fas fa-map-marker-alt fa-3x text-success mb-3"></i> <h5 class="card-title">地理标志产品认证</h5> <p class="card-text">证明产品原产于特定地域</p> <button class="btn btn-outline-success">申请认证</button> </div> </div> </div> <div class="col-md-4 mb-3"> <div class="card h-100"> <div class="card-body text-center"> <i class="fas fa-tractor fa-3x text-success mb-3"></i> <h5 class="card-title">良好农业规范认证</h5> <p class="card-text">证明农产品生产过程符合良好农业规范</p> <button class="btn btn-outline-success">申请认证</button> </div> </div> </div> <div class="col-md-4 mb-3"> <div class="card h-100"> <div class="card-body text-center"> <i class="fas fa-globe fa-3x text-success mb-3"></i> <h5 class="card-title">国际认证</h5> <p class="card-text">包括欧盟有机认证、美国有机认证等</p> <button class="btn btn-outline-success">申请认证</button> </div> </div> </div> </div> </div> <!-- 认证验证 --> <div class="certification-card"> <h3 class="mb-4">认证验证</h3> <div class="row"> <div class="col-md-6"> <div class="verification-card"> <div class="verification-title">扫码验证</div> <p class="text-muted">扫描产品上的认证二维码，验证认证真伪</p> <div class="text-center py-4"> <i class="fas fa-qrcode fa-5x text-secondary mb-3"></i> <div> <button class="btn btn-success"><i class="fas fa-camera me-2"></i> 打开相机扫码</button> </div> </div> </div> </div> <div class="col-md-6"> <div class="verification-card"> <div class="verification-title">证书编号验证</div> <p class="text-muted">输入认证证书编号，验证认证真伪</p> <div class="verification-form"> <div class="mb-3"> <label for="certificateType" class="form-label">认证类型</label> <select class="form-select" id="certificateType"> <option value="">请选择认证类型</option> <option value="organic">有机食品认证</option> <option value="green">绿色食品认证</option> <option value="pollution-free">无公害农产品认证</option> <option value="geographical">地理标志产品认证</option> <option value="gap">良好农业规范认证</option> </select> </div> <div class="mb-3"> <label for="certificateNumber" class="form-label">证书编号</label> <input type="text" class="form-control" id="certificateNumber" placeholder="请输入认证证书编号"> </div> <button class="btn btn-success w-100" onclick="verifyCertificate()">验证</button> </div> <div class="verification-result verification-success" id="verificationSuccess"> <div class="d-flex align-items-center mb-2"> <i class="fas fa-check-circle text-success me-2"></i> <strong>验证成功</strong> </div> <p class="mb-0">该认证证书有效，由中国有机食品认证中心颁发，有效期至 2026-01-09。</p> </div> <div class="verification-result verification-error" id="verificationError"> <div class="d-flex align-items-center mb-2"> <i class="fas fa-times-circle text-danger me-2"></i> <strong>验证失败</strong> </div> <p class="mb-0">未找到该认证证书信息，请检查证书编号是否正确。</p> </div> </div> </div> </div> </div> <!-- 到期提醒 --> <div class="certification-card"> <h3 class="mb-4">认证到期提醒</h3> <div class="reminder-card"> <div class="reminder-title"> <i class="fas fa-exclamation-triangle reminder-icon"></i> <span>无公害农产品认证即将到期</span> </div> <div class="reminder-content"> <p>您的无公害农产品认证（编号：WGH-CN-2021089）将于 2023-06-14 到期，请及时申请续期以避免认证失效。</p> </div> <div class="reminder-actions"> <button class="btn btn-warning">立即申请续期</button> </div> </div> <div class="reminder-card mt-3"> <div class="reminder-title"> <i class="fas fa-exclamation-triangle reminder-icon"></i> <span>地理标志产品认证已过期</span> </div> <div class="reminder-content"> <p>您的地理标志产品认证（编号：GI-**********）已于 2022-03-19 到期，请尽快重新申请以恢复认证有效性。</p> </div> <div class="reminder-actions"> <button class="btn btn-danger">重新申请认证</button> </div> </div> </div> </div> <!-- 页脚 -->
<div id="footer-container"></div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="js/login-config.js"></script>
<script src="js/components.js"></script>

 <script>
// 使用 common.js 加载导航栏
// 认证验证功能
function verifyCertificate() {
const certificateType = document.getElementById('certificateType').value;
const certificateNumber = document.getElementById('certificateNumber').value;
// 验证表单
if (!certificateType) {
alert('请选择认证类型');
return;
}
if (!certificateNumber) {
alert('请输入证书编号');
return;
}
// 模拟验证结果
// 在实际应用中，这里应该是一个后端 API 调用
const successElement = document.getElementById('verificationSuccess');
const errorElement = document.getElementById('verificationError');
// 模拟成功的情况
if (certificateType === 'organic' && certificateNumber === 'ORG-**********') {
successElement.style.display = 'block';
errorElement.style.display = 'none';
} else {
successElement.style.display = 'none';
errorElement.style.display = 'block';
}
}
// 初始化页面
document.addEventListener('DOMContentLoaded', function() {
// 隐藏验证结果
document.getElementById('verificationSuccess').style.display = 'none';
document.getElementById('verificationError').style.display = 'none';
// 认证申请按钮点击事件
document.querySelectorAll('.card .btn-outline-success').forEach(btn => {
btn.addEventListener('click', function() {
const cardTitle = this.closest('.card').querySelector('.card-title').textContent;
alert(`即将开始申请${cardTitle}，请准备相关材料。`);
});
});
// 认证详情按钮点击事件
document.querySelectorAll('.certificate-actions .btn-outline-success').forEach(btn => {
btn.addEventListener('click', function() {
const certificateTitle = this.closest('.certificate-content').querySelector('.certificate-title').textContent;
alert(`查看${certificateTitle}详情`);
});
});
// 下载证书按钮点击事件
document.querySelectorAll('.certificate-actions .btn-outline-secondary').forEach(btn => {
btn.addEventListener('click', function() {
const certificateTitle = this.closest('.certificate-content').querySelector('.certificate-title').textContent;
alert(`下载${certificateTitle}证书`);
});
});
// 续期申请按钮点击事件
document.querySelectorAll('.certificate-actions .btn-outline-primary').forEach(btn => {
btn.addEventListener('click', function() {
const certificateTitle = this.closest('.certificate-content').querySelector('.certificate-title').textContent;
alert(`申请${certificateTitle}续期`);
});
});
// 到期提醒按钮点击事件
document.querySelectorAll('.reminder-actions .btn').forEach(btn => {
btn.addEventListener('click', function() {
const reminderTitle = this.closest('.reminder-card').querySelector('.reminder-title span').textContent;
alert(`处理${reminderTitle}`);
});
});
});

// 确保导航栏在页面加载完成后可见

// 页面加载完成后确保导航栏可见
window.onload = function() {
    // 确保导航栏可见
    const navbarContainer = document.getElementById('navbar-container');
    if (navbarContainer) {
        // 强制设置为可见
        navbarContainer.style.display = 'block';
        
        // 确保导航栏内容已加载
        if (!navbarContainer.innerHTML || navbarContainer.innerHTML.trim() === '') {
            // 如果导航栏内容为空，尝试重新加载
            if (typeof navbarHTML !== 'undefined') {
                navbarContainer.innerHTML = navbarHTML;
                
                // 重新应用登录状态和模块可见性
                if (typeof loadLoginState === 'function') loadLoginState();
                if (typeof updateNavbarDisplay === 'function') updateNavbarDisplay();
                if (typeof setActiveNavItem === 'function') setActiveNavItem();
                if (typeof setupLoginEvents === 'function') setupLoginEvents();
            }
        }
    }
};
</script> </body> </html>
