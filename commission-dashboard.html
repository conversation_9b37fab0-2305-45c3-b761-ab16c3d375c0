<!DOCTYPE html> <html lang="zh-CN"> <head> <meta charset="UTF-8"> <meta name="viewport" content="width=device-width, initial-scale=1.0"> <title>分销佣金可视化看板 - 智慧农业平台</title> <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet"> <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet"> <link rel="stylesheet" href="css/common.css"> <link rel="stylesheet" href="css/navbar.css"> <style>
:root {
--primary-color: #28a745;
--secondary-color: #6c757d;
--light-bg: #f8f9fa;
--card-shadow: 0 4px 6px rgba(0,0,0,0.1);
--transition: all 0.3s ease;
--distribution-color: #8e44ad;
--level1-color: #3498db;
--level2-color: #e67e22;
--chart-color1: #3498db;
--chart-color2: #e67e22;
--chart-color3: #2ecc71;
--chart-color4: #9b59b6;
--chart-color5: #f1c40f;
}
body {
background-color: var(--light-bg);
font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}
.commission-header {
background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80');
background-size: cover;
background-position: center;
height: 200px;
color: white;
display: flex;
align-items: center;
margin-bottom: 30px;
border-radius: 10px;
}
.commission-card {
background: white;
padding: 20px;
border-radius: 10px;
box-shadow: var(--card-shadow);
margin-bottom: 30px;
min-height: auto;
}
.stat-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 20px;
transition: var(--transition);
}
.stat-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.stat-icon {
font-size: 2rem;
color: var(--distribution-color);
margin-bottom: 10px;
}
.stat-value {
font-size: 1.5rem;
font-weight: bold;
color: var(--distribution-color);
margin-bottom: 5px;
}
.stat-label {
font-size: 0.9rem;
color: var(--secondary-color);
}
.chart-container {
position: relative;
height: 300px;
width: 100%;
margin-bottom: 20px;
}
.filter-btn {
padding: 5px 15px;
border-radius: 20px;
background-color: white;
border: 1px solid #ddd;
margin-right: 10px;
margin-bottom: 10px;
cursor: pointer;
transition: var(--transition);
}
.filter-btn.active {
background-color: var(--primary-color);
color: white;
border-color: var(--primary-color);
}
.filter-btn:hover:not(.active) {
background-color: #f0f0f0;
}
.commission-table {
margin-bottom: 20px;
}
.table-responsive {
max-height: 400px;
overflow-y: auto;
}
.table th {
position: sticky;
top: 0;
background-color: #f8f9fa;
z-index: 1;
}
.table-hover tbody tr:hover {
background-color: rgba(142, 68, 173, 0.05);
}
.status-badge {
display: inline-block;
padding: 3px 8px;
border-radius: 15px;
font-size: 0.8rem;
}
.status-pending {
background-color: rgba(255, 193, 7, 0.1);
color: #ffc107;
}
.status-paid {
background-color: rgba(40, 167, 69, 0.1);
color: var(--primary-color);
}
.status-processing {
background-color: rgba(13, 202, 240, 0.1);
color: #0dcaf0;
}
.rank-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 15px;
transition: var(--transition);
}
.rank-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.rank-header {
display: flex;
justify-content: space-between;
align-items: center;
margin-bottom: 15px;
}
.rank-title {
font-weight: bold;
display: flex;
align-items: center;
}
.rank-icon {
color: var(--distribution-color);
margin-right: 10px;
}
.rank-item {
display: flex;
align-items: center;
padding: 10px;
border-radius: 5px;
margin-bottom: 10px;
background-color: white;
transition: var(--transition);
}
.rank-item:hover {
transform: translateX(5px);
box-shadow: var(--card-shadow);
}
.rank-number {
width: 30px;
height: 30px;
border-radius: 50%;
display: flex;
align-items: center;
justify-content: center;
margin-right: 15px;
font-weight: bold;
flex-shrink: 0;
}
.rank-1 {
background-color: #f1c40f;
color: white;
}
.rank-2 {
background-color: #bdc3c7;
color: white;
}
.rank-3 {
background-color: #e67e22;
color: white;
}
.rank-other {
background-color: #ecf0f1;
color: var(--secondary-color);
}
.rank-avatar {
width: 40px;
height: 40px;
border-radius: 50%;
object-fit: cover;
margin-right: 15px;
}
.rank-info {
flex: 1;
}
.rank-name {
font-weight: bold;
margin-bottom: 3px;
}
.rank-role {
font-size: 0.8rem;
color: var(--secondary-color);
}
.rank-value {
font-weight: bold;
color: var(--distribution-color);
}
.progress-container {
height: 8px;
background-color: #e9ecef;
border-radius: 4px;
margin-bottom: 5px;
overflow: hidden;
}
.progress-bar {
min-height: auto;
border-radius: 4px;
}
.progress-level1 {
background-color: var(--level1-color);
}
.progress-level2 {
background-color: var(--level2-color);
}
.progress-text {
display: flex;
justify-content: space-between;
font-size: 0.8rem;
color: var(--secondary-color);
}
.withdrawal-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 15px;
transition: var(--transition);
}
.withdrawal-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.withdrawal-header {
display: flex;
justify-content: space-between;
align-items: center;
margin-bottom: 15px;
}
.withdrawal-title {
font-weight: bold;
display: flex;
align-items: center;
}
.withdrawal-icon {
color: var(--distribution-color);
margin-right: 10px;
}
.withdrawal-info {
display: flex;
justify-content: space-between;
margin-bottom: 10px;
}
.withdrawal-label {
color: var(--secondary-color);
}
.withdrawal-value {
font-weight: bold;
}
.withdrawal-history {
margin-top: 15px;
}
.history-item {
display: flex;
justify-content: space-between;
padding: 10px;
border-bottom: 1px solid #eee;
}
.history-item:last-child {
border-bottom: none;
}
.history-date {
color: var(--secondary-color);
font-size: 0.9rem;
}
.history-amount {
font-weight: bold;
}
.history-status {
font-size: 0.9rem;
}
.status-success {
color: var(--primary-color);
}
.status-waiting {
color: #ffc107;
}
.faq-item {
margin-bottom: 15px;
}
.faq-question {
font-weight: bold;
margin-bottom: 5px;
cursor: pointer;
display: flex;
justify-content: space-between;
align-items: center;
}
.faq-answer {
color: var(--secondary-color);
display: none;
padding: 10px 0;
}
.faq-answer.active {
display: block;
}
</style> </head> <body> <!-- 导航栏 -->
<div id="navbar-container"></div>
<!-- 主要内容 --> <div class="container py-5"> <div class="commission-header"> <div class="container"> <h1>分销佣金可视化看板</h1> <p class="lead">实时查看分销佣金数据，了解销售业绩和佣金规则，优化分销策略，提高销售效率</p> </div> </div> <!-- 顶部统计卡片 --> <div class="row mb-4"> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-yen-sign stat-icon"></i> <div class="stat-value">128,560</div> <div class="stat-label">本月总佣金</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-shopping-cart stat-icon"></i> <div class="stat-value">1,256</div> <div class="stat-label">本月订单数</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-chart-line stat-icon"></i> <div class="stat-value">+15.6%</div> <div class="stat-label">环比增长率</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-users stat-icon"></i> <div class="stat-value">256</div> <div class="stat-label">活跃分销商</div> </div> </div> </div> <!-- 主要内容区域 --> <div class="row"> <!-- 左侧内容 --> <div class="col-md-8"> <!-- 佣金趋势图 --> <div class="commission-card"> <div class="d-flex justify-content-between align-items-center mb-4"> <h4>佣金趋势</h4> <div> <button class="filter-btn">日</button> <button class="filter-btn">周</button> <button class="filter-btn active">月</button> <button class="filter-btn">年</button> </div> </div> <div class="chart-container"> <canvas id="commissionTrendChart"></canvas> </div> <div class="d-flex justify-content-between"> <div> <div class="form-check form-check-inline"> <input class="form-check-input" type="checkbox" id="showLevel1" checked> <label class="form-check-label" for="showLevel1">一级分销商</label> </div> <div class="form-check form-check-inline"> <input class="form-check-input" type="checkbox" id="showLevel2" checked> <label class="form-check-label" for="showLevel2">二级分销商</label> </div> </div> <button class="btn btn-sm btn-outline-success"><i class="fas fa-download me-1"></i>导出数据</button> </div> </div> <!-- 佣金分布图 --> <div class="commission-card"> <div class="d-flex justify-content-between align-items-center mb-4"> <h4>佣金分布</h4> <div class="dropdown"> <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="dropdownMenuButton" data-bs-toggle="dropdown" aria-expanded="false">
2023年6月
</button> <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton"> <li><a class="dropdown-item" href="#">2023年6月</a></li> <li><a class="dropdown-item" href="#">2023年5月</a></li> <li><a class="dropdown-item" href="#">2023年4月</a></li> </ul> </div> </div> <div class="row"> <div class="col-md-6"> <div class="chart-container"> <canvas id="commissionPieChart"></canvas> </div> </div> <div class="col-md-6"> <div class="chart-container"> <canvas id="commissionBarChart"></canvas> </div> </div> </div> </div> <!-- 佣金记录表格 --> <div class="commission-card"> <div class="d-flex justify-content-between align-items-center mb-4"> <h4>佣金记录</h4> <div class="input-group" style="width: 250px;"> <input type="text" class="form-control form-control-sm" placeholder="搜索分销商或订单号"> <button class="btn btn-sm btn-outline-secondary"><i class="fas fa-search"></i></button> </div> </div> <div class="commission-table"> <div class="table-responsive"> <table class="table table-hover"> <thead> <tr> <th>订单编号</th> <th>分销商</th> <th>级别</th> <th>订单金额</th> <th>佣金比例</th> <th>佣金金额</th> <th>结算时间</th> <th>状态</th> </tr> </thead> <tbody> <tr> <td>ORD-20230620-001</td> <td>王地主</td> <td>一级</td> <td>1,280元</td> <td>15%</td> <td>192元</td> <td>2023-06-20</td> <td><span class="status-badge status-paid">已结算</span></td> </tr> <tr> <td>ORD-20230619-002</td> <td>李地主</td> <td>一级</td> <td>2,560元</td> <td>15%</td> <td>384元</td> <td>2023-06-19</td> <td><span class="status-badge status-paid">已结算</span></td> </tr> <tr> <td>ORD-20230618-003</td> <td>张村民</td> <td>二级</td> <td>980元</td> <td>8%</td> <td>78.4元</td> <td>2023-06-18</td> <td><span class="status-badge status-paid">已结算</span></td> </tr> <tr> <td>ORD-20230618-004</td> <td>王地主</td> <td>一级</td> <td>3,600元</td> <td>18%</td> <td>648元</td> <td>2023-06-18</td> <td><span class="status-badge status-paid">已结算</span></td> </tr> <tr> <td>ORD-20230617-005</td> <td>赵村民</td> <td>二级</td> <td>1,450元</td> <td>8%</td> <td>116元</td> <td>2023-06-17</td> <td><span class="status-badge status-paid">已结算</span></td> </tr> <tr> <td>ORD-20230617-006</td> <td>李地主</td> <td>一级</td> <td>5,800元</td> <td>18%</td> <td>1,044元</td> <td>2023-06-17</td> <td><span class="status-badge status-paid">已结算</span></td> </tr> <tr> <td>ORD-20230621-007</td> <td>张村民</td> <td>二级</td> <td>2,100元</td> <td>8%</td> <td>168元</td> <td>2023-06-21</td> <td><span class="status-badge status-processing">结算中</span></td> </tr> <tr> <td>ORD-20230621-008</td> <td>王地主</td> <td>一级</td> <td>4,200元</td> <td>18%</td> <td>756元</td> <td>2023-06-21</td> <td><span class="status-badge status-processing">结算中</span></td> </tr> </tbody> </table> </div> </div> <div class="d-flex justify-content-between align-items-center mt-3"> <div>显示 1 至 8 条记录，共 256 条</div> <nav aria-label="Page navigation"><ul class="pagination pagination-sm"><li class="page-item disabled"><a class="page-link" href="#">上一页</a></li><li class="page-item active"><a class="page-link" href="#">1</a></li><li class="page-item"><a class="page-link" href="#">2</a></li><li class="page-item"><a class="page-link" href="#">3</a></li><li class="page-item"><a class="page-link" href="#">下一页</a></li></ul></nav> </div> </div> </div> <!-- 右侧内容 --> <div class="col-md-4"> <!-- 销售排行榜 --> <div class="commission-card"> <h4 class="mb-4">销售排行榜</h4> <div class="rank-card"> <div class="rank-header"> <div class="rank-title"> <i class="fas fa-crown rank-icon"></i> <span>一级分销商排行</span> </div> <div class="dropdown"> <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="rankingPeriod" data-bs-toggle="dropdown" aria-expanded="false">
本月
</button> <ul class="dropdown-menu" aria-labelledby="rankingPeriod"> <li><a class="dropdown-item" href="#">本月</a></li> <li><a class="dropdown-item" href="#">本季度</a></li> <li><a class="dropdown-item" href="#">全年</a></li> </ul> </div> </div> <div class="rank-item"> <div class="rank-number rank-1">1</div> <img src="images/avatar1.jpg" alt="分销商头像" class="rank-avatar"> <div class="rank-info"> <div class="rank-name">王地主</div> <div class="rank-role">浙江区域 | 32个村民</div> </div> <div class="rank-value">25,680元</div> </div> <div class="rank-item"> <div class="rank-number rank-2">2</div> <img src="images/avatar2.jpg" alt="分销商头像" class="rank-avatar"> <div class="rank-info"> <div class="rank-name">李地主</div> <div class="rank-role">江苏区域 | 28个村民</div> </div> <div class="rank-value">18,450元</div> </div> <div class="rank-item"> <div class="rank-number rank-3">3</div> <img src="images/avatar3.jpg" alt="分销商头像" class="rank-avatar"> <div class="rank-info"> <div class="rank-name">张地主</div> <div class="rank-role">安徽区域 | 25个村民</div> </div> <div class="rank-value">15,320元</div> </div> <div class="rank-item"> <div class="rank-number rank-other">4</div> <img src="images/avatar4.jpg" alt="分销商头像" class="rank-avatar"> <div class="rank-info"> <div class="rank-name">赵地主</div> <div class="rank-role">山东区域 | 20个村民</div> </div> <div class="rank-value">12,680元</div> </div> <div class="rank-item"> <div class="rank-number rank-other">5</div> <img src="images/avatar5.jpg" alt="分销商头像" class="rank-avatar"> <div class="rank-info"> <div class="rank-name">孙地主</div> <div class="rank-role">河南区域 | 18个村民</div> </div> <div class="rank-value">10,560元</div> </div> </div> <div class="rank-card mt-4"> <div class="rank-header"> <div class="rank-title"> <i class="fas fa-crown rank-icon"></i> <span>二级分销商排行</span> </div> </div> <div class="rank-item"> <div class="rank-number rank-1">1</div> <img src="images/avatar6.jpg" alt="分销商头像" class="rank-avatar"> <div class="rank-info"> <div class="rank-name">张村民</div> <div class="rank-role">王地主团队 | 32笔订单</div> </div> <div class="rank-value">6,450元</div> </div> <div class="rank-item"> <div class="rank-number rank-2">2</div> <img src="images/avatar7.jpg" alt="分销商头像" class="rank-avatar"> <div class="rank-info"> <div class="rank-name">赵村民</div> <div class="rank-role">李地主团队 | 28笔订单</div> </div> <div class="rank-value">5,280元</div> </div> <div class="rank-item"> <div class="rank-number rank-3">3</div> <img src="images/avatar8.jpg" alt="分销商头像" class="rank-avatar"> <div class="rank-info"> <div class="rank-name">王村民</div> <div class="rank-role">王地主团队 | 25笔订单</div> </div> <div class="rank-value">4,320元</div> </div> </div> </div> <!-- 佣金提现 --> <div class="commission-card"> <h4 class="mb-4">佣金提现</h4> <div class="withdrawal-card"> <div class="withdrawal-header"> <div class="withdrawal-title"> <i class="fas fa-wallet withdrawal-icon"></i> <span>佣金账户</span> </div> </div> <div class="withdrawal-info"> <div class="withdrawal-label">可提现佣金：</div> <div class="withdrawal-value">3,580元</div> </div> <div class="withdrawal-info"> <div class="withdrawal-label">冻结佣金：</div> <div class="withdrawal-value">924元</div> </div> <div class="withdrawal-info"> <div class="withdrawal-label">已提现佣金：</div> <div class="withdrawal-value">12,560元</div> </div> <div class="progress-container mt-3"> <div class="progress-bar progress-level1" style="width: 75%;"></div> </div> <div class="progress-text"> <span>本月目标：20,000元</span> <span>已完成：75%</span> </div> <div class="d-grid gap-2 mt-3"> <button class="btn btn-success">申请提现</button> </div> </div> <div class="withdrawal-card mt-4"> <div class="withdrawal-header"> <div class="withdrawal-title"> <i class="fas fa-history withdrawal-icon"></i> <span>提现记录</span> </div> </div> <div class="withdrawal-history"> <div class="history-item"> <div class="history-date">2023-06-15</div> <div class="history-amount">2,500元</div> <div class="history-status status-success">已到账</div> </div> <div class="history-item"> <div class="history-date">2023-05-30</div> <div class="history-amount">3,600元</div> <div class="history-status status-success">已到账</div> </div> <div class="history-item"> <div class="history-date">2023-05-15</div> <div class="history-amount">2,800元</div> <div class="history-status status-success">已到账</div> </div> <div class="history-item"> <div class="history-date">2023-04-30</div> <div class="history-amount">3,660元</div> <div class="history-status status-success">已到账</div> </div> </div> </div> </div> <!-- 常见问题 --> <div class="commission-card"> <h4 class="mb-4">常见问题</h4> <div class="faq-item"> <div class="faq-question" onclick="toggleFaq(this)"> <span>佣金如何计算？</span> <i class="fas fa-chevron-down"></i> </div> <div class="faq-answer">
佣金计算基于分销商级别和销售金额。一级分销商（地主）的直接销售佣金为15%，团队间接佣金为3%。二级分销商（村民）的直接销售佣金为8%。当销售金额达到特定阶梯时，佣金比例会提高。
</div> </div> <div class="faq-item"> <div class="faq-question" onclick="toggleFaq(this)"> <span>佣金什么时候结算？</span> <i class="fas fa-chevron-down"></i> </div> <div class="faq-answer">
佣金每月结算一次，系统会在每月底结算上月的佣金，并在5个工作日内发放到您的账户。当订单完成交付并确认无退换货风险后，佣金将进入可提现状态。
</div> </div> <div class="faq-item"> <div class="faq-question" onclick="toggleFaq(this)"> <span>如何提现佣金？</span> <i class="fas fa-chevron-down"></i> </div> <div class="faq-answer">
当可提现佣金超过100元时，您可以申请提现。点击“申请提现”按钮，选择提现金额和提现方式（支付宝、微信或银行卡），提交申请后系统将在1-3个工作日内处理。
</div> </div> <div class="faq-item"> <div class="faq-question" onclick="toggleFaq(this)"> <span>什么是冻结佣金？</span> <i class="fas fa-chevron-down"></i> </div> <div class="faq-answer">
冻结佣金是指订单已完成但处于退换货保障期内的佣金。一般情况下，订单完成后有一个7天的退换货保障期，这期间的佣金将被冻结。保障期过后，冻结佣金将自动转为可提现佣金。
</div> </div> </div> </div> </div> </div> <!-- 页脚 -->
<div id="footer-container"></div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="js/login-config.js"></script>
<script src="js/components.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script> <script>
// 初始化页面
document.addEventListener('DOMContentLoaded', function() {
    // 设置当前页面的导航链接为激活状态
    setActiveNavItem();
});

// 初始化佣金趋势图表
const commissionTrendCtx = document.getElementById('commissionTrendChart').getContext('2d');
const commissionTrendChart = new Chart(commissionTrendCtx, {
type: 'line',
data: {
labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
datasets: [
{
label: '一级分销商佣金',
data: [65000, 72000, 68000, 85000, 95000, 105000],
borderColor: 'rgba(52, 152, 219, 1)',
backgroundColor: 'rgba(52, 152, 219, 0.1)',
tension: 0.4,
fill: true
},
{
label: '二级分销商佣金',
data: [25000, 28000, 32000, 35000, 40000, 45000],
borderColor: 'rgba(230, 126, 34, 1)',
backgroundColor: 'rgba(230, 126, 34, 0.1)',
tension: 0.4,
fill: true
}
]
},
options: {
responsive: true,
maintainAspectRatio: false,
plugins: {
legend: {
position: 'top',
},
tooltip: {
mode: 'index',
intersect: false
}
},
scales: {
y: {
beginAtZero: true,
ticks: {
callback: function(value) {
return value / 1000 + 'k';
}
}
}
}
}
});
// 初始化佣金分布饼图
const commissionPieCtx = document.getElementById('commissionPieChart').getContext('2d');
const commissionPieChart = new Chart(commissionPieCtx, {
type: 'pie',
data: {
labels: ['一级分销商', '二级分销商'],
datasets: [
{
data: [70, 30],
backgroundColor: [
'rgba(52, 152, 219, 0.8)',
'rgba(230, 126, 34, 0.8)'
],
borderColor: [
'rgba(52, 152, 219, 1)',
'rgba(230, 126, 34, 1)'
],
borderWidth: 1
}
]
},
options: {
responsive: true,
maintainAspectRatio: false,
plugins: {
legend: {
position: 'bottom'
},
tooltip: {
callbacks: {
label: function(context) {
return context.label + ': ' + context.raw + '%';
}
}
}
}
}
});
// 初始化佣金分布柱状图
const commissionBarCtx = document.getElementById('commissionBarChart').getContext('2d');
const commissionBarChart = new Chart(commissionBarCtx, {
type: 'bar',
data: {
labels: ['浙江', '江苏', '安徽', '山东', '河南'],
datasets: [
{
label: '佣金金额(元)',
data: [25680, 18450, 15320, 12680, 10560],
backgroundColor: 'rgba(142, 68, 173, 0.8)',
borderColor: 'rgba(142, 68, 173, 1)',
borderWidth: 1
}
]
},
options: {
responsive: true,
maintainAspectRatio: false,
plugins: {
legend: {
position: 'bottom'
}
},
scales: {
y: {
beginAtZero: true
}
}
}
});
// 切换FAQ显示/隐藏
function toggleFaq(element) {
const answer = element.nextElementSibling;
const icon = element.querySelector('i');
if (answer.classList.contains('active')) {
answer.classList.remove('active');
icon.classList.remove('fa-chevron-up');
icon.classList.add('fa-chevron-down');
} else {
answer.classList.add('active');
icon.classList.remove('fa-chevron-down');
icon.classList.add('fa-chevron-up');
}
}
// 过滤按钮点击事件
document.querySelectorAll('.filter-btn').forEach(btn => {
btn.addEventListener('click', function() {
document.querySelectorAll('.filter-btn').forEach(b => {
b.classList.remove('active');
});
this.classList.add('active');
});
});
// 显示/隐藏数据集事件
document.getElementById('showLevel1').addEventListener('change', function() {
commissionTrendChart.data.datasets[0].hidden = !this.checked;
commissionTrendChart.update();
});
document.getElementById('showLevel2').addEventListener('change', function() {
commissionTrendChart.data.datasets[1].hidden = !this.checked;
commissionTrendChart.update();
});
// 导出数据按钮点击事件
document.querySelector('.btn-outline-success').addEventListener('click', function() {
alert('正在导出数据，请稍后...');
});
// 申请提现按钮点击事件
document.querySelector('.btn-success').addEventListener('click', function() {
alert('正在跳转到提现页面，请稍后...');
});

// 切换FAQ显示/隐藏
function toggleFaq(element) {
    const answer = element.nextElementSibling;
    const icon = element.querySelector('i');

    if (answer.classList.contains('active')) {
        answer.classList.remove('active');
        icon.classList.remove('fa-chevron-up');
        icon.classList.add('fa-chevron-down');
    } else {
        answer.classList.add('active');
        icon.classList.remove('fa-chevron-down');
        icon.classList.add('fa-chevron-up');
    }
}

// 确保导航栏在页面加载完成后可见

// 页面加载完成后确保导航栏可见
window.onload = function() {
    // 确保导航栏可见
    const navbarContainer = document.getElementById('navbar-container');
    if (navbarContainer) {
        // 强制设置为可见
        navbarContainer.style.display = 'block';
        
        // 确保导航栏内容已加载
        if (!navbarContainer.innerHTML || navbarContainer.innerHTML.trim() === '') {
            // 如果导航栏内容为空，尝试重新加载
            if (typeof navbarHTML !== 'undefined') {
                navbarContainer.innerHTML = navbarHTML;
                
                // 重新应用登录状态和模块可见性
                if (typeof loadLoginState === 'function') loadLoginState();
                if (typeof updateNavbarDisplay === 'function') updateNavbarDisplay();
                if (typeof setActiveNavItem === 'function') setActiveNavItem();
                if (typeof setupLoginEvents === 'function') setupLoginEvents();
            }
        }
    }
};
</script>