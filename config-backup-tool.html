<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>配置文件备份和恢复工具 - 智慧农业平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="css/navbar.css">
    
    <style>
    #navbar-container {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
        position: relative !important;
        z-index: 1000 !important;
    }
    
    .tool-card {
        background: white;
        padding: 25px;
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        margin-bottom: 20px;
        border: 1px solid #e9ecef;
    }
    
    .backup-item {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 10px;
        border: 1px solid #dee2e6;
    }
    
    .backup-item:hover {
        background: #e9ecef;
        cursor: pointer;
    }
    
    .file-drop-zone {
        border: 2px dashed #007bff;
        border-radius: 8px;
        padding: 40px;
        text-align: center;
        background: #f8f9fa;
        transition: all 0.3s;
    }
    
    .file-drop-zone.dragover {
        border-color: #0056b3;
        background: #e3f2fd;
    }
    
    .status-indicator {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
    }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <div id="navbar-container"></div>

    <!-- 状态指示器 -->
    <div id="statusIndicator" class="status-indicator"></div>

    <!-- 主要内容 -->
    <div class="container py-5">
        <div class="row">
            <div class="col-12">
                <h2 class="mb-4">
                    <i class="fas fa-tools me-2"></i>配置文件备份和恢复工具
                </h2>
                <p class="text-muted mb-4">此工具帮助您备份、恢复和管理权限配置文件，确保配置的安全性和可追溯性。</p>
            </div>
        </div>
        
        <div class="row">
            <!-- 左侧：备份工具 -->
            <div class="col-md-6">
                <div class="tool-card">
                    <h5 class="mb-4">
                        <i class="fas fa-save me-2 text-primary"></i>配置备份
                    </h5>
                    
                    <!-- 当前配置信息 -->
                    <div class="mb-4">
                        <h6>当前配置信息</h6>
                        <div id="currentConfigInfo" class="bg-light p-3 rounded">
                            <div class="d-flex justify-content-between">
                                <span>配置文件：</span>
                                <span class="text-muted">js/login-config.js</span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span>用户角色：</span>
                                <span id="userCount" class="text-muted">4 个</span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span>功能模块：</span>
                                <span id="moduleCount" class="text-muted">6 个</span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span>功能页面：</span>
                                <span id="pageCount" class="text-muted">29 个</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 备份操作 -->
                    <div class="d-grid gap-2">
                        <button class="btn btn-primary" onclick="createBackup()">
                            <i class="fas fa-download me-1"></i>创建配置备份
                        </button>
                        <button class="btn btn-outline-primary" onclick="createTimestampedBackup()">
                            <i class="fas fa-clock me-1"></i>创建时间戳备份
                        </button>
                        <button class="btn btn-outline-secondary" onclick="exportCurrentConfig()">
                            <i class="fas fa-file-export me-1"></i>导出当前配置
                        </button>
                    </div>
                    
                    <!-- 备份说明 -->
                    <div class="mt-3 p-2 bg-success bg-opacity-10 rounded">
                        <small class="text-success">
                            <i class="fas fa-info-circle me-1"></i>
                            <strong>备份说明：</strong><br>
                            • 创建配置备份：生成标准的配置文件备份<br>
                            • 时间戳备份：文件名包含创建时间<br>
                            • 导出当前配置：导出JSON格式的配置数据
                        </small>
                    </div>
                </div>
                
                <!-- 快速操作 -->
                <div class="tool-card">
                    <h5 class="mb-3">
                        <i class="fas fa-bolt me-2 text-warning"></i>快速操作
                    </h5>
                    
                    <div class="d-grid gap-2">
                        <button class="btn btn-warning" onclick="resetToFactory()">
                            <i class="fas fa-undo-alt me-1"></i>恢复出厂设置
                        </button>
                        <button class="btn btn-info" onclick="openPermissionEditor()">
                            <i class="fas fa-edit me-1"></i>打开权限编辑器
                        </button>
                        <button class="btn btn-secondary" onclick="viewCurrentConfig()">
                            <i class="fas fa-eye me-1"></i>查看当前配置
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 右侧：恢复工具 -->
            <div class="col-md-6">
                <div class="tool-card">
                    <h5 class="mb-4">
                        <i class="fas fa-upload me-2 text-success"></i>配置恢复
                    </h5>
                    
                    <!-- 文件上传区域 -->
                    <div class="file-drop-zone mb-4" id="fileDropZone">
                        <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                        <h6>拖拽配置文件到此处</h6>
                        <p class="text-muted mb-3">或点击下方按钮选择文件</p>
                        <input type="file" id="configFileInput" accept=".js,.json" style="display: none;">
                        <button class="btn btn-outline-primary" onclick="document.getElementById('configFileInput').click()">
                            <i class="fas fa-folder-open me-1"></i>选择配置文件
                        </button>
                    </div>
                    
                    <!-- 文件信息 -->
                    <div id="fileInfo" class="mb-4" style="display: none;">
                        <h6>选择的文件</h6>
                        <div class="bg-light p-3 rounded">
                            <div class="d-flex justify-content-between">
                                <span>文件名：</span>
                                <span id="fileName" class="text-muted"></span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span>文件大小：</span>
                                <span id="fileSize" class="text-muted"></span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span>修改时间：</span>
                                <span id="fileDate" class="text-muted"></span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 恢复操作 -->
                    <div class="d-grid gap-2">
                        <button class="btn btn-success" id="restoreBtn" onclick="restoreConfig()" disabled>
                            <i class="fas fa-upload me-1"></i>恢复配置
                        </button>
                        <button class="btn btn-outline-success" id="previewBtn" onclick="previewConfig()" disabled>
                            <i class="fas fa-eye me-1"></i>预览配置
                        </button>
                        <button class="btn btn-outline-danger" onclick="clearSelection()">
                            <i class="fas fa-times me-1"></i>清除选择
                        </button>
                    </div>
                    
                    <!-- 恢复说明 -->
                    <div class="mt-3 p-2 bg-warning bg-opacity-10 rounded">
                        <small class="text-warning">
                            <i class="fas fa-exclamation-triangle me-1"></i>
                            <strong>注意事项：</strong><br>
                            • 恢复配置会覆盖当前的权限设置<br>
                            • 建议在恢复前先创建当前配置的备份<br>
                            • 支持 .js 和 .json 格式的配置文件
                        </small>
                    </div>
                </div>
                
                <!-- 历史备份 -->
                <div class="tool-card">
                    <h5 class="mb-3">
                        <i class="fas fa-history me-2 text-info"></i>最近操作
                    </h5>
                    
                    <div id="recentOperations">
                        <p class="text-muted text-center">暂无操作记录</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 配置预览模态框 -->
    <div class="modal fade" id="configPreviewModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">配置文件预览</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>配置摘要</h6>
                            <div id="configSummary" class="bg-light p-3 rounded mb-3"></div>
                        </div>
                        <div class="col-md-6">
                            <h6>用户权限</h6>
                            <div id="userPermissions" class="bg-light p-3 rounded mb-3"></div>
                        </div>
                    </div>
                    <h6>完整配置</h6>
                    <pre id="configContent" class="bg-dark text-light p-3 rounded" style="max-height: 300px; overflow-y: auto;"></pre>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-success" onclick="confirmRestore()">确认恢复</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <div id="footer-container"></div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/login-config.js"></script>
    <script src="js/components.js"></script>
    
    <script>
    // 全局变量
    let selectedFile = null;
    let selectedConfig = null;
    
    // 立即执行函数，确保导航栏在页面加载过程中就开始处理
    (function() {
        console.log('配置备份工具页面脚本开始执行');
        
        // 确保导航栏可见
        function ensureNavbarVisible() {
            const navbarContainer = document.getElementById('navbar-container');
            if (navbarContainer) {
                navbarContainer.style.display = 'block';
                navbarContainer.style.visibility = 'visible';
                navbarContainer.style.opacity = '1';
                
                if (!navbarContainer.innerHTML || navbarContainer.innerHTML.trim() === '') {
                    if (typeof navbarHTML !== 'undefined') {
                        navbarContainer.innerHTML = navbarHTML;
                        
                        if (typeof loadLoginState === 'function') loadLoginState();
                        if (typeof updateNavbarDisplay === 'function') updateNavbarDisplay();
                        if (typeof setActiveNavItem === 'function') setActiveNavItem();
                        if (typeof setupLoginEvents === 'function') setupLoginEvents();
                    }
                }
            }
        }
        
        ensureNavbarVisible();
        
        document.addEventListener('DOMContentLoaded', function() {
            ensureNavbarVisible();
            initializeBackupTool();
        });
        
        window.onload = function() {
            ensureNavbarVisible();
            initializeBackupTool();
        };
    })();
    
    // 初始化备份工具
    function initializeBackupTool() {
        updateCurrentConfigInfo();
        setupFileDropZone();
        loadRecentOperations();
    }
    
    // 更新当前配置信息
    function updateCurrentConfigInfo() {
        if (typeof userAccounts !== 'undefined') {
            document.getElementById('userCount').textContent = Object.keys(userAccounts).length + ' 个';
        }
        
        if (typeof allPages !== 'undefined') {
            document.getElementById('moduleCount').textContent = Object.keys(allPages).length + ' 个';
            const totalPages = Object.values(allPages).reduce((sum, pages) => sum + pages.length, 0);
            document.getElementById('pageCount').textContent = totalPages + ' 个';
        }
    }
    </script>
</body>
</html>
