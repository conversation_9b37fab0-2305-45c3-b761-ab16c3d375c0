<!DOCTYPE html> <html lang="zh-CN"> <head> <meta charset="UTF-8"> <meta name="viewport" content="width=device-width, initial-scale=1.0"> <title>消费商转化机制 - 智慧农业平台</title> <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet"> <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet"> <link rel="stylesheet" href="css/common.css"> <link rel="stylesheet" href="css/navbar.css"> <style>
:root {
--primary-color: #28a745;
--secondary-color: #6c757d;
--light-bg: #f8f9fa;
--card-shadow: 0 4px 6px rgba(0,0,0,0.1);
--transition: all 0.3s ease;
--distribution-color: #8e44ad;
--level1-color: #3498db;
--level2-color: #e67e22;
--consumer-color: #16a085;
}
body {
background-color: var(--light-bg);
font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}
.conversion-header {
background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80');
background-size: cover;
background-position: center;
height: 200px;
color: white;
display: flex;
align-items: center;
margin-bottom: 30px;
border-radius: 10px;
}
.conversion-card {
background: white;
padding: 20px;
border-radius: 10px;
box-shadow: var(--card-shadow);
margin-bottom: 30px;
min-height: auto;
}
.stat-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 20px;
transition: var(--transition);
}
.stat-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.stat-icon {
font-size: 2rem;
color: var(--consumer-color);
margin-bottom: 10px;
}
.stat-value {
font-size: 1.5rem;
font-weight: bold;
color: var(--consumer-color);
margin-bottom: 5px;
}
.stat-label {
font-size: 0.9rem;
color: var(--secondary-color);
}
.filter-btn {
padding: 5px 15px;
border-radius: 20px;
background-color: white;
border: 1px solid #ddd;
margin-right: 10px;
margin-bottom: 10px;
cursor: pointer;
transition: var(--transition);
}
.filter-btn.active {
background-color: var(--primary-color);
color: white;
border-color: var(--primary-color);
}
.filter-btn:hover:not(.active) {
background-color: #f0f0f0;
}
.step-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 15px;
transition: var(--transition);
position: relative;
}
.step-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.step-header {
display: flex;
align-items: center;
margin-bottom: 15px;
}
.step-number {
width: 40px;
height: 40px;
border-radius: 50%;
background-color: var(--consumer-color);
color: white;
display: flex;
align-items: center;
justify-content: center;
font-weight: bold;
margin-right: 15px;
flex-shrink: 0;
}
.step-title {
font-weight: bold;
font-size: 1.1rem;
}
.step-body {
margin-bottom: 15px;
}
.step-description {
color: var(--secondary-color);
margin-bottom: 15px;
}
.step-connector {
position: absolute;
left: 20px;
top: 55px;
width: 2px;
height: calc(100% - 40px);
background-color: var(--consumer-color);
z-index: 0;
}
.strategy-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 15px;
transition: var(--transition);
min-height: auto;
}
.strategy-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.strategy-header {
display: flex;
align-items: center;
margin-bottom: 15px;
}
.strategy-icon {
width: 50px;
height: 50px;
border-radius: 10px;
background-color: var(--consumer-color);
color: white;
display: flex;
align-items: center;
justify-content: center;
font-size: 1.5rem;
margin-right: 15px;
flex-shrink: 0;
}
.strategy-title {
font-weight: bold;
font-size: 1.1rem;
margin-bottom: 5px;
}
.strategy-subtitle {
font-size: 0.9rem;
color: var(--secondary-color);
}
.strategy-body {
margin-bottom: 15px;
}
.strategy-description {
color: var(--secondary-color);
margin-bottom: 15px;
}
.strategy-list {
margin-bottom: 15px;
}
.strategy-item {
display: flex;
margin-bottom: 10px;
}
.strategy-item-icon {
color: var(--consumer-color);
margin-right: 10px;
flex-shrink: 0;
margin-top: 3px;
}
.strategy-item-text {
flex: 1;
}
.strategy-footer {
text-align: center;
}
.consumer-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 15px;
transition: var(--transition);
display: flex;
align-items: center;
}
.consumer-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.consumer-avatar {
width: 60px;
height: 60px;
border-radius: 50%;
object-fit: cover;
margin-right: 15px;
}
.consumer-info {
flex: 1;
margin-right: 15px;
}
.consumer-name {
font-weight: bold;
margin-bottom: 5px;
display: flex;
align-items: center;
}
.consumer-badge {
display: inline-block;
padding: 2px 8px;
border-radius: 15px;
font-size: 0.7rem;
margin-left: 10px;
}
.badge-potential {
background-color: rgba(241, 196, 15, 0.1);
color: #f1c40f;
}
.badge-converted {
background-color: rgba(46, 204, 113, 0.1);
color: #2ecc71;
}
.consumer-role {
font-size: 0.9rem;
color: var(--secondary-color);
margin-bottom: 5px;
}
.consumer-stats {
display: flex;
gap: 15px;
font-size: 0.9rem;
}
.consumer-stat {
display: flex;
align-items: center;
}
.consumer-stat-icon {
color: var(--consumer-color);
margin-right: 5px;
}
.consumer-actions {
display: flex;
gap: 10px;
}
.progress-container {
height: 8px;
background-color: #e9ecef;
border-radius: 4px;
margin-bottom: 5px;
overflow: hidden;
}
.progress-bar {
min-height: auto;
border-radius: 4px;
}
.progress-consumer {
background-color: var(--consumer-color);
}
.progress-text {
display: flex;
justify-content: space-between;
font-size: 0.8rem;
color: var(--secondary-color);
}
.chart-container {
position: relative;
height: 300px;
width: 100%;
margin-bottom: 20px;
}
.tool-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 15px;
transition: var(--transition);
min-height: auto;
}
.tool-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.tool-header {
display: flex;
align-items: center;
margin-bottom: 15px;
}
.tool-icon {
width: 50px;
height: 50px;
border-radius: 10px;
background-color: var(--consumer-color);
color: white;
display: flex;
align-items: center;
justify-content: center;
font-size: 1.5rem;
margin-right: 15px;
flex-shrink: 0;
}
.tool-title {
font-weight: bold;
font-size: 1.1rem;
margin-bottom: 5px;
}
.tool-subtitle {
font-size: 0.9rem;
color: var(--secondary-color);
}
.tool-body {
margin-bottom: 15px;
}
.tool-description {
color: var(--secondary-color);
margin-bottom: 15px;
}
.tool-footer {
text-align: center;
}
.faq-item {
margin-bottom: 15px;
}
.faq-question {
font-weight: bold;
margin-bottom: 5px;
cursor: pointer;
display: flex;
justify-content: space-between;
align-items: center;
}
.faq-answer {
color: var(--secondary-color);
display: none;
padding: 10px 0;
}
.faq-answer.active {
display: block;
}
.nav-tabs .nav-link {
color: var(--secondary-color);
}
.nav-tabs .nav-link.active {
color: var(--consumer-color);
font-weight: bold;
}
.tab-content {
padding: 20px 0;
}
</style> </head> <body> <!-- 导航栏 -->
<div id="navbar-container"></div>
<!-- 主要内容 --> <div class="container py-5"> <div class="conversion-header"> <div class="container"> <h1>消费商转化机制</h1> <p class="lead">将普通消费者转化为分销商，提高用户粘性和销售效率，实现消费者与分销商的无缝转换</p> </div> </div> <!-- 顶部统计卡片 --> <div class="row mb-4"> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-users stat-icon"></i> <div class="stat-value">3,256</div> <div class="stat-label">活跃消费者</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-user-plus stat-icon"></i> <div class="stat-value">568</div> <div class="stat-label">转化分销商</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-percentage stat-icon"></i> <div class="stat-value">17.4%</div> <div class="stat-label">转化率</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-chart-line stat-icon"></i> <div class="stat-value">+23.6%</div> <div class="stat-label">环比增长率</div> </div> </div> </div> <!-- 主要内容区域 --> <div class="row"> <!-- 左侧内容 --> <div class="col-md-8"> <!-- 消费者转化流程 --> <div class="conversion-card"> <h4 class="mb-4">消费者转化流程</h4> <div class="step-card"> <div class="step-header"> <div class="step-number">1</div> <div class="step-title">消费者识别与分类</div> </div> <div class="step-body"> <div class="step-description">
通过数据分析和行为跟踪，识别有潜力的消费者，并根据购买频率、购买金额、产品偏好等因素进行分类。
</div> <div class="row"> <div class="col-md-4 mb-3"> <div class="card h-100"> <div class="card-body text-center"> <i class="fas fa-star text-warning mb-3" style="font-size: 2rem;"></i> <h5 class="card-title">高频消费者</h5> <p class="card-text text-secondary">每月购买多次，对产品非常熟悉</p> </div> </div> </div> <div class="col-md-4 mb-3"> <div class="card h-100"> <div class="card-body text-center"> <i class="fas fa-money-bill-wave text-success mb-3" style="font-size: 2rem;"></i> <h5 class="card-title">高额消费者</h5> <p class="card-text text-secondary">单次购买金额大，消费能力强</p> </div> </div> </div> <div class="col-md-4 mb-3"> <div class="card h-100"> <div class="card-body text-center"> <i class="fas fa-heart text-danger mb-3" style="font-size: 2rem;"></i> <h5 class="card-title">忠诚消费者</h5> <p class="card-text text-secondary">长期购买，对品牌有强烈认同感</p> </div> </div> </div> </div> </div> <div class="step-connector"></div> </div> <div class="step-card"> <div class="step-header"> <div class="step-number">2</div> <div class="step-title">消费者激励与引导</div> </div> <div class="step-body"> <div class="step-description">
通过定制化的激励措施，吸引消费者关注分销机会，并引导其了解分销商的收益模式和成功案例。
</div> <div class="row"> <div class="col-md-6 mb-3"> <div class="card h-100"> <div class="card-body"> <h5 class="card-title"><i class="fas fa-gift text-primary me-2"></i>优惠券与激励</h5> <p class="card-text text-secondary">提供专属优惠券、购买返利和会员特权，提高消费者的参与度和兴趣。</p> </div> </div> </div> <div class="col-md-6 mb-3"> <div class="card h-100"> <div class="card-body"> <h5 class="card-title"><i class="fas fa-bullhorn text-danger me-2"></i>分销收益展示</h5> <p class="card-text text-secondary">通过案例展示、收益计算器等工具，直观展示分销商的收益潜力。</p> </div> </div> </div> </div> </div> <div class="step-connector"></div> </div> <div class="step-card"> <div class="step-header"> <div class="step-number">3</div> <div class="step-title">简化转化流程</div> </div> <div class="step-body"> <div class="step-description">
提供简单高效的转化流程，降低转化门槛，并提供必要的支持和指导，帮助消费者快速成为分销商。
</div> <div class="card mb-3"> <div class="card-body"> <div class="row align-items-center"> <div class="col-md-3 text-center mb-3 mb-md-0"> <i class="fas fa-mouse-pointer text-primary" style="font-size: 2.5rem;"></i> <p class="mt-2 mb-0">一键申请</p> </div> <div class="col-md-3 text-center mb-3 mb-md-0"> <i class="fas fa-id-card text-primary" style="font-size: 2.5rem;"></i> <p class="mt-2 mb-0">简化认证</p> </div> <div class="col-md-3 text-center mb-3 mb-md-0"> <i class="fas fa-graduation-cap text-primary" style="font-size: 2.5rem;"></i> <p class="mt-2 mb-0">快速培训</p> </div> <div class="col-md-3 text-center"> <i class="fas fa-rocket text-primary" style="font-size: 2.5rem;"></i> <p class="mt-2 mb-0">立即开始</p> </div> </div> </div> </div> </div> <div class="step-connector"></div> </div> <div class="step-card"> <div class="step-header"> <div class="step-number">4</div> <div class="step-title">新手分销商成长计划</div> </div> <div class="step-body"> <div class="step-description">
为新转化的分销商提供专属的成长计划，包括培训、指导和特殊激励，帮助其快速成长为成功的分销商。
</div> <div class="row"> <div class="col-md-6 mb-3"> <div class="card h-100"> <div class="card-body"> <h5 class="card-title"><i class="fas fa-chalkboard-teacher text-success me-2"></i>新手培训计划</h5> <p class="card-text text-secondary">提供产品知识、销售技巧和分销系统使用的专业培训，帮助新手快速上手。</p> </div> </div> </div> <div class="col-md-6 mb-3"> <div class="card h-100"> <div class="card-body"> <h5 class="card-title"><i class="fas fa-hands-helping text-info me-2"></i>一对一指导</h5> <p class="card-text text-secondary">分配专属的成功分销商作为导师，提供个性化指导和经验分享。</p> </div> </div> </div> </div> <div class="card mt-3"> <div class="card-body"> <h5 class="card-title"><i class="fas fa-award text-warning me-2"></i>新手特殊激励</h5> <div class="row"> <div class="col-md-4 mb-2 mb-md-0"> <div class="d-flex align-items-center"> <i class="fas fa-percentage text-success me-2"></i> <div>首月佣金翻倍</div> </div> </div> <div class="col-md-4 mb-2 mb-md-0"> <div class="d-flex align-items-center"> <i class="fas fa-tools text-primary me-2"></i> <div>免费推广工具包</div> </div> </div> <div class="col-md-4"> <div class="d-flex align-items-center"> <i class="fas fa-shield-alt text-danger me-2"></i> <div>首单保障计划</div> </div> </div> </div> </div> </div> </div> </div> </div> <!-- 转化策略 --> <div class="conversion-card"> <h4 class="mb-4">转化策略</h4> <div class="row"> <div class="col-md-6 mb-4"> <div class="strategy-card"> <div class="strategy-header"> <div class="strategy-icon"> <i class="fas fa-bullseye"></i> </div> <div> <div class="strategy-title">精准营销策略</div> <div class="strategy-subtitle">针对不同类型消费者的定制化营销</div> </div> </div> <div class="strategy-body"> <div class="strategy-description">
根据消费者的行为特征和偏好，定制个性化的营销策略，提高转化效果。
</div> <div class="strategy-list"> <div class="strategy-item"> <i class="fas fa-check-circle strategy-item-icon"></i> <div class="strategy-item-text">根据消费者购买历史推荐相关分销产品</div> </div> <div class="strategy-item"> <i class="fas fa-check-circle strategy-item-icon"></i> <div class="strategy-item-text">在消费者最活跃的时间段发送营销信息</div> </div> <div class="strategy-item"> <i class="fas fa-check-circle strategy-item-icon"></i> <div class="strategy-item-text">根据消费者偏好定制营销语言和内容</div> </div> </div> </div> <div class="strategy-footer"> <button class="btn btn-outline-success">查看详情</button> </div> </div> </div> <div class="col-md-6 mb-4"> <div class="strategy-card"> <div class="strategy-header"> <div class="strategy-icon"> <i class="fas fa-hand-holding-usd"></i> </div> <div> <div class="strategy-title">限时激励策略</div> <div class="strategy-subtitle">创造紧迫感和独特价值</div> </div> </div> <div class="strategy-body"> <div class="strategy-description">
通过限时特惠和独特激励，创造紧迫感和稀缺性，促使消费者快速做出决策。
</div> <div class="strategy-list"> <div class="strategy-item"> <i class="fas fa-check-circle strategy-item-icon"></i> <div class="strategy-item-text">限时提供更高的首月佣金比例</div> </div> <div class="strategy-item"> <i class="fas fa-check-circle strategy-item-icon"></i> <div class="strategy-item-text">限量的免费培训和指导机会</div> </div> <div class="strategy-item"> <i class="fas fa-check-circle strategy-item-icon"></i> <div class="strategy-item-text">限时优惠券和特权礼包</div> </div> </div> </div> <div class="strategy-footer"> <button class="btn btn-outline-success">查看详情</button> </div> </div> </div> <div class="col-md-6 mb-4"> <div class="strategy-card"> <div class="strategy-header"> <div class="strategy-icon"> <i class="fas fa-users"></i> </div> <div> <div class="strategy-title">社交影响策略</div> <div class="strategy-subtitle">利用社交关系和社交证明</div> </div> </div> <div class="strategy-body"> <div class="strategy-description">
利用社交影响和群体心理，通过成功案例和用户见证增强转化信心。
</div> <div class="strategy-list"> <div class="strategy-item"> <i class="fas fa-check-circle strategy-item-icon"></i> <div class="strategy-item-text">展示真实的分销商成功案例和收益</div> </div> <div class="strategy-item"> <i class="fas fa-check-circle strategy-item-icon"></i> <div class="strategy-item-text">提供分销商社区和交流平台</div> </div> <div class="strategy-item"> <i class="fas fa-check-circle strategy-item-icon"></i> <div class="strategy-item-text">组织线上线下分销商交流活动</div> </div> </div> </div> <div class="strategy-footer"> <button class="btn btn-outline-success">查看详情</button> </div> </div> </div> <div class="col-md-6 mb-4"> <div class="strategy-card"> <div class="strategy-header"> <div class="strategy-icon"> <i class="fas fa-graduation-cap"></i> </div> <div> <div class="strategy-title">教育资源策略</div> <div class="strategy-subtitle">提供价值和降低门槛</div> </div> </div> <div class="strategy-body"> <div class="strategy-description">
通过提供有价值的教育资源和培训，降低消费者转化为分销商的心理门槛。
</div> <div class="strategy-list"> <div class="strategy-item"> <i class="fas fa-check-circle strategy-item-icon"></i> <div class="strategy-item-text">提供免费的分销入门指南和课程</div> </div> <div class="strategy-item"> <i class="fas fa-check-circle strategy-item-icon"></i> <div class="strategy-item-text">组织线上分销技巧分享会和答疑解惑</div> </div> <div class="strategy-item"> <i class="fas fa-check-circle strategy-item-icon"></i> <div class="strategy-item-text">提供完整的分销商成长路径和规划</div> </div> </div> </div> <div class="strategy-footer"> <button class="btn btn-outline-success">查看详情</button> </div> </div> </div> </div> </div> </div> <!-- 右侧内容 --> <div class="col-md-4"> <!-- 潜力消费者列表 --> <div class="conversion-card"> <div class="d-flex justify-content-between align-items-center mb-4"> <h4>潜力消费者</h4> <div class="input-group" style="width: 200px;"> <input type="text" class="form-control form-control-sm" placeholder="搜索消费者"> <button class="btn btn-sm btn-outline-secondary"><i class="fas fa-search"></i></button> </div> </div> <div class="consumer-card"> <img src="images/consumer1.jpg" alt="消费者头像" class="consumer-avatar"> <div class="consumer-info"> <div class="consumer-name">
王小明 <span class="consumer-badge badge-potential">高潜力</span> </div> <div class="consumer-role">活跃消费者 | 最近购买：2023-06-18</div> <div class="consumer-stats"> <div class="consumer-stat"> <i class="fas fa-shopping-cart consumer-stat-icon"></i> <span>12笔订单</span> </div> <div class="consumer-stat"> <i class="fas fa-yen-sign consumer-stat-icon"></i> <span>5,680元</span> </div> </div> </div> <div class="consumer-actions"> <button class="btn btn-sm btn-outline-primary"><i class="fas fa-eye"></i></button> <button class="btn btn-sm btn-success"><i class="fas fa-user-plus"></i></button> </div> </div> <div class="consumer-card"> <img src="images/consumer2.jpg" alt="消费者头像" class="consumer-avatar"> <div class="consumer-info"> <div class="consumer-name">
李丽丽 <span class="consumer-badge badge-potential">高潜力</span> </div> <div class="consumer-role">活跃消费者 | 最近购买：2023-06-20</div> <div class="consumer-stats"> <div class="consumer-stat"> <i class="fas fa-shopping-cart consumer-stat-icon"></i> <span>8笔订单</span> </div> <div class="consumer-stat"> <i class="fas fa-yen-sign consumer-stat-icon"></i> <span>6,820元</span> </div> </div> </div> <div class="consumer-actions"> <button class="btn btn-sm btn-outline-primary"><i class="fas fa-eye"></i></button> <button class="btn btn-sm btn-success"><i class="fas fa-user-plus"></i></button> </div> </div> <div class="consumer-card"> <img src="images/consumer3.jpg" alt="消费者头像" class="consumer-avatar"> <div class="consumer-info"> <div class="consumer-name">
张三 <span class="consumer-badge badge-converted">已转化</span> </div> <div class="consumer-role">二级分销商 | 转化时间：2023-06-15</div> <div class="consumer-stats"> <div class="consumer-stat"> <i class="fas fa-shopping-cart consumer-stat-icon"></i> <span>15笔订单</span> </div> <div class="consumer-stat"> <i class="fas fa-yen-sign consumer-stat-icon"></i> <span>4,560元</span> </div> </div> </div> <div class="consumer-actions"> <button class="btn btn-sm btn-outline-primary"><i class="fas fa-eye"></i></button> <button class="btn btn-sm btn-outline-success"><i class="fas fa-comment"></i></button> </div> </div> <div class="consumer-card"> <img src="images/consumer4.jpg" alt="消费者头像" class="consumer-avatar"> <div class="consumer-info"> <div class="consumer-name">
赵丽娜 <span class="consumer-badge badge-potential">高潜力</span> </div> <div class="consumer-role">活跃消费者 | 最近购买：2023-06-19</div> <div class="consumer-stats"> <div class="consumer-stat"> <i class="fas fa-shopping-cart consumer-stat-icon"></i> <span>6笔订单</span> </div> <div class="consumer-stat"> <i class="fas fa-yen-sign consumer-stat-icon"></i> <span>3,850元</span> </div> </div> </div> <div class="consumer-actions"> <button class="btn btn-sm btn-outline-primary"><i class="fas fa-eye"></i></button> <button class="btn btn-sm btn-success"><i class="fas fa-user-plus"></i></button> </div> </div> <div class="consumer-card"> <img src="images/consumer5.jpg" alt="消费者头像" class="consumer-avatar"> <div class="consumer-info"> <div class="consumer-name">
孙小红 <span class="consumer-badge badge-converted">已转化</span> </div> <div class="consumer-role">二级分销商 | 转化时间：2023-06-10</div> <div class="consumer-stats"> <div class="consumer-stat"> <i class="fas fa-shopping-cart consumer-stat-icon"></i> <span>10笔订单</span> </div> <div class="consumer-stat"> <i class="fas fa-yen-sign consumer-stat-icon"></i> <span>4,120元</span> </div> </div> </div> <div class="consumer-actions"> <button class="btn btn-sm btn-outline-primary"><i class="fas fa-eye"></i></button> <button class="btn btn-sm btn-outline-success"><i class="fas fa-comment"></i></button> </div> </div> <div class="text-center mt-3"> <button class="btn btn-outline-primary">查看更多消费者</button> </div> </div> <!-- 转化工具 --> <div class="conversion-card"> <h4 class="mb-4">转化工具</h4> <div class="row"> <div class="col-md-6 mb-3"> <div class="tool-card"> <div class="tool-header"> <div class="tool-icon"> <i class="fas fa-calculator"></i> </div> <div> <div class="tool-title">佣金计算器</div> <div class="tool-subtitle">模拟分销收益</div> </div> </div> <div class="tool-body"> <div class="tool-description">
帮助消费者计算成为分销商后的潜在收益，直观展示分销收益。
</div> </div> <div class="tool-footer"> <button class="btn btn-outline-success">使用工具</button> </div> </div> </div> <div class="col-md-6 mb-3"> <div class="tool-card"> <div class="tool-header"> <div class="tool-icon"> <i class="fas fa-envelope"></i> </div> <div> <div class="tool-title">邮件营销工具</div> <div class="tool-subtitle">自动化邮件营销</div> </div> </div> <div class="tool-body"> <div class="tool-description">
向潜力消费者发送定制化的邮件，介绍分销机会和收益模式。
</div> </div> <div class="tool-footer"> <button class="btn btn-outline-success">使用工具</button> </div> </div> </div> <div class="col-md-6 mb-3"> <div class="tool-card"> <div class="tool-header"> <div class="tool-icon"> <i class="fas fa-gift"></i> </div> <div> <div class="tool-title">优惠券生成器</div> <div class="tool-subtitle">创建专属优惠</div> </div> </div> <div class="tool-body"> <div class="tool-description">
为潜力消费者创建专属优惠券，引导其关注并考虑分销机会。
</div> </div> <div class="tool-footer"> <button class="btn btn-outline-success">使用工具</button> </div> </div> </div> <div class="col-md-6 mb-3"> <div class="tool-card"> <div class="tool-header"> <div class="tool-icon"> <i class="fas fa-chart-pie"></i> </div> <div> <div class="tool-title">消费者分析</div> <div class="tool-subtitle">智能消费者分析</div> </div> </div> <div class="tool-body"> <div class="tool-description">
分析消费者行为和偏好，识别高潜力消费者并制定转化策略。
</div> </div> <div class="tool-footer"> <button class="btn btn-outline-success">使用工具</button> </div> </div> </div> </div> </div> <!-- 转化数据 --> <div class="conversion-card"> <h4 class="mb-4">转化数据</h4> <div class="chart-container"> <canvas id="conversionChart"></canvas> </div> <div class="mt-4"> <h5>转化目标进度</h5> <div class="progress-container mt-3"> <div class="progress-bar progress-consumer" style="width: 56.8%;"></div> </div> <div class="progress-text"> <span>当前转化数：568</span> <span>月度目标：1,000</span> </div> </div> <div class="mt-4"> <h5>转化率对比</h5> <div class="table-responsive"> <table class="table table-sm"> <thead> <tr> <th>指标</th> <th>本月</th> <th>上月</th> <th>环比</th> </tr> </thead> <tbody> <tr> <td>转化率</td> <td>17.4%</td> <td>14.1%</td> <td class="text-success">+3.3%</td> </tr> <tr> <td>平均转化时间</td> <td>5.2天</td> <td>6.8天</td> <td class="text-success">-1.6天</td> </tr> <tr> <td>转化成本</td> <td>45元/人</td> <td>52元/人</td> <td class="text-success">-7元/人</td> </tr> </tbody> </table> </div> </div> </div> </div> </div> </div> <!-- 页脚 -->
<div id="footer-container"></div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="js/login-config.js"></script>
<script src="js/components.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script> <script>
// 使用 common.js 加载导航栏
// 初始化转化数据图表
const conversionChartCtx = document.getElementById('conversionChart').getContext('2d');
const conversionChart = new Chart(conversionChartCtx, {
type: 'line',
data: {
labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
datasets: [
{
label: '转化率',
data: [8.2, 9.5, 11.3, 12.8, 14.1, 17.4],
borderColor: 'rgba(22, 160, 133, 1)',
backgroundColor: 'rgba(22, 160, 133, 0.1)',
tension: 0.4,
fill: true,
yAxisID: 'y'
},
{
label: '转化数量',
data: [120, 180, 250, 320, 460, 568],
borderColor: 'rgba(142, 68, 173, 1)',
backgroundColor: 'rgba(142, 68, 173, 0.1)',
tension: 0.4,
fill: true,
yAxisID: 'y1'
}
]
},
options: {
responsive: true,
maintainAspectRatio: false,
plugins: {
legend: {
position: 'top',
},
tooltip: {
mode: 'index',
intersect: false,
callbacks: {
label: function(context) {
let label = context.dataset.label || '';
if (label) {
label += ': ';
}
if (context.dataset.yAxisID === 'y') {
label += context.raw + '%';
} else {
label += context.raw + '人';
}
return label;
}
}
}
},
scales: {
y: {
type: 'linear',
display: true,
position: 'left',
title: {
display: true,
text: '转化率(%)',
color: 'rgba(22, 160, 133, 1)'
},
ticks: {
callback: function(value) {
return value + '%';
}
}
},
y1: {
type: 'linear',
display: true,
position: 'right',
title: {
display: true,
text: '转化数量(人)',
color: 'rgba(142, 68, 173, 1)'
},
grid: {
drawOnChartArea: false
}
}
}
}
});
// 消费者卡片点击事件
document.querySelectorAll('.consumer-card').forEach(card => {
card.addEventListener('click', function() {
const consumerName = this.querySelector('.consumer-name').textContent.trim().split(' ')[0];
alert(`查看 ${consumerName} 的详细信息`);
});
});
// 消费者操作按钮点击事件
document.querySelectorAll('.consumer-actions .btn').forEach(btn => {
btn.addEventListener('click', function(e) {
e.stopPropagation();
const consumerCard = this.closest('.consumer-card');
const consumerName = consumerCard.querySelector('.consumer-name').textContent.trim().split(' ')[0];
if (this.querySelector('.fa-eye')) {
alert(`查看 ${consumerName} 的详细信息`);
} else if (this.querySelector('.fa-user-plus')) {
alert(`开始将 ${consumerName} 转化为分销商`);
} else if (this.querySelector('.fa-comment')) {
alert(`给 ${consumerName} 发送消息`);
}
});
});
// 工具卡片按钮点击事件
document.querySelectorAll('.tool-card .btn').forEach(btn => {
btn.addEventListener('click', function() {
const toolName = this.closest('.tool-card').querySelector('.tool-title').textContent;
alert(`正在打开 ${toolName} 工具，请稍后...`);
});
});
// 策略卡片按钮点击事件
document.querySelectorAll('.strategy-card .btn').forEach(btn => {
btn.addEventListener('click', function() {
const strategyName = this.closest('.strategy-card').querySelector('.strategy-title').textContent;
alert(`查看 ${strategyName} 的详细信息`);
});
});

// 确保导航栏在页面加载完成后可见
window.onload = function() {
    // 确保导航栏可见
    const navbarContainer = document.getElementById('navbar-container');
    if (navbarContainer) {
        navbarContainer.style.display = '';
    }
};
</script> </body> </html>