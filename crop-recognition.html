<!DOCTYPE html> <html lang="zh-CN"> <head> <meta charset="UTF-8"> <meta name="viewport" content="width=device-width, initial-scale=1.0"> <title>作物图像识别系统 - 智慧农业平台</title> <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet"> <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet"> <link rel="stylesheet" href="css/common.css"> <link rel="stylesheet" href="css/navbar.css"> <style>
:root {
--primary-color: #28a745;
--secondary-color: #6c757d;
--light-bg: #f8f9fa;
--card-shadow: 0 4px 6px rgba(0,0,0,0.1);
--transition: all 0.3s ease;
--warning-color: #ffc107;
--danger-color: #dc3545;
--info-color: #0dcaf0;
}
body {
background-color: var(--light-bg);
font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}
.recognition-header {
background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80');
background-size: cover;
background-position: center;
height: 200px;
color: white;
display: flex;
align-items: center;
margin-bottom: 30px;
border-radius: 10px;
}
.recognition-card {
background: white;
padding: 20px;
border-radius: 10px;
box-shadow: var(--card-shadow);
margin-bottom: 30px;
min-height: auto;
}
.stat-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 20px;
transition: var(--transition);
}
.stat-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.stat-icon {
font-size: 2rem;
margin-bottom: 10px;
}
.stat-icon.warning {
color: var(--warning-color);
}
.stat-icon.danger {
color: var(--danger-color);
}
.stat-icon.info {
color: var(--info-color);
}
.stat-icon.success {
color: var(--primary-color);
}
.stat-value {
font-size: 1.5rem;
font-weight: bold;
margin-bottom: 5px;
}
.stat-label {
font-size: 0.9rem;
color: var(--secondary-color);
}
.upload-container {
border: 2px dashed #ddd;
border-radius: 10px;
padding: 30px;
text-align: center;
margin-bottom: 20px;
cursor: pointer;
transition: var(--transition);
}
.upload-container:hover {
border-color: var(--primary-color);
}
.upload-icon {
font-size: 3rem;
color: var(--secondary-color);
margin-bottom: 15px;
}
.upload-text {
margin-bottom: 15px;
}
.upload-preview {
max-width: 100%;
max-height: 300px;
margin: 0 auto 15px;
display: none;
border-radius: 10px;
}
.recognition-result {
background: var(--light-bg);
padding: 20px;
border-radius: 10px;
margin-bottom: 20px;
display: none;
}
.result-title {
font-weight: bold;
margin-bottom: 15px;
display: flex;
align-items: center;
justify-content: space-between;
}
.result-icon {
margin-right: 10px;
color: var(--primary-color);
}
.result-accuracy {
font-size: 0.9rem;
color: var(--secondary-color);
}
.result-item {
margin-bottom: 15px;
padding-bottom: 15px;
border-bottom: 1px solid #eee;
}
.result-item:last-child {
border-bottom: none;
margin-bottom: 0;
padding-bottom: 0;
}
.result-label {
font-weight: bold;
margin-bottom: 5px;
}
.result-value {
color: var(--secondary-color);
}
.result-status {
display: inline-block;
padding: 3px 8px;
border-radius: 15px;
font-size: 0.8rem;
margin-left: 10px;
}
.status-healthy {
background-color: rgba(40, 167, 69, 0.1);
color: var(--primary-color);
}
.status-warning {
background-color: rgba(255, 193, 7, 0.1);
color: var(--warning-color);
}
.status-danger {
background-color: rgba(220, 53, 69, 0.1);
color: var(--danger-color);
}
.treatment-card {
background-color: rgba(40, 167, 69, 0.1);
border-left: 4px solid var(--primary-color);
padding: 15px;
margin-bottom: 15px;
border-radius: 0 5px 5px 0;
display: none;
}
.treatment-title {
font-weight: bold;
margin-bottom: 10px;
display: flex;
align-items: center;
}
.treatment-icon {
color: var(--primary-color);
margin-right: 10px;
}
.treatment-content {
color: var(--secondary-color);
}
.treatment-step {
display: flex;
margin-bottom: 10px;
}
.step-number {
width: 25px;
height: 25px;
background-color: var(--primary-color);
color: white;
border-radius: 50%;
display: flex;
align-items: center;
justify-content: center;
margin-right: 10px;
font-size: 0.8rem;
font-weight: bold;
}
.step-content {
flex: 1;
}
.history-item {
display: flex;
margin-bottom: 15px;
padding-bottom: 15px;
border-bottom: 1px solid #eee;
}
.history-icon {
width: 40px;
height: 40px;
background-color: var(--light-bg);
border-radius: 50%;
display: flex;
align-items: center;
justify-content: center;
margin-right: 15px;
color: var(--primary-color);
}
.history-content {
flex: 1;
}
.history-title {
font-weight: bold;
margin-bottom: 5px;
}
.history-time {
font-size: 0.9rem;
color: var(--secondary-color);
margin-bottom: 5px;
}
.history-desc {
font-size: 0.9rem;
}
.history-image {
width: 60px;
height: 60px;
object-fit: cover;
border-radius: 5px;
margin-left: 15px;
}
.gallery-container {
display: grid;
grid-template-columns: repeat(3, 1fr);
gap: 15px;
margin-bottom: 20px;
}
.gallery-item {
border-radius: 10px;
overflow: hidden;
position: relative;
height: 150px;
cursor: pointer;
transition: var(--transition);
}
.gallery-item:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.gallery-image {
width: 100%;
min-height: auto;
object-fit: cover;
}
.gallery-overlay {
position: absolute;
bottom: 0;
left: 0;
right: 0;
background: rgba(0,0,0,0.7);
color: white;
padding: 10px;
font-size: 0.9rem;
}
.disease-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 15px;
transition: var(--transition);
}
.disease-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.disease-title {
font-weight: bold;
margin-bottom: 10px;
display: flex;
align-items: center;
}
.disease-icon {
margin-right: 10px;
color: var(--danger-color);
}
.disease-desc {
color: var(--secondary-color);
margin-bottom: 15px;
}
.disease-image {
width: 100%;
height: 150px;
object-fit: cover;
border-radius: 5px;
margin-bottom: 15px;
}
</style> </head> <body> <!-- 导航栏 -->
<div id="navbar-container"></div>
<!-- 主要内容 --> <div class="container py-5"> <div class="recognition-header"> <div class="container"> <h1>作物图像识别系统</h1> <p class="lead">通过AI图像识别技术，快速诊断作物健康状况，及时发现病虫害问题，提供专业防治方案</p> </div> </div> <!-- 顶部统计卡片 --> <div class="row mb-4"> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-camera stat-icon success"></i> <div class="stat-value">1,256</div> <div class="stat-label">总识别次数</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-bug stat-icon danger"></i> <div class="stat-value">328</div> <div class="stat-label">病虫害识别数</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-check-circle stat-icon info"></i> <div class="stat-value">95%</div> <div class="stat-label">识别准确率</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-seedling stat-icon success"></i> <div class="stat-value">12</div> <div class="stat-label">支持作物种类</div> </div> </div> </div> <!-- 主要内容区域 --> <div class="row"> <!-- 左侧内容 --> <div class="col-md-8"> <!-- 图像上传区域 --> <div class="recognition-card"> <h4 class="mb-4">作物图像识别</h4> <div class="upload-container" id="uploadContainer"> <i class="fas fa-cloud-upload-alt upload-icon"></i> <div class="upload-text">点击或拖拽图片到这里进行作物识别</div> <div class="upload-text text-secondary">支持 JPG, PNG 格式，大小不超过 10MB</div> <button class="btn btn-success">选择图片</button> <input type="file" id="fileUpload" style="display: none;" accept="image/*"> </div> <img src="" id="imagePreview" class="upload-preview"> <!-- 识别结果 --> <div class="recognition-result" id="recognitionResult"> <div class="result-title"> <div> <i class="fas fa-check-circle result-icon"></i> <span>识别结果</span> </div> <div class="result-accuracy">准确率：95%</div> </div> <div class="result-item"> <div class="result-label">作物类型：<span class="text-success">水稻</span></div> <div class="result-value">生长阶段：抽穗期</div> </div> <div class="result-item"> <div class="result-label">健康状况：<span class="text-danger">异常</span> <span class="result-status status-danger">需要处理</span></div> <div class="result-value">检测到稻飞虫感染，发生率约25%，建议及时处理。</div> </div> <div class="result-item"> <div class="result-label">发现问题：</div> <div class="result-value"> <ul> <li>叶片出现黄色斑点</li> <li>叶片边缘出现卷曲现象</li> <li>部分叶片出现干枝</li> </ul> </div> </div> </div> <!-- 处理建议 --> <div class="treatment-card" id="treatmentCard"> <div class="treatment-title"> <i class="fas fa-prescription-bottle treatment-icon"></i> <span>防治建议</span> </div> <div class="treatment-content"> <p>根据识别结果，建议采取以下措施对稻飞虫进行防治：</p> <div class="treatment-steps"> <div class="treatment-step"> <div class="step-number">1</div> <div class="step-content">使用生物农药《苯菌素》，每些用量50ml，稍稍稀释后均匀喷洒</div> </div> <div class="treatment-step"> <div class="step-number">2</div> <div class="step-content">在田间设置诱捕灯，诱杀成虫</div> </div> <div class="treatment-step"> <div class="step-number">3</div> <div class="step-content">加强田间管理，保持通风透光，减少病虫害发生条件</div> </div> </div> <div class="d-grid gap-2 mt-3"> <button class="btn btn-success">查看详细防治方案</button> <button class="btn btn-outline-secondary">咨询专家</button> </div> </div> </div> </div> <!-- 常见病虫害图库 --> <div class="recognition-card"> <div class="d-flex justify-content-between align-items-center mb-4"> <h4>常见病虫害图库</h4> <div> <button class="btn btn-sm btn-outline-success me-2">水稻</button> <button class="btn btn-sm btn-success me-2">小麦</button> <button class="btn btn-sm btn-outline-success">现代豆</button> </div> </div> <div class="gallery-container"> <div class="gallery-item"> <img src="images/disease1.jpg" alt="病虫害图片" class="gallery-image"> <div class="gallery-overlay">稻飞虫</div> </div> <div class="gallery-item"> <img src="images/disease2.jpg" alt="病虫害图片" class="gallery-image"> <div class="gallery-overlay">稻灰飞虫</div> </div> <div class="gallery-item"> <img src="images/disease3.jpg" alt="病虫害图片" class="gallery-image"> <div class="gallery-overlay">稻灰发病</div> </div> <div class="gallery-item"> <img src="images/disease4.jpg" alt="病虫害图片" class="gallery-image"> <div class="gallery-overlay">稻纽枝线虫</div> </div> <div class="gallery-item"> <img src="images/disease5.jpg" alt="病虫害图片" class="gallery-image"> <div class="gallery-overlay">稻绿叶蝇</div> </div> <div class="gallery-item"> <img src="images/disease6.jpg" alt="病虫害图片" class="gallery-image"> <div class="gallery-overlay">稻红叶枯</div> </div> </div> <div class="text-center mt-3"> <button class="btn btn-outline-success">查看更多</button> </div> </div> </div> <!-- 右侧内容 --> <div class="col-md-4"> <!-- 病虫害详情 --> <div class="recognition-card"> <h4 class="mb-4">病虫害详情</h4> <div class="disease-card"> <div class="disease-title"> <i class="fas fa-bug disease-icon"></i> <span>稻飞虫</span> </div> <img src="images/disease1.jpg" alt="稻飞虫" class="disease-image"> <div class="disease-desc"> <p>稻飞虫是水稻主要害虫之一，主要危害水稻、小麦等作物。成虫和若虫吸食水稻叶片汁液，导致叶片发黄、干枝，严重时可导致整株死亡。</p> <p>主要特征：</p> <ul> <li>叶片出现黄色斑点</li> <li>叶片边缘出现卷曲现象</li> <li>部分叶片出现干枝</li> <li>虫体大小约3-4mm，身体黄绿色</li> </ul> </div> </div> <!-- 防治方案 --> <div class="disease-card"> <div class="disease-title"> <i class="fas fa-prescription-bottle disease-icon"></i> <span>防治方案</span> </div> <div class="disease-desc"> <p>稻飞虫防治方案：</p> <ol> <li><strong>农业防治：</strong> 种植抵抗品种，合理轮作，加强田间管理。</li> <li><strong>物理防治：</strong> 利用黄色粘板、诱捕灯等诱杀成虫。</li> <li><strong>生物防治：</strong> 采用天敌、苯菌素等生物农药进行防治。</li> <li><strong>化学防治：</strong> 在虫害发生初期，使用吉他能、吸毒丙等药剂进行防治。</li> </ol> <p><strong>推荐生物农药：</strong> 苯菌素，每些用量50ml，稀释后均匀喷洒。</p> </div> </div> </div> <!-- 识别历史 --> <div class="recognition-card"> <h4 class="mb-4">识别历史</h4> <div class="history-item"> <div class="history-icon"> <i class="fas fa-camera"></i> </div> <div class="history-content"> <div class="history-title">水稻 - 稻飞虫感染</div> <div class="history-time">2023-06-20 10:30</div> <div class="history-desc">检测到稻飞虫感染，发生率约25%，已提供防治方案。</div> </div> <img src="images/history1.jpg" alt="历史图片" class="history-image"> </div> <div class="history-item"> <div class="history-icon"> <i class="fas fa-camera"></i> </div> <div class="history-content"> <div class="history-title">小麦 - 健康状态</div> <div class="history-time">2023-06-15 14:45</div> <div class="history-desc">小麦生长状态良好，未发现病虫害问题。</div> </div> <img src="images/history2.jpg" alt="历史图片" class="history-image"> </div> <div class="history-item"> <div class="history-icon"> <i class="fas fa-camera"></i> </div> <div class="history-content"> <div class="history-title">水稻 - 稻红叶枯病</div> <div class="history-time">2023-06-10 09:15</div> <div class="history-desc">检测到稻红叶枯病，发生率约15%，已提供防治方案。</div> </div> <img src="images/history3.jpg" alt="历史图片" class="history-image"> </div> </div> <!-- 作物识别指南 --> <div class="recognition-card"> <h4 class="mb-4">作物识别指南</h4> <div class="disease-card"> <div class="disease-title"> <i class="fas fa-info-circle disease-icon"></i> <span>拍照技巧</span> </div> <div class="disease-desc"> <p>为了获得更准确的识别结果，请注意以下拍照技巧：</p> <ul> <li>选择光线充足的环境，避免阴影和强光</li> <li>保持相机稳定，避免模糊</li> <li>尽量近距离拍摄病变部位</li> <li>拍摄多个角度，包括整株和特写</li> <li>尽量包含健康和病变部位的对比</li> </ul> </div> </div> <div class="disease-card"> <div class="disease-title"> <i class="fas fa-question-circle disease-icon"></i> <span>常见问题</span> </div> <div class="disease-desc"> <p><strong>Q: 识别结果不准确怎么办？</strong></p> <p>A: 请尝试重新拍摄更清晰的图片，或从不同角度拍摄。您也可以尝试使用专家咨询功能。</p> <p><strong>Q: 支持哪些作物的识别？</strong></p> <p>A: 目前支持水稻、小麦、玉米、现代豆、花生、马铃薯、番茄、辣椒、黄瓜、苹果、柚子和葡萄等作物的识别。</p> </div> </div> </div> </div> </div> </div> <!-- 页脚 -->
<div id="footer-container"></div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="js/login-config.js"></script>
<script src="js/components.js"></script>
<script>
// 使用 common.js 加载导航栏
// 图片上传功能
const uploadContainer = document.getElementById('uploadContainer');
const fileUpload = document.getElementById('fileUpload');
const imagePreview = document.getElementById('imagePreview');
const recognitionResult = document.getElementById('recognitionResult');
const treatmentCard = document.getElementById('treatmentCard');
// 点击上传区域触发文件选择
uploadContainer.addEventListener('click', function() {
fileUpload.click();
});
// 文件选择后的处理
fileUpload.addEventListener('change', function() {
if (this.files && this.files[0]) {
const reader = new FileReader();
reader.onload = function(e) {
// 显示图片预览
imagePreview.src = e.target.result;
imagePreview.style.display = 'block';
// 模拟识别过程，实际应用中应该调用API
setTimeout(function() {
// 显示识别结果
recognitionResult.style.display = 'block';
treatmentCard.style.display = 'block';
// 滚动到结果区域
recognitionResult.scrollIntoView({ behavior: 'smooth' });
}, 1500); // 模拟1.5秒的识别时间
}
reader.readAsDataURL(this.files[0]);
}
});
// 支持拖拽上传
['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
uploadContainer.addEventListener(eventName, preventDefaults, false);
});
function preventDefaults(e) {
e.preventDefault();
e.stopPropagation();
}
['dragenter', 'dragover'].forEach(eventName => {
uploadContainer.addEventListener(eventName, highlight, false);
});
['dragleave', 'drop'].forEach(eventName => {
uploadContainer.addEventListener(eventName, unhighlight, false);
});
function highlight() {
uploadContainer.style.borderColor = 'var(--primary-color)';
uploadContainer.style.backgroundColor = 'rgba(40, 167, 69, 0.1)';
}
function unhighlight() {
uploadContainer.style.borderColor = '#ddd';
uploadContainer.style.backgroundColor = 'white';
}
uploadContainer.addEventListener('drop', handleDrop, false);
function handleDrop(e) {
const dt = e.dataTransfer;
const files = dt.files;
if (files && files[0]) {
fileUpload.files = files;
const event = new Event('change');
fileUpload.dispatchEvent(event);
}
}
// 查看详细防治方案按钮点击事件
document.querySelector('.treatment-card .btn-success').addEventListener('click', function() {
alert('正在加载详细防治方案...');
});
// 咨询专家按钮点击事件
document.querySelector('.treatment-card .btn-outline-secondary').addEventListener('click', function() {
alert('正在连接专家，请稍后...');
});
// 图库筛选按钮点击事件
document.querySelectorAll('.recognition-card .btn-sm').forEach(btn => {
btn.addEventListener('click', function() {
if (!this.classList.contains('btn-success')) {
document.querySelectorAll('.recognition-card .btn-sm').forEach(b => {
b.classList.remove('btn-success');
b.classList.add('btn-outline-success');
});
this.classList.remove('btn-outline-success');
this.classList.add('btn-success');
}
});
});
// 查看更多按钮点击事件
document.querySelector('.recognition-card .btn-outline-success').addEventListener('click', function() {
alert('正在加载更多病虫害图片...');
});
// 图库项目点击事件
document.querySelectorAll('.gallery-item').forEach(item => {
item.addEventListener('click', function() {
const diseaseName = this.querySelector('.gallery-overlay').textContent;
alert(`正在加载 ${diseaseName} 的详细信息...`);
});
});

// 确保导航栏在页面加载完成后可见
window.onload = function() {
    // 确保导航栏可见
    const navbarContainer = document.getElementById('navbar-container');
    if (navbarContainer) {
        navbarContainer.style.display = '';
    }
};
</script>