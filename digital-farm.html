<!DOCTYPE html> <html lang="zh-CN"> <head> <meta charset="UTF-8"> <meta name="viewport" content="width=device-width, initial-scale=1.0"> <title>与政府合作数字农场试点 - 智慧农业平台</title> <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet"> <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet"> <link rel="stylesheet" href="css/common.css"> <link rel="stylesheet" href="css/navbar.css"> <style>
:root {
--primary-color: #28a745;
--secondary-color: #6c757d;
--light-bg: #f8f9fa;
--card-shadow: 0 4px 6px rgba(0,0,0,0.1);
--transition: all 0.3s ease;
--digital-color: #2ecc71;
}
body {
background-color: var(--light-bg);
font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}
.digital-header {
background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80');
background-size: cover;
background-position: center;
height: 300px;
color: white;
display: flex;
align-items: center;
margin-bottom: 30px;
border-radius: 10px;
}
.digital-card {
background: white;
padding: 20px;
border-radius: 10px;
box-shadow: var(--card-shadow);
margin-bottom: 30px;
min-height: auto;
}
.stat-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 20px;
transition: var(--transition);
}
.stat-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.stat-icon {
font-size: 2rem;
color: var(--digital-color);
margin-bottom: 10px;
}
.stat-value {
font-size: 1.5rem;
font-weight: bold;
color: var(--digital-color);
margin-bottom: 5px;
}
.stat-label {
font-size: 0.9rem;
color: var(--secondary-color);
}
.project-card {
background: var(--light-bg);
border-radius: 10px;
overflow: hidden;
margin-bottom: 20px;
transition: var(--transition);
min-height: auto;
border: 1px solid #eee;
}
.project-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.project-img {
width: 100%;
height: 200px;
object-fit: cover;
}
.project-info {
padding: 15px;
}
.project-title {
font-weight: bold;
font-size: 1.1rem;
margin-bottom: 5px;
display: flex;
justify-content: space-between;
align-items: center;
}
.project-badge {
display: inline-block;
padding: 2px 8px;
border-radius: 15px;
font-size: 0.7rem;
background-color: rgba(46, 204, 113, 0.1);
color: var(--digital-color);
}
.project-desc {
color: var(--secondary-color);
margin-bottom: 10px;
font-size: 0.9rem;
height: 60px;
overflow: hidden;
}
.project-meta {
display: flex;
justify-content: space-between;
margin-bottom: 10px;
font-size: 0.9rem;
}
.project-location {
color: var(--secondary-color);
display: flex;
align-items: center;
}
.project-location i {
margin-right: 5px;
}
.project-date {
color: var(--secondary-color);
display: flex;
align-items: center;
}
.project-date i {
margin-right: 5px;
}
.project-features {
display: flex;
flex-wrap: wrap;
gap: 5px;
margin-bottom: 10px;
}
.project-feature {
display: inline-block;
padding: 2px 8px;
border-radius: 15px;
font-size: 0.7rem;
background-color: #f0f0f0;
color: var(--secondary-color);
}
.project-actions {
display: flex;
gap: 10px;
}
.benefit-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 15px;
transition: var(--transition);
min-height: auto;
}
.benefit-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.benefit-header {
display: flex;
align-items: center;
margin-bottom: 15px;
}
.benefit-icon {
width: 50px;
height: 50px;
border-radius: 10px;
background-color: var(--digital-color);
color: white;
display: flex;
align-items: center;
justify-content: center;
font-size: 1.5rem;
margin-right: 15px;
flex-shrink: 0;
}
.benefit-title {
font-weight: bold;
font-size: 1.1rem;
margin-bottom: 5px;
}
.benefit-subtitle {
font-size: 0.9rem;
color: var(--secondary-color);
}
.benefit-body {
margin-bottom: 15px;
}
.benefit-description {
color: var(--secondary-color);
margin-bottom: 15px;
}
.step-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 15px;
transition: var(--transition);
position: relative;
}
.step-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.step-header {
display: flex;
align-items: center;
margin-bottom: 15px;
}
.step-number {
width: 40px;
height: 40px;
border-radius: 50%;
background-color: var(--digital-color);
color: white;
display: flex;
align-items: center;
justify-content: center;
font-weight: bold;
margin-right: 15px;
flex-shrink: 0;
}
.step-title {
font-weight: bold;
font-size: 1.1rem;
}
.step-body {
margin-bottom: 15px;
}
.step-description {
color: var(--secondary-color);
margin-bottom: 15px;
}
.step-connector {
position: absolute;
left: 20px;
top: 55px;
width: 2px;
height: calc(100% - 40px);
background-color: var(--digital-color);
z-index: 0;
}
.faq-item {
margin-bottom: 15px;
}
.faq-question {
font-weight: bold;
margin-bottom: 5px;
cursor: pointer;
display: flex;
justify-content: space-between;
align-items: center;
}
.faq-answer {
color: var(--secondary-color);
display: none;
padding: 10px 0;
}
.faq-answer.active {
display: block;
}
.partner-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 15px;
transition: var(--transition);
display: flex;
align-items: center;
}
.partner-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.partner-logo {
width: 80px;
height: 80px;
border-radius: 10px;
object-fit: contain;
margin-right: 15px;
background-color: white;
padding: 5px;
}
.partner-info {
flex: 1;
}
.partner-name {
font-weight: bold;
margin-bottom: 5px;
}
.partner-role {
font-size: 0.9rem;
color: var(--secondary-color);
margin-bottom: 5px;
}
.partner-desc {
font-size: 0.9rem;
color: var(--secondary-color);
}
.news-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 15px;
transition: var(--transition);
}
.news-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.news-date {
font-size: 0.8rem;
color: var(--secondary-color);
margin-bottom: 5px;
}
.news-title {
font-weight: bold;
margin-bottom: 10px;
}
.news-content {
color: var(--secondary-color);
margin-bottom: 10px;
font-size: 0.9rem;
}
.news-source {
font-size: 0.8rem;
color: var(--secondary-color);
font-style: italic;
}
.map-container {
height: 300px;
width: 100%;
border-radius: 10px;
overflow: hidden;
margin-bottom: 20px;
}
.chart-container {
position: relative;
height: 300px;
width: 100%;
margin-bottom: 20px;
}
.nav-tabs .nav-link {
color: var(--secondary-color);
}
.nav-tabs .nav-link.active {
color: var(--digital-color);
font-weight: bold;
}
.tab-content {
padding: 20px 0;
}
.timeline {
position: relative;
padding-left: 30px;
margin-bottom: 20px;
}
.timeline-line {
position: absolute;
left: 15px;
top: 0;
bottom: 0;
width: 2px;
background-color: var(--digital-color);
}
.timeline-item {
position: relative;
margin-bottom: 20px;
}
.timeline-dot {
position: absolute;
left: -30px;
top: 0;
width: 20px;
height: 20px;
border-radius: 50%;
background-color: var(--digital-color);
border: 3px solid white;
}
.timeline-date {
font-weight: bold;
margin-bottom: 5px;
color: var(--digital-color);
}
.timeline-content {
background-color: white;
padding: 15px;
border-radius: 10px;
box-shadow: var(--card-shadow);
}
.timeline-title {
font-weight: bold;
margin-bottom: 10px;
}
.timeline-desc {
color: var(--secondary-color);
font-size: 0.9rem;
}
</style> </head> <body> <!-- 导航栏 -->
<div id="navbar-container"></div>
<!-- 主要内容 --> <div class="container py-5"> <div class="digital-header"> <div class="container"> <h1>与政府合作数字农场试点</h1> <p class="lead">携手政府部门共同打造数字农业示范基地，推动农业现代化和数字化转型，实现农业生产的智能化、精准化和高效化</p> <div class="mt-4"> <button class="btn btn-success btn-lg me-2"><i class="fas fa-handshake me-2"></i>申请合作</button> <button class="btn btn-outline-light btn-lg"><i class="fas fa-map-marked-alt me-2"></i>查看试点地图</button> </div> </div> </div> <!-- 顶部统计卡片 --> <div class="row mb-4"> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-map-marker-alt stat-icon"></i> <div class="stat-value">28</div> <div class="stat-label">试点数量</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-building stat-icon"></i> <div class="stat-value">15</div> <div class="stat-label">合作政府部门</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-users stat-icon"></i> <div class="stat-value">3,560</div> <div class="stat-label">参与农户数量</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-percentage stat-icon"></i> <div class="stat-value">32%</div> <div class="stat-label">平均产量提升</div> </div> </div> </div> <!-- 主要内容区域 --> <div class="row"> <!-- 左侧内容 --> <div class="col-md-8"> <!-- 试点项目列表 --> <div class="digital-card mb-4"> <div class="d-flex justify-content-between align-items-center mb-4"> <h4>数字农场试点项目</h4> <div class="input-group" style="width: 250px;"> <input type="text" class="form-control" placeholder="搜索试点项目"> <button class="btn btn-outline-secondary"><i class="fas fa-search"></i></button> </div> </div> <div class="row"> <div class="col-md-6 mb-4"> <div class="project-card"> <img src="images/digital-farm1.jpg" alt="浙江智慧农业示范基地" class="project-img"> <div class="project-info"> <div class="project-title"> <span>浙江智慧农业示范基地</span> <span class="project-badge">进行中</span> </div> <div class="project-desc">与浙江省农业农村厅合作建设的智慧农业示范基地，采用物联网、大数据、人工智能等技术，实现农业生产全过程数字化管理</div> <div class="project-meta"> <div class="project-location"><i class="fas fa-map-marker-alt"></i> 浙江省杭州市</div> <div class="project-date"><i class="fas fa-calendar-alt"></i> 2022-05 至今</div> </div> <div class="project-features"> <span class="project-feature"><i class="fas fa-wifi"></i> 物联网</span> <span class="project-feature"><i class="fas fa-robot"></i> 智能控制</span> <span class="project-feature"><i class="fas fa-chart-line"></i> 数据分析</span> </div> <div class="project-actions"> <button class="btn btn-primary w-100"><i class="fas fa-info-circle me-2"></i>查看详情</button> </div> </div> </div> </div> <div class="col-md-6 mb-4"> <div class="project-card"> <img src="images/digital-farm2.jpg" alt="江苏数字农业产业园" class="project-img"> <div class="project-info"> <div class="project-title"> <span>江苏数字农业产业园</span> <span class="project-badge">进行中</span> </div> <div class="project-desc">与江苏省农业农村厅和苏州市政府合作建设的数字农业产业园，整合农业生产、加工、物流、销售等环节，打造全产业链数字化示范</div> <div class="project-meta"> <div class="project-location"><i class="fas fa-map-marker-alt"></i> 江苏省苏州市</div> <div class="project-date"><i class="fas fa-calendar-alt"></i> 2022-08 至今</div> </div> <div class="project-features"> <span class="project-feature"><i class="fas fa-industry"></i> 产业链整合</span> <span class="project-feature"><i class="fas fa-database"></i> 大数据</span> <span class="project-feature"><i class="fas fa-cloud"></i> 云平台</span> </div> <div class="project-actions"> <button class="btn btn-primary w-100"><i class="fas fa-info-circle me-2"></i>查看详情</button> </div> </div> </div> </div> <div class="col-md-6 mb-4"> <div class="project-card"> <img src="images/digital-farm3.jpg" alt="山东智慧果园示范基地" class="project-img"> <div class="project-info"> <div class="project-title"> <span>山东智慧果园示范基地</span> <span class="project-badge">进行中</span> </div> <div class="project-desc">与山东省农业农村厅和烟台市政府合作建设的智慧果园示范基地，采用物联网、人工智能等技术，实现果园智能管理和精准农业</div> <div class="project-meta"> <div class="project-location"><i class="fas fa-map-marker-alt"></i> 山东省烟台市</div> <div class="project-date"><i class="fas fa-calendar-alt"></i> 2023-03 至今</div> </div> <div class="project-features"> <span class="project-feature"><i class="fas fa-tint"></i> 智能水肥</span> <span class="project-feature"><i class="fas fa-bug"></i> 病虫害预警</span> <span class="project-feature"><i class="fas fa-leaf"></i> 生长监测</span> </div> <div class="project-actions"> <button class="btn btn-primary w-100"><i class="fas fa-info-circle me-2"></i>查看详情</button> </div> </div> </div> </div> <div class="col-md-6 mb-4"> <div class="project-card"> <img src="images/digital-farm4.jpg" alt="安徽智慧稽田示范基地" class="project-img"> <div class="project-info"> <div class="project-title"> <span>安徽智慧稽田示范基地</span> <span class="project-badge">计划中</span> </div> <div class="project-desc">与安徽省农业农村厅和合肥市政府合作建设的智慧稽田示范基地，采用全程机械化、自动化和智能化技术，实现水稽种植全过程数字化管理</div> <div class="project-meta"> <div class="project-location"><i class="fas fa-map-marker-alt"></i> 安徽省合肥市</div> <div class="project-date"><i class="fas fa-calendar-alt"></i> 2023-09 至今</div> </div> <div class="project-features"> <span class="project-feature"><i class="fas fa-tractor"></i> 全程机械化</span> <span class="project-feature"><i class="fas fa-satellite"></i> 遥感监测</span> <span class="project-feature"><i class="fas fa-cloud-rain"></i> 智能灵源</span> </div> <div class="project-actions"> <button class="btn btn-primary w-100"><i class="fas fa-info-circle me-2"></i>查看详情</button> </div> </div> </div> </div> </div> <div class="text-center mt-3"> <button class="btn btn-outline-primary">查看更多试点项目</button> </div> </div> <!-- 数字农场优势 --> <div class="digital-card mb-4"> <h4 class="mb-4">数字农场优势</h4> <div class="row"> <div class="col-md-6 mb-4"> <div class="benefit-card"> <div class="benefit-header"> <div class="benefit-icon"> <i class="fas fa-chart-line"></i> </div> <div> <div class="benefit-title">提高生产效率</div> <div class="benefit-subtitle">平均产量提升32%</div> </div> </div> <div class="benefit-body"> <div class="benefit-description">
通过物联网、大数据和人工智能技术，实现农业生产的精准化管理，包括精准播种、精准施肥、精准灵源和精准病虫害防治，显著提高作物产量和质量。根据试点数据，平均产量提升32%，农药使用量减少25%，肥料利用率提高20%。
</div> </div> </div> </div> <div class="col-md-6 mb-4"> <div class="benefit-card"> <div class="benefit-header"> <div class="benefit-icon"> <i class="fas fa-leaf"></i> </div> <div> <div class="benefit-title">提升生态环保水平</div> <div class="benefit-subtitle">减少环境污染</div> </div> </div> <div class="benefit-body"> <div class="benefit-description">
数字农场采用精准农业技术，显著减少农药、化肥等化学品的使用，降低农业面源污染。同时，通过智能灵源系统，提高水资源利用效率，减少水资源浪费。试点数据显示，农药使用量减少25%，化肥使用量减少20%，灵源效率提高30%。
</div> </div> </div> </div> <div class="col-md-6 mb-4"> <div class="benefit-card"> <div class="benefit-header"> <div class="benefit-icon"> <i class="fas fa-coins"></i> </div> <div> <div class="benefit-title">提高农民收入</div> <div class="benefit-subtitle">平均增收40%</div> </div> </div> <div class="benefit-body"> <div class="benefit-description">
数字农场通过提高产量、降低成本和提升产品质量，显著提高了农民收入。同时，数字农场的品牌效应和示范作用，也提升了农产品的销售价格和市场竞争力。试点数据显示，参与农户平均增收超过40%，农产品销售价格提升15-20%。
</div> </div> </div> </div> <div class="col-md-6 mb-4"> <div class="benefit-card"> <div class="benefit-header"> <div class="benefit-icon"> <i class="fas fa-graduation-cap"></i> </div> <div> <div class="benefit-title">推动农业现代化</div> <div class="benefit-subtitle">引领农业发展方向</div> </div> </div> <div class="benefit-body"> <div class="benefit-description">
数字农场试点项目作为农业现代化的示范和模板，引领了区域农业发展方向。通过技术培训、现场参观和经验分享，推动先进农业技术和管理模式的广泛应用。试点项目已吸引超过5000名农民参观学习，带动周边地区超过100个农业合作社采用数字农业技术。
</div> </div> </div> </div> </div> </div> <!-- 合作流程 --> <div class="digital-card mb-4"> <h4 class="mb-4">合作流程</h4> <div class="step-card"> <div class="step-header"> <div class="step-number">1</div> <div class="step-title">项目申请与评估</div> </div> <div class="step-body"> <div class="step-description">
地方政府或农业主管部门提交数字农场试点项目申请，包括项目背景、目标、实施计划、预期效益等。我们将组织专家团队进行项目评估和可行性分析。
</div> </div> <div class="step-connector"></div> </div> <div class="step-card"> <div class="step-header"> <div class="step-number">2</div> <div class="step-title">签署合作协议</div> </div> <div class="step-body"> <div class="step-description">
项目评估通过后，双方签署数字农场试点项目合作协议，明确双方的权利、义务、资源投入、技术支持、成果共享等内容，为项目实施提供制度保障。
</div> </div> <div class="step-connector"></div> </div> <div class="step-card"> <div class="step-header"> <div class="step-number">3</div> <div class="step-title">项目规划与设计</div> </div> <div class="step-body"> <div class="step-description">
我们的专家团队与政府相关部门共同制定详细的项目规划和实施方案，包括技术方案、设备选型、系统架构、数据标准、实施路线图等，确保项目的可行性和科学性。
</div> </div> <div class="step-connector"></div> </div> <div class="step-card"> <div class="step-header"> <div class="step-number">4</div> <div class="step-title">项目建设与实施</div> </div> <div class="step-body"> <div class="step-description">
按照项目规划和实施方案，开展数字农场试点项目建设，包括基础设施建设、设备安装、系统部署、人员培训等工作。我们将提供全程技术支持和质量监督。
</div> </div> <div class="step-connector"></div> </div> <div class="step-card"> <div class="step-header"> <div class="step-number">5</div> <div class="step-title">运营管理与效果评估</div> </div> <div class="step-body"> <div class="step-description">
项目建成后，进入运营管理阶段，包括日常运维、技术支持、数据分析、效果评估等工作。定期开展项目效果评估，总结经验教训，不断优化完善项目实施方案。
</div> </div> </div> </div> </div> <!-- 右侧内容 --> <div class="col-md-4"> <!-- 申请合作 --> <div class="digital-card"> <h4 class="mb-4">申请合作</h4> <div class="alert alert-success mb-3"> <i class="fas fa-info-circle me-2"></i>
欢迎各级政府部门、农业企业和科研机构申请数字农场试点项目合作。
</div> <form> <div class="mb-3"> <label class="form-label">申请单位名称</label> <input type="text" class="form-control" placeholder="请输入单位名称"> </div> <div class="mb-3"> <label class="form-label">单位类型</label> <select class="form-select"> <option>政府部门</option> <option>农业企业</option> <option>科研机构</option> <option>其他</option> </select> </div> <div class="mb-3"> <label class="form-label">联系人</label> <input type="text" class="form-control" placeholder="请输入联系人姓名"> </div> <div class="mb-3"> <label class="form-label">联系电话</label> <input type="tel" class="form-control" placeholder="请输入联系电话"> </div> <div class="mb-3"> <label class="form-label">电子邮箱</label> <input type="email" class="form-control" placeholder="请输入电子邮箱"> </div> <div class="mb-3"> <label class="form-label">项目简介</label> <textarea class="form-control" rows="4" placeholder="请简要描述项目背景、目标和预期效益"></textarea> </div> <div class="d-grid"> <button class="btn btn-success">提交申请</button> </div> </form> </div> <!-- 合作伙伴 --> <div class="digital-card mt-4"> <h4 class="mb-4">合作伙伴</h4> <div class="partner-card"> <img src="images/partner1.jpg" alt="农业农村部" class="partner-logo"> <div class="partner-info"> <div class="partner-name">农业农村部</div> <div class="partner-role">政策支持与指导</div> <div class="partner-desc">提供政策支持和指导，推动数字农业发展和农业现代化进程</div> </div> </div> <div class="partner-card"> <img src="images/partner2.jpg" alt="科技部" class="partner-logo"> <div class="partner-info"> <div class="partner-name">科技部</div> <div class="partner-role">技术支持与研发</div> <div class="partner-desc">提供技术支持和研发合作，推动农业科技创新和成果转化</div> </div> </div> <div class="partner-card"> <img src="images/partner3.jpg" alt="中国农业大学" class="partner-logo"> <div class="partner-info"> <div class="partner-name">中国农业大学</div> <div class="partner-role">学术支持与人才培养</div> <div class="partner-desc">提供学术支持和人才培养，开展数字农业研究和技术开发</div> </div> </div> </div> <!-- 新闻动态 --> <div class="digital-card mt-4"> <h4 class="mb-4">新闻动态</h4> <div class="news-card"> <div class="news-date">2023-10-15</div> <div class="news-title">我国首个数字农业国家级示范区在浙江成立</div> <div class="news-content">近日，我国首个数字农业国家级示范区在浙江省正式成立。该示范区将采用物联网、大数据、人工智能等技术，全面推动农业生产、经营、管理和服务的数字化转型。</div> <div class="news-source">来源：人民日报</div> </div> <div class="news-card"> <div class="news-date">2023-09-20</div> <div class="news-title">我公司与江苏省农业农村厅签署数字农业战略合作协议</div> <div class="news-content">我公司与江苏省农业农村厅签署数字农业战略合作协议，共同推动江苏省数字农业发展。双方将在苏州市建设数字农业产业园，打造全产业链数字化示范。</div> <div class="news-source">来源：公司新闻</div> </div> <div class="news-card"> <div class="news-date">2023-08-10</div> <div class="news-title">山东智慧果园示范基地项目正式启动</div> <div class="news-content">山东智慧果园示范基地项目在烟台市正式启动。该项目将采用物联网、人工智能等技术，实现果园智能管理和精准农业，提高果品产量和质量。</div> <div class="news-source">来源：山东农业信息网</div> </div> </div> <!-- 试点地图 --> <div class="digital-card mt-4"> <h4 class="mb-4">试点地图</h4> <div class="map-container"> <img src="images/digital-farm-map.jpg" alt="数字农场试点地图" style="width: 100%; height: 100%; object-fit: cover;"> </div> <div class="mt-3 text-center"> <button class="btn btn-outline-primary"><i class="fas fa-map-marked-alt me-2"></i>查看全国试点分布</button> </div> </div> </div> </div> </div> <!-- 页脚 -->
<div id="footer-container"></div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="js/login-config.js"></script>
<script src="js/components.js"></script>
<script>
// 使用 common.js 加载导航栏

// 申请合作表单提交
document.querySelector('form .btn-success').addEventListener('click', function(e) {
e.preventDefault();
alert('申请已提交，我们将尽快与您联系！');
});
// 查看详情按钮点击事件
document.querySelectorAll('.project-actions .btn').forEach(btn => {
btn.addEventListener('click', function() {
const projectName = this.closest('.project-card').querySelector('.project-title span').textContent;
alert(`正在查看 ${projectName} 的详细信息，请稍后...`);
});
});
// 查看更多试点项目按钮点击事件
document.querySelector('.btn-outline-primary').addEventListener('click', function() {
alert('正在加载更多试点项目，请稍后...');
});
// 申请合作按钮点击事件
document.querySelector('.btn-success.btn-lg').addEventListener('click', function() {
document.querySelector('.digital-card:nth-child(1)').scrollIntoView({ behavior: 'smooth' });
});
// 查看试点地图按钮点击事件
document.querySelector('.btn-outline-light.btn-lg').addEventListener('click', function() {
document.querySelector('.digital-card:nth-child(4)').scrollIntoView({ behavior: 'smooth' });
});

// 确保导航栏在页面加载完成后可见
window.onload = function() {
    // 确保导航栏可见
    const navbarContainer = document.getElementById('navbar-container');
    if (navbarContainer) {
        navbarContainer.style.display = '';
    }
};
</script> </body> </html>