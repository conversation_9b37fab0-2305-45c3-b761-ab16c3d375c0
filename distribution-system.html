<!DOCTYPE html> <html lang="zh-CN"> <head> <meta charset="UTF-8"> <meta name="viewport" content="width=device-width, initial-scale=1.0"> <title>双层分销结构设置 - 智慧农业平台</title> <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet"> <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet"> <link rel="stylesheet" href="css/common.css"> <link rel="stylesheet" href="css/navbar.css"> <style>
:root {
--primary-color: #28a745;
--secondary-color: #6c757d;
--light-bg: #f8f9fa;
--card-shadow: 0 4px 6px rgba(0,0,0,0.1);
--transition: all 0.3s ease;
--distribution-color: #8e44ad;
--level1-color: #3498db;
--level2-color: #e67e22;
}
body {
background-color: var(--light-bg);
font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
color: #333;
}
.distribution-header {
background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80');
background-size: cover;
background-position: center;
height: 200px;
color: white;
display: flex;
align-items: center;
margin-bottom: 30px;
border-radius: 10px;
}
.distribution-card {
background: white;
padding: 25px;
border-radius: 12px;
box-shadow: var(--card-shadow);
margin-bottom: 30px;
transition: var(--transition);
}

.distribution-card h4 {
  position: relative;
  padding-bottom: 12px;
  margin-bottom: 20px;
  color: #333;
  font-weight: 600;
}

.distribution-card h4::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 50px;
  height: 3px;
  background: var(--distribution-color);
  border-radius: 3px;
}
.stat-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 20px;
transition: var(--transition);
}
.stat-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.stat-icon {
font-size: 2rem;
color: var(--distribution-color);
margin-bottom: 10px;
}
.stat-value {
font-size: 1.5rem;
font-weight: bold;
color: var(--distribution-color);
margin-bottom: 5px;
}
.stat-label {
font-size: 0.9rem;
color: var(--secondary-color);
}
.distribution-structure {
position: relative;
padding: 10px;
margin-bottom: 30px;
}
.structure-level {
background-color: white;
border-radius: 12px;
padding: 25px;
margin-bottom: 25px;
box-shadow: var(--card-shadow);
position: relative;
transition: all 0.3s ease;
border-left: 5px solid transparent;
}

.structure-level:hover {
box-shadow: 0 10px 20px rgba(0,0,0,0.1);
transform: translateY(-3px);
}

.structure-level:nth-child(1) {
border-left-color: var(--level1-color);
}

.structure-level:nth-child(3) {
border-left-color: var(--level2-color);
}
.level-header {
display: flex;
justify-content: space-between;
align-items: center;
margin-bottom: 20px;
padding-bottom: 15px;
border-bottom: 1px solid rgba(0,0,0,0.05);
}

.level-title {
font-weight: 600;
font-size: 1.2rem;
display: flex;
align-items: center;
color: #333;
}
.level-icon {
width: 40px;
height: 40px;
border-radius: 50%;
display: flex;
align-items: center;
justify-content: center;
margin-right: 12px;
color: white;
box-shadow: 0 3px 6px rgba(0,0,0,0.1);
transition: all 0.3s ease;
}

.level-title:hover .level-icon {
transform: scale(1.1);
}
.level1-icon {
background-color: var(--level1-color);
}
.level2-icon {
background-color: var(--level2-color);
}
.level-actions {
display: flex;
gap: 10px;
}
.level-content {
margin-bottom: 15px;
}
.level-info {
display: flex;
justify-content: space-between;
margin-bottom: 10px;
}
.level-label {
color: var(--secondary-color);
}
.level-value {
font-weight: bold;
}
.level-description {
color: var(--secondary-color);
margin-bottom: 15px;
}
.level-members {
display: flex;
flex-wrap: wrap;
gap: 10px;
margin-bottom: 15px;
}
.member-avatar {
width: 50px;
height: 50px;
border-radius: 50%;
object-fit: cover;
border: 3px solid white;
box-shadow: 0 3px 6px rgba(0,0,0,0.15);
transition: var(--transition);
}

.member-card:hover .member-avatar {
transform: scale(1.05);
box-shadow: 0 5px 10px rgba(0,0,0,0.2);
}
.member-more {
width: 40px;
height: 40px;
border-radius: 50%;
background-color: var(--light-bg);
display: flex;
align-items: center;
justify-content: center;
font-size: 0.9rem;
color: var(--secondary-color);
cursor: pointer;
}
.structure-connector {
position: absolute;
left: 50%;
width: 3px;
background: linear-gradient(to bottom, var(--level1-color), var(--level2-color));
z-index: 0;
transform: translateX(-50%);
}

.connector-1-2 {
top: 160px;
height: 50px;
}

.connector-1-2::before, .connector-1-2::after {
content: '';
position: absolute;
width: 12px;
height: 12px;
border-radius: 50%;
left: 50%;
transform: translateX(-50%);
animation: pulse 2s infinite;
}

.connector-1-2::before {
top: -6px;
background-color: var(--level1-color);
}

.connector-1-2::after {
bottom: -6px;
background-color: var(--level2-color);
}

@keyframes pulse {
0% { transform: translateX(-50%) scale(1); opacity: 1; }
50% { transform: translateX(-50%) scale(1.2); opacity: 0.7; }
100% { transform: translateX(-50%) scale(1); opacity: 1; }
}
.commission-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 15px;
transition: var(--transition);
}
.commission-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.commission-header {
display: flex;
justify-content: space-between;
align-items: center;
margin-bottom: 15px;
}
.commission-title {
font-weight: bold;
display: flex;
align-items: center;
}
.commission-icon {
color: var(--distribution-color);
margin-right: 10px;
}
.commission-body {
margin-bottom: 15px;
}
.commission-info {
display: flex;
justify-content: space-between;
margin-bottom: 10px;
}
.commission-label {
color: var(--secondary-color);
}
.commission-value {
font-weight: bold;
}
.commission-slider {
margin-bottom: 15px;
}
.slider-header {
display: flex;
justify-content: space-between;
margin-bottom: 5px;
}
.slider-label {
font-size: 0.9rem;
color: var(--secondary-color);
}
.slider-value {
font-size: 0.9rem;
font-weight: bold;
}
.slider-container {
position: relative;
height: 8px;
background-color: #e9ecef;
border-radius: 4px;
margin-bottom: 5px;
}
.slider-fill {
position: absolute;
top: 0;
left: 0;
height: 100%;
border-radius: 4px;
}
.level1-fill {
background-color: var(--level1-color);
}
.level2-fill {
background-color: var(--level2-color);
}
.slider-markers {
display: flex;
justify-content: space-between;
font-size: 0.8rem;
color: var(--secondary-color);
}
.rule-card {
background: var(--light-bg);
padding: 20px;
border-radius: 12px;
margin-bottom: 20px;
transition: var(--transition);
border-left: 3px solid var(--distribution-color);
}

.rule-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
background-color: white;
}
.rule-header {
display: flex;
justify-content: space-between;
align-items: center;
margin-bottom: 15px;
}
.rule-title {
font-weight: bold;
display: flex;
align-items: center;
}
.rule-icon {
color: var(--distribution-color);
margin-right: 10px;
}
.rule-body {
margin-bottom: 15px;
}
.rule-item {
display: flex;
margin-bottom: 20px;
padding-bottom: 15px;
border-bottom: 1px dashed rgba(0,0,0,0.1);
}

.rule-item:last-child {
border-bottom: none;
padding-bottom: 0;
margin-bottom: 0;
}
.rule-number {
width: 30px;
height: 30px;
border-radius: 50%;
background-color: var(--distribution-color);
color: white;
display: flex;
align-items: center;
justify-content: center;
margin-right: 15px;
font-size: 0.9rem;
flex-shrink: 0;
box-shadow: 0 3px 6px rgba(0,0,0,0.1);
transition: var(--transition);
}

.rule-item:hover .rule-number {
transform: scale(1.1) rotate(10deg);
}
.rule-content {
flex: 1;
}
.rule-text {
font-weight: 600;
margin-bottom: 8px;
color: #333;
font-size: 1.05rem;
}
.rule-note {
font-size: 0.9rem;
color: var(--secondary-color);
line-height: 1.5;
}
.member-card {
background: var(--light-bg);
padding: 18px;
border-radius: 12px;
margin-bottom: 20px;
transition: var(--transition);
display: flex;
align-items: center;
border-left: 3px solid transparent;
}

.member-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
background-color: white;
border-left-color: var(--distribution-color);
}
.member-info {
flex: 1;
margin-left: 15px;
}
.member-name {
font-weight: 600;
margin-bottom: 6px;
color: #333;
font-size: 1.1rem;
}

.member-role {
font-size: 0.9rem;
color: var(--secondary-color);
margin-bottom: 10px;
padding-bottom: 8px;
border-bottom: 1px dashed rgba(0,0,0,0.1);
}

.member-stats {
display: flex;
gap: 15px;
font-size: 0.9rem;
flex-wrap: wrap;
}

.member-stat {
display: flex;
align-items: center;
color: #333;
padding: 4px 0;
}

.member-stat-icon {
color: var(--distribution-color);
margin-right: 5px;
font-size: 1rem;
}

.member-actions {
display: flex;
gap: 10px;
margin-left: 15px;
}

.member-actions .btn {
width: 36px;
height: 36px;
display: flex;
align-items: center;
justify-content: center;
transition: var(--transition);
}

.member-actions .btn:hover {
transform: scale(1.1);
}
.faq-item {
margin-bottom: 15px;
}
.faq-question {
font-weight: bold;
margin-bottom: 5px;
cursor: pointer;
display: flex;
justify-content: space-between;
align-items: center;
}
.faq-answer {
color: var(--secondary-color);
display: none;
padding: 10px 0;
}
.faq-answer.active {
display: block;
}
.chart-container {
position: relative;
height: 300px;
width: 100%;
}
</style> </head> <body> <!-- 导航栏 -->
<div id="navbar-container"></div>
<!-- 主要内容 --> <div class="container py-5"> <div class="distribution-header"> <div class="container"> <h1>双层分销结构设置</h1> <p class="lead">设置和管理您的双层分销体系，包括地主和村民两级分销商，优化佣金结构，提高销售效率</p> </div> </div> <!-- 顶部统计卡片 --> <div class="row mb-4"> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-users stat-icon"></i> <div class="stat-value">256</div> <div class="stat-label">总分销商数</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-user-tie stat-icon"></i> <div class="stat-value">68</div> <div class="stat-label">一级分销商(地主)</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-user-friends stat-icon"></i> <div class="stat-value">188</div> <div class="stat-label">二级分销商(村民)</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-yen-sign stat-icon"></i> <div class="stat-value">128,560</div> <div class="stat-label">本月分销佣金</div> </div> </div> </div> <!-- 主要内容区域 --> <div class="row"> <!-- 左侧内容 --> <div class="col-md-8"> <!-- 分销结构设置 --> <div class="distribution-card"> <div class="d-flex justify-content-between align-items-center mb-4"> <h4>分销结构设置</h4> <button class="btn btn-success"><i class="fas fa-save me-2"></i>保存设置</button> </div> <div class="distribution-structure"> <!-- 一级分销商（地主） --> <div class="structure-level"> <div class="level-header"> <div class="level-title"> <div class="level-icon level1-icon"> <i class="fas fa-user-tie"></i> </div> <span>一级分销商（地主）</span> </div> <div class="level-actions"> <button class="btn btn-sm btn-outline-primary"><i class="fas fa-cog me-1"></i>设置</button> <button class="btn btn-sm btn-outline-success"><i class="fas fa-user-plus me-1"></i>添加</button> </div> </div> <div class="level-content"> <div class="level-info"> <div class="level-label">当前数量：</div> <div class="level-value">68人</div> </div> <div class="level-info"> <div class="level-label">佣金比例：</div> <div class="level-value">15%</div> </div> <div class="level-info"> <div class="level-label">成为条件：</div> <div class="level-value">实名认证 + 资质审核</div> </div> <div class="level-description">
一级分销商（地主）是我们的核心合作伙伴，负责特定区域的产品推广和销售，并可以发展二级分销商（村民）。他们可以获得直接销售佣金和二级分销商销售的间接佣金。
</div> <div class="level-members"> <img src="images/avatar1.jpg" alt="分销商头像" class="member-avatar"> <img src="images/avatar2.jpg" alt="分销商头像" class="member-avatar"> <img src="images/avatar3.jpg" alt="分销商头像" class="member-avatar"> <img src="images/avatar4.jpg" alt="分销商头像" class="member-avatar"> <img src="images/avatar5.jpg" alt="分销商头像" class="member-avatar"> <div class="member-more">+63</div> </div> </div> </div> <!-- 连接线 --> <div class="structure-connector connector-1-2"></div> <!-- 二级分销商（村民） --> <div class="structure-level"> <div class="level-header"> <div class="level-title"> <div class="level-icon level2-icon"> <i class="fas fa-user-friends"></i> </div> <span>二级分销商（村民）</span> </div> <div class="level-actions"> <button class="btn btn-sm btn-outline-primary"><i class="fas fa-cog me-1"></i>设置</button> <button class="btn btn-sm btn-outline-success"><i class="fas fa-user-plus me-1"></i>添加</button> </div> </div> <div class="level-content"> <div class="level-info"> <div class="level-label">当前数量：</div> <div class="level-value">188人</div> </div> <div class="level-info"> <div class="level-label">佣金比例：</div> <div class="level-value">8%</div> </div> <div class="level-info"> <div class="level-label">成为条件：</div> <div class="level-value">手机号验证</div> </div> <div class="level-description">
二级分销商（村民）是由一级分销商发展的团队成员，主要负责产品的终端销售。他们可以获得直接销售佣金，同时也为上级地主贡献间接佣金。成为村民的门槛较低，便于快速发展团队。
</div> <div class="level-members"> <img src="images/avatar6.jpg" alt="分销商头像" class="member-avatar"> <img src="images/avatar7.jpg" alt="分销商头像" class="member-avatar"> <img src="images/avatar8.jpg" alt="分销商头像" class="member-avatar"> <img src="images/avatar9.jpg" alt="分销商头像" class="member-avatar"> <img src="images/avatar10.jpg" alt="分销商头像" class="member-avatar"> <div class="member-more">+183</div> </div> </div> </div> </div> </div> <!-- 佣金设置 --> <div class="distribution-card"> <h4 class="mb-4">佣金设置</h4> <div class="commission-card"> <div class="commission-header"> <div class="commission-title"> <i class="fas fa-percentage commission-icon"></i> <span>一级分销商（地主）佣金设置</span> </div> </div> <div class="commission-body"> <div class="commission-info"> <div class="commission-label">直接销售佣金：</div> <div class="commission-value">15%</div> </div> <div class="commission-info"> <div class="commission-label">团队间接佣金：</div> <div class="commission-value">3%</div> </div> <div class="commission-slider"> <div class="slider-header"> <div class="slider-label">直接销售佣金比例</div> <div class="slider-value">15%</div> </div> <div class="slider-container"> <div class="slider-fill level1-fill" style="width: 60%;"></div> </div> <div class="slider-markers"> <span>5%</span> <span>10%</span> <span>15%</span> <span>20%</span> <span>25%</span> </div> </div> <div class="commission-slider"> <div class="slider-header"> <div class="slider-label">团队间接佣金比例</div> <div class="slider-value">3%</div> </div> <div class="slider-container"> <div class="slider-fill level1-fill" style="width: 30%;"></div> </div> <div class="slider-markers"> <span>1%</span> <span>3%</span> <span>5%</span> <span>7%</span> <span>10%</span> </div> </div> </div> </div> <div class="commission-card"> <div class="commission-header"> <div class="commission-title"> <i class="fas fa-percentage commission-icon"></i> <span>二级分销商（村民）佣金设置</span> </div> </div> <div class="commission-body"> <div class="commission-info"> <div class="commission-label">直接销售佣金：</div> <div class="commission-value">8%</div> </div> <div class="commission-slider"> <div class="slider-header"> <div class="slider-label">直接销售佣金比例</div> <div class="slider-value">8%</div> </div> <div class="slider-container"> <div class="slider-fill level2-fill" style="width: 40%;"></div> </div> <div class="slider-markers"> <span>3%</span> <span>5%</span> <span>8%</span> <span>10%</span> <span>15%</span> </div> </div> </div> </div> <div class="commission-card"> <div class="commission-header"> <div class="commission-title"> <i class="fas fa-chart-line commission-icon"></i> <span>阶梯佣金设置</span> </div> </div> <div class="commission-body"> <div class="alert alert-info"> <i class="fas fa-info-circle me-2"></i>
阶梯佣金可以激励分销商提高销售业绩。当分销商的销售金额达到特定阶梯时，将获得更高的佣金比例。
</div> <div class="table-responsive"> <table class="table table-bordered"> <thead class="table-light"> <tr> <th style="color: #333; background-color: #f8f9fa;">销售金额阶梯</th> <th style="color: #333; background-color: #f8f9fa;">一级分销商佣金</th> <th style="color: #333; background-color: #f8f9fa;">二级分销商佣金</th> </tr> </thead> <tbody> <tr> <td style="color: #333;">0-5,000元</td> <td style="color: #333;">15%</td> <td style="color: #333;">8%</td> </tr> <tr> <td style="color: #333;">5,001-10,000元</td> <td style="color: #333;">18%</td> <td style="color: #333;">10%</td> </tr> <tr> <td style="color: #333;">10,001-50,000元</td> <td style="color: #333;">20%</td> <td style="color: #333;">12%</td> </tr> <tr> <td style="color: #333;">50,001元以上</td> <td style="color: #333;">25%</td> <td style="color: #333;">15%</td> </tr> </tbody> </table> </div> </div> </div> </div> </div> <!-- 右侧内容 --> <div class="col-md-4"> <!-- 分销规则 --> <div class="distribution-card"> <h4 class="mb-4">分销规则</h4> <div class="rule-card"> <div class="rule-header"> <div class="rule-title"> <i class="fas fa-book rule-icon"></i> <span>基本规则</span> </div> </div> <div class="rule-body"> <div class="rule-item"> <div class="rule-number">1</div> <div class="rule-content"> <div class="rule-text">分销商需要遵守平台规则，不得违反国家法律法规。</div> <div class="rule-note">如发现违规行为，平台有权终止合作并追究相关责任。</div> </div> </div> <div class="rule-item"> <div class="rule-number">2</div> <div class="rule-content"> <div class="rule-text">一级分销商（地主）需要实名认证并通过资质审核。</div> <div class="rule-note">需提供身份证、联系方式和相关资质证明。</div> </div> </div> <div class="rule-item"> <div class="rule-number">3</div> <div class="rule-content"> <div class="rule-text">二级分销商（村民）需要手机号验证。</div> <div class="rule-note">需由一级分销商邀请或自行注册并绑定上级分销商。</div> </div> </div> <div class="rule-item"> <div class="rule-number">4</div> <div class="rule-content"> <div class="rule-text">佣金结算周期为每月一次，每月底结算上月佣金。</div> <div class="rule-note">佣金将在每月底结算，并在5个工作日内发放到分销商账户。</div> </div> </div> <div class="rule-item"> <div class="rule-number">5</div> <div class="rule-content"> <div class="rule-text">分销商需要遵守平台的产品宣传规范，不得虚假宣传。</div> <div class="rule-note">如发现虚假宣传，平台有权冻结佣金并要求整改。</div> </div> </div> </div> </div> </div> <!-- 分销商管理 --> <div class="distribution-card"> <div class="d-flex justify-content-between align-items-center mb-4"> <h4>分销商管理</h4> <div class="input-group" style="width: 200px;"> <input type="text" class="form-control form-control-sm" placeholder="搜索分销商"> <button class="btn btn-sm btn-outline-secondary"><i class="fas fa-search"></i></button> </div> </div> <div class="member-card"> <img src="images/avatar1.jpg" alt="分销商头像" class="member-avatar"> <div class="member-info"> <div class="member-name">王地主</div> <div class="member-role">一级分销商 | 浙江区域</div> <div class="member-stats"> <div class="member-stat"> <i class="fas fa-user-friends member-stat-icon"></i> <span>32个村民</span> </div> <div class="member-stat"> <i class="fas fa-shopping-cart member-stat-icon"></i> <span>128笔订单</span> </div> <div class="member-stat"> <i class="fas fa-yen-sign member-stat-icon"></i> <span>25,680元</span> </div> </div> </div> <div class="member-actions"> <button class="btn btn-sm btn-outline-primary"><i class="fas fa-eye"></i></button> <button class="btn btn-sm btn-outline-success"><i class="fas fa-edit"></i></button> </div> </div> <div class="member-card"> <img src="images/avatar2.jpg" alt="分销商头像" class="member-avatar"> <div class="member-info"> <div class="member-name">李地主</div> <div class="member-role">一级分销商 | 江苏区域</div> <div class="member-stats"> <div class="member-stat"> <i class="fas fa-user-friends member-stat-icon"></i> <span>28个村民</span> </div> <div class="member-stat"> <i class="fas fa-shopping-cart member-stat-icon"></i> <span>96笔订单</span> </div> <div class="member-stat"> <i class="fas fa-yen-sign member-stat-icon"></i> <span>18,450元</span> </div> </div> </div> <div class="member-actions"> <button class="btn btn-sm btn-outline-primary"><i class="fas fa-eye"></i></button> <button class="btn btn-sm btn-outline-success"><i class="fas fa-edit"></i></button> </div> </div> <div class="member-card"> <img src="images/avatar6.jpg" alt="分销商头像" class="member-avatar"> <div class="member-info"> <div class="member-name">张村民</div> <div class="member-role">二级分销商 | 王地主团队</div> <div class="member-stats"> <div class="member-stat"> <i class="fas fa-shopping-cart member-stat-icon"></i> <span>32笔订单</span> </div> <div class="member-stat"> <i class="fas fa-yen-sign member-stat-icon"></i> <span>6,450元</span> </div> </div> </div> <div class="member-actions"> <button class="btn btn-sm btn-outline-primary"><i class="fas fa-eye"></i></button> <button class="btn btn-sm btn-outline-success"><i class="fas fa-edit"></i></button> </div> </div> <div class="text-center mt-3"> <button class="btn btn-outline-primary">查看更多</button> </div> </div> <!-- 常见问题 --> <div class="distribution-card"> <h4 class="mb-4">常见问题</h4> <div class="faq-item"> <div class="faq-question" onclick="toggleFaq(this)"> <span>如何成为地主（一级分销商）？</span> <i class="fas fa-chevron-down"></i> </div> <div class="faq-answer">
要成为地主，您需要完成实名认证并提交资质审核申请。审核通过后，您将获得地主资格，可以发展自己的村民团队，并享受更高的佣金比例。请在「个人中心」-「成为分销商」中提交申请。
</div> </div> <div class="faq-item"> <div class="faq-question" onclick="toggleFaq(this)"> <span>地主和村民的佣金差异是什么？</span> <i class="fas fa-chevron-down"></i> </div> <div class="faq-answer">
地主（一级分销商）享有更高的直接销售佣金（15%），并且可以享受团队间接佣金（3%）。村民（二级分销商）的直接销售佣金为8%。当销售金额达到特定阶梯时，两级分销商都可以获得更高的佣金比例。
</div> </div> <div class="faq-item"> <div class="faq-question" onclick="toggleFaq(this)"> <span>佣金如何结算和提现？</span> <i class="fas fa-chevron-down"></i> </div> <div class="faq-answer">
佣金每月结算一次，系统会在每月底结算上月的佣金，并在5个工作日内发放到您的账户。当佣金余额达到100元以上时，您可以申请提现到绑定的银行卡或支付宝账户。提现申请在1-3个工作日内处理完成。
</div> </div> <div class="faq-item"> <div class="faq-question" onclick="toggleFaq(this)"> <span>如何提高我的分销业绩？</span> <i class="fas fa-chevron-down"></i> </div> <div class="faq-answer">
提高分销业绩的关键是发展更多的团队成员和提高产品转化率。您可以使用平台提供的推广工具，如海报生成器、专属推广链接等。正确介绍产品价值，定期与客户跟进，并参加平台的培训活动，也能显著提升您的销售能力。
</div> </div> </div> </div> </div> </div> <!-- 页脚 -->
<div id="footer-container"></div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="js/login-config.js"></script>
<script src="js/components.js"></script>

 <script src="https://cdn.jsdelivr.net/npm/chart.js"></script> <script>
// 初始化页面
document.addEventListener('DOMContentLoaded', function() {
    // 设置当前页面的导航链接为激活状态
    setActiveNavItem();
});

// 切换FAQ显示/隐藏
function toggleFaq(element) {
const answer = element.nextElementSibling;
const icon = element.querySelector('i');
if (answer.classList.contains('active')) {
answer.classList.remove('active');
icon.classList.remove('fa-chevron-up');
icon.classList.add('fa-chevron-down');
} else {
answer.classList.add('active');
icon.classList.remove('fa-chevron-down');
icon.classList.add('fa-chevron-up');
}
}
// 佣金滑块交互
document.querySelectorAll('.slider-container').forEach(slider => {
slider.addEventListener('click', function(e) {
const sliderWidth = this.offsetWidth;
const clickPosition = e.offsetX;
const percentage = (clickPosition / sliderWidth) * 100;
const sliderFill = this.querySelector('.slider-fill');
sliderFill.style.width = percentage + '%';
// 更新显示的数值
const sliderHeader = this.previousElementSibling;
const sliderValue = sliderHeader.querySelector('.slider-value');
// 根据不同的滑块设置不同的值范围
let value;
if (sliderValue.textContent.includes('%')) {
if (sliderFill.classList.contains('level1-fill')) {
if (percentage <= 20) value = '5%';
else if (percentage <= 40) value = '10%';
else if (percentage <= 60) value = '15%';
else if (percentage <= 80) value = '20%';
else value = '25%';
} else {
if (percentage <= 20) value = '3%';
else if (percentage <= 40) value = '5%';
else if (percentage <= 60) value = '8%';
else if (percentage <= 80) value = '10%';
else value = '15%';
}
}
sliderValue.textContent = value;
// 更新佣金信息显示
const commissionCard = this.closest('.commission-card');
const commissionInfo = commissionCard.querySelector('.commission-info');
if (commissionInfo) {
const commissionValue = commissionInfo.querySelector('.commission-value');
if (commissionValue) {
commissionValue.textContent = value;
}
}
});
});
// 保存设置按钮点击事件
document.querySelector('.btn-success').addEventListener('click', function() {
alert('设置已保存！');
});
// 分销商管理按钮点击事件
document.querySelectorAll('.member-actions .btn').forEach(btn => {
btn.addEventListener('click', function() {
const memberCard = this.closest('.member-card');
const memberName = memberCard.querySelector('.member-name').textContent;
if (this.querySelector('.fa-eye')) {
alert(`查看 ${memberName} 的详细信息`);
} else if (this.querySelector('.fa-edit')) {
alert(`编辑 ${memberName} 的信息`);
}
});
});
// 结构设置按钮点击事件
document.querySelectorAll('.level-actions .btn').forEach(btn => {
btn.addEventListener('click', function() {
const levelTitle = this.closest('.level-header').querySelector('.level-title span').textContent;
if (this.querySelector('.fa-cog')) {
alert(`设置 ${levelTitle} 参数`);
} else if (this.querySelector('.fa-user-plus')) {
alert(`添加新的 ${levelTitle}`);
}
});
});
</script>