<!DOCTYPE html> <html lang="zh-CN"> <head> <meta charset="UTF-8"> <meta name="viewport" content="width=device-width, initial-scale=1.0"> <title>分销商管理 - 智慧农业平台</title> <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet"> <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet"> <link rel="stylesheet" href="css/common.css"> <link rel="stylesheet" href="css/navbar.css"> <style>
:root {
--primary-color: #28a745;
--secondary-color: #6c757d;
--light-bg: #f8f9fa;
--card-shadow: 0 4px 6px rgba(0,0,0,0.1);
--transition: all 0.3s ease;
--distribution-color: #8e44ad;
--level1-color: #3498db;
--level2-color: #e67e22;
}
body {
background-color: var(--light-bg);
font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}
.distributor-header {
background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80');
background-size: cover;
background-position: center;
height: 200px;
color: white;
display: flex;
align-items: center;
margin-bottom: 30px;
border-radius: 10px;
}
.distributor-card {
background: white;
padding: 20px;
border-radius: 10px;
box-shadow: var(--card-shadow);
margin-bottom: 30px;
min-height: auto;
}
.stat-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 20px;
transition: var(--transition);
}
.stat-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.stat-icon {
font-size: 2rem;
color: var(--distribution-color);
margin-bottom: 10px;
}
.stat-value {
font-size: 1.5rem;
font-weight: bold;
color: var(--distribution-color);
margin-bottom: 5px;
}
.stat-label {
font-size: 0.9rem;
color: var(--secondary-color);
}
.member-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 15px;
transition: var(--transition);
display: flex;
align-items: center;
}
.member-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.member-avatar {
width: 60px;
height: 60px;
border-radius: 50%;
object-fit: cover;
margin-right: 15px;
}
.member-info {
flex: 1;
margin-right: 15px;
}
.member-name {
font-weight: bold;
margin-bottom: 5px;
display: flex;
align-items: center;
}
.member-badge {
display: inline-block;
padding: 2px 8px;
border-radius: 15px;
font-size: 0.7rem;
margin-left: 10px;
}
.badge-level1 {
background-color: rgba(52, 152, 219, 0.1);
color: var(--level1-color);
}
.badge-level2 {
background-color: rgba(230, 126, 34, 0.1);
color: var(--level2-color);
}
.member-role {
font-size: 0.9rem;
color: var(--secondary-color);
margin-bottom: 5px;
}
.member-stats {
display: flex;
gap: 15px;
font-size: 0.9rem;
}
.member-stat {
display: flex;
align-items: center;
}
.member-stat-icon {
color: var(--distribution-color);
margin-right: 5px;
}
.member-actions {
display: flex;
gap: 10px;
}
.status-badge {
display: inline-block;
padding: 3px 8px;
border-radius: 15px;
font-size: 0.8rem;
}
.status-active {
background-color: rgba(40, 167, 69, 0.1);
color: var(--primary-color);
}
.status-inactive {
background-color: rgba(108, 117, 125, 0.1);
color: var(--secondary-color);
}
.status-pending {
background-color: rgba(255, 193, 7, 0.1);
color: #ffc107;
}
.filter-btn {
padding: 5px 15px;
border-radius: 20px;
background-color: white;
border: 1px solid #ddd;
margin-right: 10px;
margin-bottom: 10px;
cursor: pointer;
transition: var(--transition);
}
.filter-btn.active {
background-color: var(--primary-color);
color: white;
border-color: var(--primary-color);
}
.filter-btn:hover:not(.active) {
background-color: #f0f0f0;
}
.detail-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 15px;
transition: var(--transition);
}
.detail-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.detail-header {
display: flex;
justify-content: space-between;
align-items: center;
margin-bottom: 15px;
}
.detail-title {
font-weight: bold;
display: flex;
align-items: center;
}
.detail-icon {
color: var(--distribution-color);
margin-right: 10px;
}
.detail-body {
margin-bottom: 15px;
}
.detail-item {
display: flex;
justify-content: space-between;
margin-bottom: 10px;
}
.detail-label {
color: var(--secondary-color);
}
.detail-value {
font-weight: bold;
}
.team-member {
display: flex;
align-items: center;
margin-bottom: 10px;
padding: 10px;
background-color: white;
border-radius: 5px;
transition: var(--transition);
}
.team-member:hover {
transform: translateX(5px);
box-shadow: var(--card-shadow);
}
.team-avatar {
width: 40px;
height: 40px;
border-radius: 50%;
object-fit: cover;
margin-right: 15px;
}
.team-info {
flex: 1;
}
.team-name {
font-weight: bold;
margin-bottom: 3px;
}
.team-role {
font-size: 0.8rem;
color: var(--secondary-color);
}
.team-value {
font-weight: bold;
color: var(--distribution-color);
}
.chart-container {
position: relative;
height: 300px;
width: 100%;
margin-bottom: 20px;
}
.form-section {
margin-bottom: 20px;
}
.form-section-title {
font-weight: bold;
margin-bottom: 15px;
padding-bottom: 10px;
border-bottom: 1px solid #eee;
}
.approval-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 15px;
transition: var(--transition);
}
.approval-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.approval-header {
display: flex;
justify-content: space-between;
align-items: center;
margin-bottom: 15px;
}
.approval-title {
font-weight: bold;
}
.approval-body {
margin-bottom: 15px;
}
.approval-item {
display: flex;
justify-content: space-between;
margin-bottom: 10px;
}
.approval-label {
color: var(--secondary-color);
}
.approval-value {
font-weight: bold;
}
.approval-actions {
display: flex;
justify-content: flex-end;
gap: 10px;
}
.faq-item {
margin-bottom: 15px;
}
.faq-question {
font-weight: bold;
margin-bottom: 5px;
cursor: pointer;
display: flex;
justify-content: space-between;
align-items: center;
}
.faq-answer {
color: var(--secondary-color);
display: none;
padding: 10px 0;
}
.faq-answer.active {
display: block;
}
.nav-tabs .nav-link {
color: var(--secondary-color);
}
.nav-tabs .nav-link.active {
color: var(--distribution-color);
font-weight: bold;
}
.tab-content {
padding: 20px 0;
}
</style> </head> <body> <!-- 导航栏 --> <nav class="navbar navbar-expand-lg navbar-dark"><div class="container"><a class="navbar-brand" href="index.html">智慧农业平台</a><button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"><span class="navbar-toggler-icon"></span></button><div class="collapse navbar-collapse" id="navbarNav"><ul class="navbar-nav ms-auto"><li class="nav-item dropdown"><a class="nav-link dropdown-toggle" href="#" id="module1Dropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false" data-page="module1">基地土地招租与订单种植</a><ul class="dropdown-menu" aria-labelledby="module1Dropdown"><li><a class="dropdown-item" href="land-map.html">动态地图分区浏览</a></li><li><a class="dropdown-item" href="land-detail.html">土地标签信息展示</a></li><li><a class="dropdown-item" href="land-publish.html">土地招租信息发布</a></li><li><a class="dropdown-item" href="land-selection.html">用户在线选地并收藏</a></li><li><a class="dropdown-item" href="land-contract.html">在线签约流程</a></li><li><a class="dropdown-item" href="planting-plan.html">自动生成种植计划模板</a></li><li><a class="dropdown-item" href="planting-dashboard.html">全周期种植看板展示</a></li><li><a class="dropdown-item" href="land-management.html">土地托管服务申请</a></li><li><a class="dropdown-item" href="land-recommendation.html">AI地块推荐系统</a></li></ul></li><li class="nav-item dropdown"><a class="nav-link dropdown-toggle" href="#" id="module2Dropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false" data-page="module2">农产品品牌化运营</a><ul class="dropdown-menu" aria-labelledby="module2Dropdown"><li><a class="dropdown-item" href="brand-story.html">品牌故事展示模块</a></li><li><a class="dropdown-item" href="product-traceability.html">产品溯源系统</a></li><li><a class="dropdown-item" href="certification.html">认证系统接入</a></li><li><a class="dropdown-item" href="brand-customization.html">品牌联名定制功能</a></li><li><a class="dropdown-item" href="live-streaming.html">直播带货与基地实景直播</a></li></ul></li><li class="nav-item dropdown"><a class="nav-link dropdown-toggle" href="#" id="module3Dropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false" data-page="module3">全过程监控系统</a><ul class="dropdown-menu" aria-labelledby="module3Dropdown"><li><a class="dropdown-item" href="monitoring-dashboard.html">智能传感数据看板</a></li><li><a class="dropdown-item" href="alert-system.html">农事预警系统</a></li><li><a class="dropdown-item" href="crop-recognition.html">作物图像识别系统</a></li><li><a class="dropdown-item" href="blockchain-verification.html">关键节点区块链存证</a></li><li><a class="dropdown-item" href="remote-control.html">远程控制农业设备功能</a></li><li><a class="dropdown-item" href="expert-consultation.html">专家在线问诊</a></li></ul></li><li class="nav-item dropdown"><a class="nav-link dropdown-toggle" href="#" id="module4Dropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false" data-page="module4">社群运营体系</a><ul class="dropdown-menu" aria-labelledby="module4Dropdown"><li><a class="dropdown-item" href="distribution-system.html">双层分销结构设置</a></li><li><a class="dropdown-item" href="commission-dashboard.html">分销佣金可视化看板</a></li><li><a class="dropdown-item" href="promotion-tools.html">分销推广工具</a></li><li><a class="dropdown-item" href="invitation-ranking.html">邀请排行榜与激励机制</a></li><li><a class="dropdown-item" href="consumer-conversion.html">消费者转化机制</a></li><li><a class="dropdown-item" href="team-incentives.html">区域团队激励</a></li></ul></li><li class="nav-item dropdown"><a class="nav-link dropdown-toggle" href="#" id="module5Dropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false" data-page="module5">农资农机交易系统</a><ul class="dropdown-menu" aria-labelledby="module5Dropdown"><li><a class="dropdown-item" href="equipment-rental.html">智能农机设备租赁系统</a></li><li><a class="dropdown-item" href="agricultural-supplies.html">农资采购金融服务</a></li><li><a class="dropdown-item" href="used-equipment.html">二手农机交易市场</a></li><li><a class="dropdown-item" href="subscription-service.html">农资订阅服务</a></li><li><a class="dropdown-item" href="ar-preview.html">AR虚拟试用功能</a></li></ul></li><li class="nav-item dropdown"><a class="nav-link dropdown-toggle" href="#" id="module6Dropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false" data-page="module6">运营支撑体系</a><ul class="dropdown-menu" aria-labelledby="module6Dropdown"><li><a class="dropdown-item" href="digital-farm.html">与政府合作数字农场试点</a></li><li><a class="dropdown-item" href="referral-program.html">邀请好友赠农资礼包</a></li><li><a class="dropdown-item" href="kol-incubation.html">新农人KOL孵化计划</a></li><li><a class="dropdown-item" href="exclusive-cooperation.html">与基地签独家合作协议</a></li><li><a class="dropdown-item" href="logistics-center.html">接入顺丰/京东冷链物流</a></li></ul></li></ul></div></div></nav> <!-- 主要内容 --> <div class="container py-5"> <div class="distributor-header"> <div class="container"> <h1>分销商管理</h1> <p class="lead">管理您的分销团队，查看分销商详情，审核新申请，提高团队协作效率和销售业绩</p> </div> </div> <!-- 顶部统计卡片 --> <div class="row mb-4"> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-user-tie stat-icon"></i> <div class="stat-value">68</div> <div class="stat-label">一级分销商(地主)</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-user-friends stat-icon"></i> <div class="stat-value">188</div> <div class="stat-label">二级分销商(村民)</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-user-plus stat-icon"></i> <div class="stat-value">12</div> <div class="stat-label">待审核申请</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-chart-line stat-icon"></i> <div class="stat-value">+15.6%</div> <div class="stat-label">分销商增长率</div> </div> </div> </div> <!-- 主要内容区域 --> <div class="row"> <!-- 左侧内容 --> <div class="col-md-8"> <!-- 分销商列表 --> <div class="distributor-card"> <div class="d-flex justify-content-between align-items-center mb-4"> <h4>分销商列表</h4> <div class="d-flex"> <div class="input-group me-2" style="width: 250px;"> <input type="text" class="form-control form-control-sm" placeholder="搜索分销商名称或ID"> <button class="btn btn-sm btn-outline-secondary"><i class="fas fa-search"></i></button> </div> <button class="btn btn-success btn-sm"><i class="fas fa-user-plus me-1"></i>添加分销商</button> </div> </div> <div class="mb-3"> <button class="filter-btn active">全部</button> <button class="filter-btn">一级分销商</button> <button class="filter-btn">二级分销商</button> <button class="filter-btn">活跃分销商</button> <button class="filter-btn">待审核</button> </div> <!-- 分销商列表内容 --> <div class="member-card"> <img src="images/avatar1.jpg" alt="分销商头像" class="member-avatar"> <div class="member-info"> <div class="member-name">
王地主 <span class="member-badge badge-level1">一级</span> </div> <div class="member-role">浙江区域 | 加入时间：2023-01-15</div> <div class="member-stats"> <div class="member-stat"> <i class="fas fa-user-friends member-stat-icon"></i> <span>32个村民</span> </div> <div class="member-stat"> <i class="fas fa-shopping-cart member-stat-icon"></i> <span>128笔订单</span> </div> <div class="member-stat"> <i class="fas fa-yen-sign member-stat-icon"></i> <span>25,680元</span> </div> </div> </div> <div class="status-badge status-active">活跃</div> <div class="member-actions"> <button class="btn btn-sm btn-outline-primary"><i class="fas fa-eye"></i></button> <button class="btn btn-sm btn-outline-success"><i class="fas fa-edit"></i></button> <button class="btn btn-sm btn-outline-secondary"><i class="fas fa-comment"></i></button> </div> </div> <div class="member-card"> <img src="images/avatar2.jpg" alt="分销商头像" class="member-avatar"> <div class="member-info"> <div class="member-name">
李地主 <span class="member-badge badge-level1">一级</span> </div> <div class="member-role">江苏区域 | 加入时间：2023-02-20</div> <div class="member-stats"> <div class="member-stat"> <i class="fas fa-user-friends member-stat-icon"></i> <span>28个村民</span> </div> <div class="member-stat"> <i class="fas fa-shopping-cart member-stat-icon"></i> <span>96笔订单</span> </div> <div class="member-stat"> <i class="fas fa-yen-sign member-stat-icon"></i> <span>18,450元</span> </div> </div> </div> <div class="status-badge status-active">活跃</div> <div class="member-actions"> <button class="btn btn-sm btn-outline-primary"><i class="fas fa-eye"></i></button> <button class="btn btn-sm btn-outline-success"><i class="fas fa-edit"></i></button> <button class="btn btn-sm btn-outline-secondary"><i class="fas fa-comment"></i></button> </div> </div> <div class="member-card"> <img src="images/avatar6.jpg" alt="分销商头像" class="member-avatar"> <div class="member-info"> <div class="member-name">
张村民 <span class="member-badge badge-level2">二级</span> </div> <div class="member-role">王地主团队 | 加入时间：2023-03-10</div> <div class="member-stats"> <div class="member-stat"> <i class="fas fa-shopping-cart member-stat-icon"></i> <span>32笔订单</span> </div> <div class="member-stat"> <i class="fas fa-yen-sign member-stat-icon"></i> <span>6,450元</span> </div> </div> </div> <div class="status-badge status-active">活跃</div> <div class="member-actions"> <button class="btn btn-sm btn-outline-primary"><i class="fas fa-eye"></i></button> <button class="btn btn-sm btn-outline-success"><i class="fas fa-edit"></i></button> <button class="btn btn-sm btn-outline-secondary"><i class="fas fa-comment"></i></button> </div> </div> <div class="member-card"> <img src="images/avatar7.jpg" alt="分销商头像" class="member-avatar"> <div class="member-info"> <div class="member-name">
赵村民 <span class="member-badge badge-level2">二级</span> </div> <div class="member-role">李地主团队 | 加入时间：2023-03-15</div> <div class="member-stats"> <div class="member-stat"> <i class="fas fa-shopping-cart member-stat-icon"></i> <span>28笔订单</span> </div> <div class="member-stat"> <i class="fas fa-yen-sign member-stat-icon"></i> <span>5,280元</span> </div> </div> </div> <div class="status-badge status-inactive">非活跃</div> <div class="member-actions"> <button class="btn btn-sm btn-outline-primary"><i class="fas fa-eye"></i></button> <button class="btn btn-sm btn-outline-success"><i class="fas fa-edit"></i></button> <button class="btn btn-sm btn-outline-secondary"><i class="fas fa-comment"></i></button> </div> </div> <div class="member-card"> <img src="images/avatar8.jpg" alt="分销商头像" class="member-avatar"> <div class="member-info"> <div class="member-name">
刘小明 <span class="member-badge badge-level2">二级</span> </div> <div class="member-role">申请时间：2023-06-18</div> <div class="member-stats"> <div class="member-stat"> <i class="fas fa-id-card member-stat-icon"></i> <span>已提交身份证</span> </div> <div class="member-stat"> <i class="fas fa-phone member-stat-icon"></i> <span>手机已验证</span> </div> </div> </div> <div class="status-badge status-pending">待审核</div> <div class="member-actions"> <button class="btn btn-sm btn-outline-primary"><i class="fas fa-eye"></i></button> <button class="btn btn-sm btn-success"><i class="fas fa-check"></i></button> <button class="btn btn-sm btn-danger"><i class="fas fa-times"></i></button> </div> </div> <div class="d-flex justify-content-between align-items-center mt-3"> <div>显示 1 至 5 条记录，共 256 条</div> <nav aria-label="Page navigation"><ul class="pagination pagination-sm"><li class="page-item disabled"><a class="page-link" href="#">上一页</a></li><li class="page-item active"><a class="page-link" href="#">1</a></li><li class="page-item"><a class="page-link" href="#">2</a></li><li class="page-item"><a class="page-link" href="#">3</a></li><li class="page-item"><a class="page-link" href="#">下一页</a></li></ul></nav> </div> </div> <!-- 分销商详情 --> <div class="distributor-card"> <h4 class="mb-4">分销商详情</h4> <div class="d-flex align-items-center mb-4"> <img src="images/avatar1.jpg" alt="分销商头像" class="member-avatar"> <div class="ms-3"> <h5>王地主 <span class="member-badge badge-level1">一级分销商</span></h5> <div class="text-secondary">浙江区域 | ID: DIS10086 | 加入时间：2023-01-15</div> </div> </div> <ul class="nav nav-tabs" id="distributorTabs" role="tablist"> <li class="nav-item" role="presentation"> <button class="nav-link active" id="basic-tab" data-bs-toggle="tab" data-bs-target="#basic" type="button" role="tab" aria-controls="basic" aria-selected="true">基本信息</button> </li> <li class="nav-item" role="presentation"> <button class="nav-link" id="team-tab" data-bs-toggle="tab" data-bs-target="#team" type="button" role="tab" aria-controls="team" aria-selected="false">团队管理</button> </li> <li class="nav-item" role="presentation"> <button class="nav-link" id="sales-tab" data-bs-toggle="tab" data-bs-target="#sales" type="button" role="tab" aria-controls="sales" aria-selected="false">销售数据</button> </li> <li class="nav-item" role="presentation"> <button class="nav-link" id="commission-tab" data-bs-toggle="tab" data-bs-target="#commission" type="button" role="tab" aria-controls="commission" aria-selected="false">佣金记录</button> </li> </ul> <div class="tab-content" id="distributorTabsContent"> <!-- 基本信息选项卡 --> <div class="tab-pane fade show active" id="basic" role="tabpanel" aria-labelledby="basic-tab"> <div class="tab-content pt-3"> <div class="row"> <div class="col-md-6"> <div class="detail-card"> <div class="detail-header"> <div class="detail-title"> <i class="fas fa-user detail-icon"></i> <span>个人信息</span> </div> </div> <div class="detail-body"> <div class="detail-item"> <div class="detail-label">真实姓名：</div> <div class="detail-value">王大力</div> </div> <div class="detail-item"> <div class="detail-label">手机号码：</div> <div class="detail-value">138****6688</div> </div> <div class="detail-item"> <div class="detail-label">身份证号：</div> <div class="detail-value">330***********1234</div> </div> <div class="detail-item"> <div class="detail-label">微信号：</div> <div class="detail-value">wangdali888</div> </div> <div class="detail-item"> <div class="detail-label">电子邮箱：</div> <div class="detail-value"><EMAIL></div> </div> </div> </div> </div> <div class="col-md-6"> <div class="detail-card"> <div class="detail-header"> <div class="detail-title"> <i class="fas fa-map-marker-alt detail-icon"></i> <span>区域信息</span> </div> </div> <div class="detail-body"> <div class="detail-item"> <div class="detail-label">负责区域：</div> <div class="detail-value">浙江省嘉兴市</div> </div> <div class="detail-item"> <div class="detail-label">详细地址：</div> <div class="detail-value">浙江省嘉兴市秦山区农业园区15号</div> </div> <div class="detail-item"> <div class="detail-label">覆盖面积：</div> <div class="detail-value">500些</div> </div> <div class="detail-item"> <div class="detail-label">主营产品：</div> <div class="detail-value">有机稻花香大米、有机蔬菜</div> </div> </div> </div> </div> </div> <div class="row mt-3"> <div class="col-md-6"> <div class="detail-card"> <div class="detail-header"> <div class="detail-title"> <i class="fas fa-money-check-alt detail-icon"></i> <span>结算信息</span> </div> </div> <div class="detail-body"> <div class="detail-item"> <div class="detail-label">结算方式：</div> <div class="detail-value">支付宝</div> </div> <div class="detail-item"> <div class="detail-label">支付宝账号：</div> <div class="detail-value">138****6688</div> </div> <div class="detail-item"> <div class="detail-label">结算周期：</div> <div class="detail-value">每月</div> </div> <div class="detail-item"> <div class="detail-label">结算日期：</div> <div class="detail-value">每月底</div> </div> </div> </div> </div> <div class="col-md-6"> <div class="detail-card"> <div class="detail-header"> <div class="detail-title"> <i class="fas fa-file-contract detail-icon"></i> <span>合同信息</span> </div> </div> <div class="detail-body"> <div class="detail-item"> <div class="detail-label">合同编号：</div> <div class="detail-value">DIS-2023-01-15-001</div> </div> <div class="detail-item"> <div class="detail-label">合同状态：</div> <div class="detail-value">有效</div> </div> <div class="detail-item"> <div class="detail-label">开始日期：</div> <div class="detail-value">2023-01-15</div> </div> <div class="detail-item"> <div class="detail-label">结束日期：</div> <div class="detail-value">2024-01-14</div> </div> <div class="detail-item"> <div class="detail-label">合同文件：</div> <div class="detail-value"> <a href="#">查看合同</a> </div> </div> </div> </div> </div> </div> </div> </div> <!-- 团队管理选项卡 --> <div class="tab-pane fade" id="team" role="tabpanel" aria-labelledby="team-tab"> <div class="tab-content pt-3"> <div class="detail-card"> <div class="detail-header"> <div class="detail-title"> <i class="fas fa-users detail-icon"></i> <span>团队成员 (32人)</span> </div> <button class="btn btn-sm btn-success"><i class="fas fa-user-plus me-1"></i>添加成员</button> </div> <div class="detail-body"> <div class="input-group mb-3" style="width: 300px;"> <input type="text" class="form-control form-control-sm" placeholder="搜索团队成员"> <button class="btn btn-sm btn-outline-secondary"><i class="fas fa-search"></i></button> </div> <div class="team-member"> <img src="images/avatar6.jpg" alt="团队成员头像" class="team-avatar"> <div class="team-info"> <div class="team-name">张村民</div> <div class="team-role">加入时间：2023-03-10 | 32笔订单</div> </div> <div class="team-value">6,450元</div> <div class="member-actions"> <button class="btn btn-sm btn-outline-primary"><i class="fas fa-eye"></i></button> <button class="btn btn-sm btn-outline-success"><i class="fas fa-edit"></i></button> </div> </div> <div class="team-member"> <img src="images/avatar9.jpg" alt="团队成员头像" class="team-avatar"> <div class="team-info"> <div class="team-name">王村民</div> <div class="team-role">加入时间：2023-03-12 | 28笔订单</div> </div> <div class="team-value">5,680元</div> <div class="member-actions"> <button class="btn btn-sm btn-outline-primary"><i class="fas fa-eye"></i></button> <button class="btn btn-sm btn-outline-success"><i class="fas fa-edit"></i></button> </div> </div> <div class="team-member"> <img src="images/avatar10.jpg" alt="团队成员头像" class="team-avatar"> <div class="team-info"> <div class="team-name">李村民</div> <div class="team-role">加入时间：2023-03-15 | 25笔订单</div> </div> <div class="team-value">4,850元</div> <div class="member-actions"> <button class="btn btn-sm btn-outline-primary"><i class="fas fa-eye"></i></button> <button class="btn btn-sm btn-outline-success"><i class="fas fa-edit"></i></button> </div> </div> <div class="d-flex justify-content-between align-items-center mt-3"> <div>显示 1 至 3 条记录，共 32 条</div> <nav aria-label="Page navigation"><ul class="pagination pagination-sm"><li class="page-item disabled"><a class="page-link" href="#">上一页</a></li><li class="page-item active"><a class="page-link" href="#">1</a></li><li class="page-item"><a class="page-link" href="#">2</a></li><li class="page-item"><a class="page-link" href="#">3</a></li><li class="page-item"><a class="page-link" href="#">下一页</a></li></ul></nav> </div> </div> </div> </div> </div> <!-- 销售数据选项卡 --> <div class="tab-pane fade" id="sales" role="tabpanel" aria-labelledby="sales-tab"> <div class="tab-content pt-3"> <div class="row"> <div class="col-md-12 mb-4"> <div class="detail-card"> <div class="detail-header"> <div class="detail-title"> <i class="fas fa-chart-line detail-icon"></i> <span>销售趋势</span> </div> <div> <button class="filter-btn">本周</button> <button class="filter-btn active">本月</button> <button class="filter-btn">本季</button> <button class="filter-btn">全年</button> </div> </div> <div class="detail-body"> <div class="chart-container"> <canvas id="salesChart"></canvas> </div> </div> </div> </div> </div> <div class="row"> <div class="col-md-6"> <div class="detail-card"> <div class="detail-header"> <div class="detail-title"> <i class="fas fa-shopping-cart detail-icon"></i> <span>销售产品分布</span> </div> </div> <div class="detail-body"> <div class="chart-container" style="height: 250px;"> <canvas id="productChart"></canvas> </div> </div> </div> </div> <div class="col-md-6"> <div class="detail-card"> <div class="detail-header"> <div class="detail-title"> <i class="fas fa-map-marker-alt detail-icon"></i> <span>销售区域分布</span> </div> </div> <div class="detail-body"> <div class="chart-container" style="height: 250px;"> <canvas id="regionChart"></canvas> </div> </div> </div> </div> </div> </div> </div> <!-- 佣金记录选项卡 --> <div class="tab-pane fade" id="commission" role="tabpanel" aria-labelledby="commission-tab"> <div class="tab-content pt-3"> <div class="detail-card"> <div class="detail-header"> <div class="detail-title"> <i class="fas fa-money-bill-wave detail-icon"></i> <span>佣金概况</span> </div> </div> <div class="detail-body"> <div class="row"> <div class="col-md-3 text-center"> <div class="stat-value">25,680元</div> <div class="stat-label">累计佣金</div> </div> <div class="col-md-3 text-center"> <div class="stat-value">3,580元</div> <div class="stat-label">本月佣金</div> </div> <div class="col-md-3 text-center"> <div class="stat-value">2,680元</div> <div class="stat-label">可提现佣金</div> </div> <div class="col-md-3 text-center"> <div class="stat-value">900元</div> <div class="stat-label">冻结佣金</div> </div> </div> </div> </div> <div class="detail-card mt-3"> <div class="detail-header"> <div class="detail-title"> <i class="fas fa-list-alt detail-icon"></i> <span>佣金记录</span> </div> <div class="input-group" style="width: 200px;"> <input type="month" class="form-control form-control-sm" value="2023-06"> <button class="btn btn-sm btn-outline-secondary"><i class="fas fa-search"></i></button> </div> </div> <div class="detail-body"> <div class="table-responsive"> <table class="table table-hover"> <thead> <tr> <th>订单编号</th> <th>产品名称</th> <th>订单金额</th> <th>佣金比例</th> <th>佣金金额</th> <th>类型</th> <th>时间</th> <th>状态</th> </tr> </thead> <tbody> <tr> <td>ORD-20230620-001</td> <td>有机稻花香大米</td> <td>1,280元</td> <td>15%</td> <td>192元</td> <td>直接销售</td> <td>2023-06-20</td> <td><span class="status-badge status-active">已结算</span></td> </tr> <tr> <td>ORD-20230619-002</td> <td>有机豆浆</td> <td>980元</td> <td>15%</td> <td>147元</td> <td>直接销售</td> <td>2023-06-19</td> <td><span class="status-badge status-active">已结算</span></td> </tr> <tr> <td>ORD-20230618-003</td> <td>有机黄瓜</td> <td>680元</td> <td>15%</td> <td>102元</td> <td>直接销售</td> <td>2023-06-18</td> <td><span class="status-badge status-active">已结算</span></td> </tr> <tr> <td>ORD-20230618-004</td> <td>有机稻花香大米</td> <td>1,280元</td> <td>3%</td> <td>38.4元</td> <td>团队销售</td> <td>2023-06-18</td> <td><span class="status-badge status-active">已结算</span></td> </tr> <tr> <td>ORD-20230621-005</td> <td>有机稻花香大米</td> <td>2,560元</td> <td>15%</td> <td>384元</td> <td>直接销售</td> <td>2023-06-21</td> <td><span class="status-badge status-pending">结算中</span></td> </tr> </tbody> </table> </div> <div class="d-flex justify-content-between align-items-center mt-3"> <div>显示 1 至 5 条记录，共 128 条</div> <nav aria-label="Page navigation"><ul class="pagination pagination-sm"><li class="page-item disabled"><a class="page-link" href="#">上一页</a></li><li class="page-item active"><a class="page-link" href="#">1</a></li><li class="page-item"><a class="page-link" href="#">2</a></li><li class="page-item"><a class="page-link" href="#">3</a></li><li class="page-item"><a class="page-link" href="#">下一页</a></li></ul></nav> </div> </div> </div> </div> </div> </div> </div> </div> <!-- 右侧内容 --> <div class="col-md-4"> <!-- 审核申请 --> <div class="distributor-card"> <div class="d-flex justify-content-between align-items-center mb-4"> <h4>审核申请</h4> <span class="badge bg-danger">12</span> </div> <div class="approval-card"> <div class="approval-header"> <div class="approval-title">刘小明 - 二级分销商申请</div> <div class="status-badge status-pending">待审核</div> </div> <div class="approval-body"> <div class="approval-item"> <div class="approval-label">申请时间：</div> <div class="approval-value">2023-06-18 15:30</div> </div> <div class="approval-item"> <div class="approval-label">手机号码：</div> <div class="approval-value">139****5678</div> </div> <div class="approval-item"> <div class="approval-label">上级分销商：</div> <div class="approval-value">王地主</div> </div> <div class="approval-item"> <div class="approval-label">身份证件：</div> <div class="approval-value">已提交</div> </div> </div> <div class="approval-actions"> <button class="btn btn-sm btn-outline-primary"><i class="fas fa-eye me-1"></i>查看详情</button> <button class="btn btn-sm btn-success"><i class="fas fa-check me-1"></i>通过</button> <button class="btn btn-sm btn-danger"><i class="fas fa-times me-1"></i>拒绝</button> </div> </div> <div class="approval-card"> <div class="approval-header"> <div class="approval-title">张三 - 一级分销商申请</div> <div class="status-badge status-pending">待审核</div> </div> <div class="approval-body"> <div class="approval-item"> <div class="approval-label">申请时间：</div> <div class="approval-value">2023-06-17 10:15</div> </div> <div class="approval-item"> <div class="approval-label">手机号码：</div> <div class="approval-value">135****1234</div> </div> <div class="approval-item"> <div class="approval-label">负责区域：</div> <div class="approval-value">安徽省芜湖市</div> </div> <div class="approval-item"> <div class="approval-label">身份证件：</div> <div class="approval-value">已提交</div> </div> </div> <div class="approval-actions"> <button class="btn btn-sm btn-outline-primary"><i class="fas fa-eye me-1"></i>查看详情</button> <button class="btn btn-sm btn-success"><i class="fas fa-check me-1"></i>通过</button> <button class="btn btn-sm btn-danger"><i class="fas fa-times me-1"></i>拒绝</button> </div> </div> <div class="text-center mt-3"> <button class="btn btn-outline-primary">查看更多申请</button> </div> </div> <!-- 常见问题 --> <div class="distributor-card"> <h4 class="mb-4">常见问题</h4> <div class="faq-item"> <div class="faq-question" onclick="toggleFaq(this)"> <span>如何审核分销商申请？</span> <i class="fas fa-chevron-down"></i> </div> <div class="faq-answer">
审核分销商申请时，需要检查申请人的身份信息、联系方式和资质证明等材料是否完整。对于一级分销商，还需要考虑其负责区域是否与现有分销商重叠。审核通过后，系统会自动发送通知并创建分销商账户。
</div> </div> <div class="faq-item"> <div class="faq-question" onclick="toggleFaq(this)"> <span>如何管理分销商团队？</span> <i class="fas fa-chevron-down"></i> </div> <div class="faq-answer">
在分销商详情页面的“团队管理”选项卡中，您可以查看该分销商的团队成员。您可以添加新成员、编辑成员信息或查看成员详情。对于一级分销商，您可以管理其下属的二级分销商团队。
</div> </div> <div class="faq-item"> <div class="faq-question" onclick="toggleFaq(this)"> <span>如何查看分销商的销售数据？</span> <i class="fas fa-chevron-down"></i> </div> <div class="faq-answer">
在分销商详情页面的“销售数据”选项卡中，您可以查看该分销商的销售趋势、产品分布和区域分布等数据。您可以按周、月、季度或年度查看数据，并可以导出详细数据进行分析。
</div> </div> <div class="faq-item"> <div class="faq-question" onclick="toggleFaq(this)"> <span>如何查看分销商的佣金记录？</span> <i class="fas fa-chevron-down"></i> </div> <div class="faq-answer">
在分销商详情页面的“佣金记录”选项卡中，您可以查看该分销商的佣金概况和详细记录。您可以按月份筛选佣金记录，并可以查看每笔佣金的订单信息、金额和状态等。
</div> </div> </div> </div> </div> </div> <!-- 页脚 --> <!-- 页脚 --> <footer class="bg-dark text-white py-4"> <div class="container"> <div class="row"> <div class="col-md-6"> <h5>智慧农业数字化平台</h5> <p>连接农业产业链，打造数字化农业生态</p> </div> <div class="col-md-6 text-md-end"> <p>联系我们：<EMAIL></p> <p>服务热线：************</p> </div> </div> </div> </footer> <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script> <script src="js/common.js"></script> <script src="https://cdn.jsdelivr.net/npm/chart.js"></script> <script>
// 使用 common.js 加载导航栏

// 切换FAQ显示/隐藏
function toggleFaq(element) {
const answer = element.nextElementSibling;
const icon = element.querySelector('i');
if (answer.classList.contains('active')) {
answer.classList.remove('active');
icon.classList.remove('fa-chevron-up');
icon.classList.add('fa-chevron-down');
} else {
answer.classList.add('active');
icon.classList.remove('fa-chevron-down');
icon.classList.add('fa-chevron-up');
}
}
// 初始化销售趋势图表
const salesChartCtx = document.getElementById('salesChart');
if (salesChartCtx) {
const salesChart = new Chart(salesChartCtx, {
type: 'line',
data: {
labels: ['1日', '5日', '10日', '15日', '20日', '25日', '30日'],
datasets: [
{
label: '销售金额(元)',
data: [3500, 4200, 5100, 4800, 5600, 6200, 5800],
borderColor: 'rgba(52, 152, 219, 1)',
backgroundColor: 'rgba(52, 152, 219, 0.1)',
tension: 0.4,
fill: true
},
{
label: '佣金金额(元)',
data: [525, 630, 765, 720, 840, 930, 870],
borderColor: 'rgba(142, 68, 173, 1)',
backgroundColor: 'rgba(142, 68, 173, 0.1)',
tension: 0.4,
fill: true
}
]
},
options: {
responsive: true,
maintainAspectRatio: false,
plugins: {
legend: {
position: 'top',
},
tooltip: {
mode: 'index',
intersect: false
}
},
scales: {
y: {
beginAtZero: true
}
}
}
});
}
// 初始化产品分布图表
const productChartCtx = document.getElementById('productChart');
if (productChartCtx) {
const productChart = new Chart(productChartCtx, {
type: 'pie',
data: {
labels: ['有机稻花香大米', '有机豆浆', '有机黄瓜', '有机番茄', '其他'],
datasets: [
{
data: [45, 20, 15, 10, 10],
backgroundColor: [
'rgba(52, 152, 219, 0.8)',
'rgba(46, 204, 113, 0.8)',
'rgba(241, 196, 15, 0.8)',
'rgba(231, 76, 60, 0.8)',
'rgba(155, 89, 182, 0.8)'
],
borderColor: [
'rgba(52, 152, 219, 1)',
'rgba(46, 204, 113, 1)',
'rgba(241, 196, 15, 1)',
'rgba(231, 76, 60, 1)',
'rgba(155, 89, 182, 1)'
],
borderWidth: 1
}
]
},
options: {
responsive: true,
maintainAspectRatio: false,
plugins: {
legend: {
position: 'bottom'
},
tooltip: {
callbacks: {
label: function(context) {
return context.label + ': ' + context.raw + '%';
}
}
}
}
}
});
}
// 初始化区域分布图表
const regionChartCtx = document.getElementById('regionChart');
if (regionChartCtx) {
const regionChart = new Chart(regionChartCtx, {
type: 'bar',
data: {
labels: ['嘉兴市', '湖州市', '绍兴市', '金华市', '其他'],
datasets: [
{
label: '销售金额(元)',
data: [12500, 8600, 7200, 6800, 5200],
backgroundColor: 'rgba(142, 68, 173, 0.8)',
borderColor: 'rgba(142, 68, 173, 1)',
borderWidth: 1
}
]
},
options: {
responsive: true,
maintainAspectRatio: false,
plugins: {
legend: {
position: 'bottom'
}
},
scales: {
y: {
beginAtZero: true
}
}
}
});
}
// 筛选按钮点击事件
document.querySelectorAll('.filter-btn').forEach(btn => {
btn.addEventListener('click', function() {
document.querySelectorAll('.filter-btn').forEach(b => {
b.classList.remove('active');
});
this.classList.add('active');
});
});
// 分销商列表操作按钮点击事件
document.querySelectorAll('.member-actions .btn').forEach(btn => {
btn.addEventListener('click', function(e) {
e.stopPropagation();
const memberCard = this.closest('.member-card');
const memberName = memberCard.querySelector('.member-name').textContent.trim();
if (this.querySelector('.fa-eye')) {
// 查看详情
document.querySelector('.distributor-card:nth-child(2)').scrollIntoView({ behavior: 'smooth' });
} else if (this.querySelector('.fa-edit')) {
// 编辑
alert(`编辑 ${memberName} 的信息`);
} else if (this.querySelector('.fa-comment')) {
// 发送消息
alert(`给 ${memberName} 发送消息`);
} else if (this.querySelector('.fa-check')) {
// 通过申请
alert(`已通过 ${memberName} 的申请`);
memberCard.remove();
} else if (this.querySelector('.fa-times')) {
// 拒绝申请
alert(`已拒绝 ${memberName} 的申请`);
memberCard.remove();
}
});
});
// 审核申请按钮点击事件
document.querySelectorAll('.approval-actions .btn').forEach(btn => {
btn.addEventListener('click', function() {
const approvalCard = this.closest('.approval-card');
const approvalTitle = approvalCard.querySelector('.approval-title').textContent;
if (this.querySelector('.fa-eye')) {
// 查看详情
alert(`查看 ${approvalTitle} 的详细信息`);
} else if (this.querySelector('.fa-check')) {
// 通过申请
alert(`已通过 ${approvalTitle}`);
approvalCard.remove();
} else if (this.querySelector('.fa-times')) {
// 拒绝申请
alert(`已拒绝 ${approvalTitle}`);
approvalCard.remove();
}
});
});
</script> </body> </html>