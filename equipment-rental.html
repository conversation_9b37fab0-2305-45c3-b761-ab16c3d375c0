<!DOCTYPE html> <html lang="zh-CN"> <head> <meta charset="UTF-8"> <meta name="viewport" content="width=device-width, initial-scale=1.0"> <title>智能农机设备租赁系统 - 智慧农业平台</title> <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet"> <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet"> <link rel="stylesheet" href="css/common.css"> <link rel="stylesheet" href="css/navbar.css"> <style>
:root {
--primary-color: #28a745;
--secondary-color: #6c757d;
--light-bg: #f8f9fa;
--card-shadow: 0 4px 6px rgba(0,0,0,0.1);
--transition: all 0.3s ease;
--equipment-color: #2980b9;
}
body {
background-color: var(--light-bg);
font-family: 'PingFang SC', '<PERSON> YaHei', sans-serif;
}
.equipment-header {
background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('https://images.unsplash.com/photo-1592982537447-7440770cbfc9?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80');
background-size: cover;
background-position: center;
height: 300px;
color: white;
display: flex;
align-items: center;
margin-bottom: 30px;
border-radius: 10px;
}
.equipment-card {
background: white;
padding: 20px;
border-radius: 10px;
box-shadow: var(--card-shadow);
margin-bottom: 30px;
min-height: auto;
}
.stat-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 20px;
transition: var(--transition);
}
.stat-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.stat-icon {
font-size: 2rem;
color: var(--equipment-color);
margin-bottom: 10px;
}
.stat-value {
font-size: 1.5rem;
font-weight: bold;
color: var(--equipment-color);
margin-bottom: 5px;
}
.stat-label {
font-size: 0.9rem;
color: var(--secondary-color);
}
.filter-btn {
padding: 5px 15px;
border-radius: 20px;
background-color: white;
border: 1px solid #ddd;
margin-right: 10px;
margin-bottom: 10px;
cursor: pointer;
transition: var(--transition);
}
.filter-btn.active {
background-color: var(--primary-color);
color: white;
border-color: var(--primary-color);
}
.filter-btn:hover:not(.active) {
background-color: #f0f0f0;
}
.equipment-item {
background: var(--light-bg);
border-radius: 10px;
overflow: hidden;
margin-bottom: 20px;
transition: var(--transition);
min-height: auto;
}
.equipment-item:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.equipment-img {
width: 100%;
height: 200px;
object-fit: cover;
}
.equipment-info {
padding: 15px;
}
.equipment-title {
font-weight: bold;
font-size: 1.1rem;
margin-bottom: 5px;
display: flex;
justify-content: space-between;
align-items: center;
}
.equipment-badge {
display: inline-block;
padding: 2px 8px;
border-radius: 15px;
font-size: 0.7rem;
background-color: rgba(41, 128, 185, 0.1);
color: var(--equipment-color);
}
.equipment-desc {
color: var(--secondary-color);
margin-bottom: 10px;
font-size: 0.9rem;
height: 40px;
overflow: hidden;
}
.equipment-meta {
display: flex;
justify-content: space-between;
margin-bottom: 10px;
font-size: 0.9rem;
}
.equipment-price {
font-weight: bold;
color: var(--equipment-color);
}
.equipment-location {
color: var(--secondary-color);
display: flex;
align-items: center;
}
.equipment-location i {
margin-right: 5px;
}
.equipment-features {
display: flex;
flex-wrap: wrap;
gap: 5px;
margin-bottom: 10px;
}
.equipment-feature {
display: inline-block;
padding: 2px 8px;
border-radius: 15px;
font-size: 0.7rem;
background-color: #f0f0f0;
color: var(--secondary-color);
}
.equipment-actions {
display: flex;
gap: 10px;
}
.rental-form {
background: var(--light-bg);
padding: 20px;
border-radius: 10px;
margin-bottom: 20px;
}
.rental-steps {
display: flex;
justify-content: space-between;
margin-bottom: 30px;
position: relative;
}
.rental-step {
display: flex;
flex-direction: column;
align-items: center;
width: 100px;
z-index: 1;
}
.step-number {
width: 40px;
height: 40px;
border-radius: 50%;
background-color: var(--equipment-color);
color: white;
display: flex;
align-items: center;
justify-content: center;
font-weight: bold;
margin-bottom: 10px;
}
.step-text {
text-align: center;
font-size: 0.9rem;
}
.step-line {
position: absolute;
top: 20px;
left: 50px;
right: 50px;
height: 2px;
background-color: var(--equipment-color);
z-index: 0;
}
.review-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 15px;
transition: var(--transition);
}
.review-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.review-header {
display: flex;
align-items: center;
margin-bottom: 10px;
}
.review-avatar {
width: 40px;
height: 40px;
border-radius: 50%;
object-fit: cover;
margin-right: 10px;
}
.review-user {
flex: 1;
}
.review-name {
font-weight: bold;
margin-bottom: 0;
}
.review-date {
font-size: 0.8rem;
color: var(--secondary-color);
}
.review-rating {
color: #f1c40f;
}
.review-content {
margin-bottom: 10px;
}
.review-equipment {
font-size: 0.8rem;
color: var(--secondary-color);
font-style: italic;
}
.faq-item {
margin-bottom: 15px;
}
.faq-question {
font-weight: bold;
margin-bottom: 5px;
cursor: pointer;
display: flex;
justify-content: space-between;
align-items: center;
}
.faq-answer {
color: var(--secondary-color);
display: none;
padding: 10px 0;
}
.faq-answer.active {
display: block;
}
.map-container {
height: 300px;
width: 100%;
border-radius: 10px;
overflow: hidden;
margin-bottom: 20px;
}
.calendar-container {
background: white;
border-radius: 10px;
padding: 15px;
margin-bottom: 20px;
}
.calendar-header {
display: flex;
justify-content: space-between;
align-items: center;
margin-bottom: 15px;
}
.calendar-title {
font-weight: bold;
}
.calendar-nav {
display: flex;
gap: 10px;
}
.calendar-grid {
display: grid;
grid-template-columns: repeat(7, 1fr);
gap: 5px;
}
.calendar-day {
text-align: center;
padding: 10px;
border-radius: 5px;
cursor: pointer;
}
.calendar-day.header {
font-weight: bold;
background-color: var(--light-bg);
}
.calendar-day.available {
background-color: #e8f5e9;
}
.calendar-day.unavailable {
background-color: #ffebee;
color: #b71c1c;
text-decoration: line-through;
}
.calendar-day.selected {
background-color: var(--equipment-color);
color: white;
}
.calendar-day:not(.header):hover {
background-color: var(--equipment-color);
color: white;
}
.chart-container {
position: relative;
height: 300px;
width: 100%;
margin-bottom: 20px;
}
.nav-tabs .nav-link {
color: var(--secondary-color);
}
.nav-tabs .nav-link.active {
color: var(--equipment-color);
font-weight: bold;
}
.tab-content {
padding: 20px 0;
}
</style> </head> <body> <!-- 导航栏 -->
<div id="navbar-container"></div>
<!-- 主要内容 --> <div class="container py-5"> <div class="equipment-header"> <div class="container"> <h1>智能农机设备租赁系统</h1> <p class="lead">便捷高效的农机设备租赁平台，提供多种智能农机设备，满足不同种植需求，降低农业生产成本</p> <div class="mt-4"> <button class="btn btn-success btn-lg me-2"><i class="fas fa-tractor me-2"></i>立即租赁</button> <button class="btn btn-outline-light btn-lg"><i class="fas fa-info-circle me-2"></i>了解更多</button> </div> </div> </div> <!-- 顶部统计卡片 --> <div class="row mb-4"> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-tractor stat-icon"></i> <div class="stat-value">256</div> <div class="stat-label">可租赁设备数</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-map-marker-alt stat-icon"></i> <div class="stat-value">32</div> <div class="stat-label">服务覆盖城市</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-users stat-icon"></i> <div class="stat-value">1,280</div> <div class="stat-label">累计服务用户</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-star stat-icon"></i> <div class="stat-value">4.8</div> <div class="stat-label">平均满意度</div> </div> </div> </div> <!-- 主要内容区域 --> <div class="row"> <!-- 左侧内容 --> <div class="col-md-8"> <!-- 设备筛选区 --> <div class="equipment-card mb-4"> <div class="d-flex justify-content-between align-items-center mb-4"> <h4>智能农机设备</h4> <div class="input-group" style="width: 250px;"> <input type="text" class="form-control" placeholder="搜索设备名称"> <button class="btn btn-outline-secondary"><i class="fas fa-search"></i></button> </div> </div> <div class="mb-3"> <button class="filter-btn active">全部设备</button> <button class="filter-btn">耕作机械</button> <button class="filter-btn">播种机械</button> <button class="filter-btn">施肥机械</button> <button class="filter-btn">收获机械</button> <button class="filter-btn">智能监测</button> <button class="filter-btn">植保机械</button> </div> <div class="row"> <div class="col-md-6 mb-4"> <div class="equipment-item"> <img src="images/equipment1.jpg" alt="智能无人驿驴机" class="equipment-img"> <div class="equipment-info"> <div class="equipment-title"> <span>智能无人驿驴机</span> <span class="equipment-badge">热门</span> </div> <div class="equipment-desc">智能无人驿驴机，支持GPS定位和自动导航，适用于各类田间作业</div> <div class="equipment-meta"> <div class="equipment-price">300元/天</div> <div class="equipment-location"><i class="fas fa-map-marker-alt"></i> 浙江省嘉兴市</div> </div> <div class="equipment-features"> <span class="equipment-feature"><i class="fas fa-wifi"></i> 远程控制</span> <span class="equipment-feature"><i class="fas fa-battery-full"></i> 电池续舨12小时</span> <span class="equipment-feature"><i class="fas fa-map"></i> GPS定位</span> </div> <div class="equipment-actions"> <button class="btn btn-primary w-100"><i class="fas fa-calendar-alt me-2"></i>立即预约</button> </div> </div> </div> </div> <div class="col-md-6 mb-4"> <div class="equipment-item"> <img src="images/equipment2.jpg" alt="智能播种机" class="equipment-img"> <div class="equipment-info"> <div class="equipment-title"> <span>智能播种机</span> <span class="equipment-badge">新品</span> </div> <div class="equipment-desc">高精度智能播种机，支持变量播种和精准定位，提高播种效率</div> <div class="equipment-meta"> <div class="equipment-price">250元/天</div> <div class="equipment-location"><i class="fas fa-map-marker-alt"></i> 江苏省苏州市</div> </div> <div class="equipment-features"> <span class="equipment-feature"><i class="fas fa-seedling"></i> 变量播种</span> <span class="equipment-feature"><i class="fas fa-tachometer-alt"></i> 高效率</span> <span class="equipment-feature"><i class="fas fa-cog"></i> 智能控制</span> </div> <div class="equipment-actions"> <button class="btn btn-primary w-100"><i class="fas fa-calendar-alt me-2"></i>立即预约</button> </div> </div> </div> </div> <div class="col-md-6 mb-4"> <div class="equipment-item"> <img src="images/equipment3.jpg" alt="智能施肥机" class="equipment-img"> <div class="equipment-info"> <div class="equipment-title"> <span>智能施肥机</span> <span class="equipment-badge">推荐</span> </div> <div class="equipment-desc">智能变量施肥机，根据土壤数据自动调整施肥量，提高肥料利用率</div> <div class="equipment-meta"> <div class="equipment-price">280元/天</div> <div class="equipment-location"><i class="fas fa-map-marker-alt"></i> 安徽省合肥市</div> </div> <div class="equipment-features"> <span class="equipment-feature"><i class="fas fa-chart-line"></i> 变量施肥</span> <span class="equipment-feature"><i class="fas fa-database"></i> 数据分析</span> <span class="equipment-feature"><i class="fas fa-leaf"></i> 环保节能</span> </div> <div class="equipment-actions"> <button class="btn btn-primary w-100"><i class="fas fa-calendar-alt me-2"></i>立即预约</button> </div> </div> </div> </div> <div class="col-md-6 mb-4"> <div class="equipment-item"> <img src="images/equipment4.jpg" alt="智能收获机" class="equipment-img"> <div class="equipment-info"> <div class="equipment-title"> <span>智能收获机</span> <span class="equipment-badge">热门</span> </div> <div class="equipment-desc">智能收获机，支持自动导航和作物识别，提高收获效率和净度</div> <div class="equipment-meta"> <div class="equipment-price">350元/天</div> <div class="equipment-location"><i class="fas fa-map-marker-alt"></i> 浙江省杭州市</div> </div> <div class="equipment-features"> <span class="equipment-feature"><i class="fas fa-robot"></i> 自动导航</span> <span class="equipment-feature"><i class="fas fa-eye"></i> 作物识别</span> <span class="equipment-feature"><i class="fas fa-tachometer-alt"></i> 高效率</span> </div> <div class="equipment-actions"> <button class="btn btn-primary w-100"><i class="fas fa-calendar-alt me-2"></i>立即预约</button> </div> </div> </div> </div> </div> <div class="text-center mt-3"> <button class="btn btn-outline-primary">加载更多设备</button> </div> </div> <!-- 租赁流程 --> <div class="equipment-card mb-4"> <h4 class="mb-4">租赁流程</h4> <div class="rental-form"> <div class="rental-steps"> <div class="step-line"></div> <div class="rental-step"> <div class="step-number">1</div> <div class="step-text">选择设备</div> </div> <div class="rental-step"> <div class="step-number">2</div> <div class="step-text">预约时间</div> </div> <div class="rental-step"> <div class="step-number">3</div> <div class="step-text">在线支付</div> </div> <div class="rental-step"> <div class="step-number">4</div> <div class="step-text">设备配送</div> </div> <div class="rental-step"> <div class="step-number">5</div> <div class="step-text">使用归还</div> </div> </div> <div class="row"> <div class="col-md-6 mb-3"> <div class="card h-100"> <div class="card-body"> <h5 class="card-title"><i class="fas fa-shield-alt text-success me-2"></i>设备保障</h5> <p class="card-text">所有设备均经过专业检测和维护，确保在最佳工作状态。租赁期间提供24小时技术支持和故障处理服务。</p> </div> </div> </div> <div class="col-md-6 mb-3"> <div class="card h-100"> <div class="card-body"> <h5 class="card-title"><i class="fas fa-truck text-primary me-2"></i>配送服务</h5> <p class="card-text">提供专业的设备配送和安装服务，确保设备安全高效地送达使用地点。大型设备提供现场操作培训。</p> </div> </div> </div> <div class="col-md-6 mb-3"> <div class="card h-100"> <div class="card-body"> <h5 class="card-title"><i class="fas fa-credit-card text-warning me-2"></i>灵活付款</h5> <p class="card-text">支持多种付款方式，包括在线支付、信用卡和农业补贴抵扣。长期租赁可享受分期付款和优惠折扣。</p> </div> </div> </div> <div class="col-md-6 mb-3"> <div class="card h-100"> <div class="card-body"> <h5 class="card-title"><i class="fas fa-headset text-danger me-2"></i>售后服务</h5> <p class="card-text">提供全方位的售后服务，包括设备使用指导、故障处理和维修服务。租赁结束后提供上门回收服务。</p> </div> </div> </div> </div> </div> </div> <!-- 用户评价 --> <div class="equipment-card mb-4"> <div class="d-flex justify-content-between align-items-center mb-4"> <h4>用户评价</h4> <div> <span class="me-2">总评分：</span> <span class="review-rating"> <i class="fas fa-star"></i> <i class="fas fa-star"></i> <i class="fas fa-star"></i> <i class="fas fa-star"></i> <i class="fas fa-star-half-alt"></i> <span class="ms-1">4.8</span> </span> </div> </div> <div class="review-card"> <div class="review-header"> <img src="images/user1.jpg" alt="用户头像" class="review-avatar"> <div class="review-user"> <h6 class="review-name">王农民</h6> <div class="review-date">2023-06-15</div> </div> <div class="review-rating"> <i class="fas fa-star"></i> <i class="fas fa-star"></i> <i class="fas fa-star"></i> <i class="fas fa-star"></i> <i class="fas fa-star"></i> </div> </div> <div class="review-content">
智能无人驿驴机非常好用，操作简单，精准度高，大大提高了我的耕作效率。配送服务也很周到，工作人员还详细讲解了操作方法。非常满意，下次还会继续租赁。
</div> <div class="review-equipment">租赁设备：智能无人驿驴机</div> </div> <div class="review-card"> <div class="review-header"> <img src="images/user2.jpg" alt="用户头像" class="review-avatar"> <div class="review-user"> <h6 class="review-name">李农民</h6> <div class="review-date">2023-06-10</div> </div> <div class="review-rating"> <i class="fas fa-star"></i> <i class="fas fa-star"></i> <i class="fas fa-star"></i> <i class="fas fa-star"></i> <i class="fas fa-star-half-alt"></i> </div> </div> <div class="review-content">
智能播种机的变量播种功能很实用，可以根据不同区域的土壤条件自动调整播种量，节省了不少种子。售后服务也很周到，有问题可以随时咨询。唯一缺点是电池续航时间稍短。
</div> <div class="review-equipment">租赁设备：智能播种机</div> </div> <div class="review-card"> <div class="review-header"> <img src="images/user3.jpg" alt="用户头像" class="review-avatar"> <div class="review-user"> <h6 class="review-name">张农民</h6> <div class="review-date">2023-06-05</div> </div> <div class="review-rating"> <i class="fas fa-star"></i> <i class="fas fa-star"></i> <i class="fas fa-star"></i> <i class="fas fa-star"></i> <i class="fas fa-star"></i> </div> </div> <div class="review-content">
智能施肥机很好用，可以根据土壤数据自动调整施肥量，施肥均匀，节省了肥料成本。租赁流程简单方便，在线预约、支付后很快就收到了设备。工作人员服务态度也很好。强烈推荐！
</div> <div class="review-equipment">租赁设备：智能施肥机</div> </div> <div class="text-center mt-3"> <button class="btn btn-outline-primary">查看更多评价</button> </div> </div> </div> <!-- 右侧内容 --> <div class="col-md-4"> <!-- 预约日历 --> <div class="equipment-card"> <h4 class="mb-4">设备预约日历</h4> <div class="calendar-container"> <div class="calendar-header"> <div class="calendar-title">2023年6月</div> <div class="calendar-nav"> <button class="btn btn-sm btn-outline-secondary"><i class="fas fa-chevron-left"></i></button> <button class="btn btn-sm btn-outline-secondary"><i class="fas fa-chevron-right"></i></button> </div> </div> <div class="calendar-grid"> <div class="calendar-day header">日</div> <div class="calendar-day header">一</div> <div class="calendar-day header">二</div> <div class="calendar-day header">三</div> <div class="calendar-day header">四</div> <div class="calendar-day header">五</div> <div class="calendar-day header">六</div> <div class="calendar-day"></div> <div class="calendar-day"></div> <div class="calendar-day"></div> <div class="calendar-day"></div> <div class="calendar-day available">1</div> <div class="calendar-day available">2</div> <div class="calendar-day available">3</div> <div class="calendar-day available">4</div> <div class="calendar-day available">5</div> <div class="calendar-day available">6</div> <div class="calendar-day available">7</div> <div class="calendar-day available">8</div> <div class="calendar-day unavailable">9</div> <div class="calendar-day unavailable">10</div> <div class="calendar-day unavailable">11</div> <div class="calendar-day unavailable">12</div> <div class="calendar-day available">13</div> <div class="calendar-day available">14</div> <div class="calendar-day available">15</div> <div class="calendar-day available">16</div> <div class="calendar-day available">17</div> <div class="calendar-day available">18</div> <div class="calendar-day available">19</div> <div class="calendar-day available">20</div> <div class="calendar-day selected">21</div> <div class="calendar-day selected">22</div> <div class="calendar-day selected">23</div> <div class="calendar-day available">24</div> <div class="calendar-day available">25</div> <div class="calendar-day available">26</div> <div class="calendar-day available">27</div> <div class="calendar-day available">28</div> <div class="calendar-day available">29</div> <div class="calendar-day available">30</div> <div class="calendar-day"></div> </div> </div> <div class="d-flex justify-content-between align-items-center mb-3"> <div> <span class="me-2">已选择：</span> <span class="text-primary">6月21日 - 6月23日</span> </div> <div> <span class="text-primary">3天</span> </div> </div> <div class="d-grid gap-2"> <button class="btn btn-success"><i class="fas fa-calendar-check me-2"></i>确认预约</button> </div> </div> <!-- 设备分布地图 --> <div class="equipment-card"> <h4 class="mb-4">设备分布地图</h4> <div class="map-container"> <img src="images/equipment-map.jpg" alt="设备分布地图" style="width: 100%; height: 100%; object-fit: cover;"> </div> <div class="mt-3"> <div class="input-group"> <input type="text" class="form-control" placeholder="输入您的位置"> <button class="btn btn-outline-secondary"><i class="fas fa-search"></i></button> </div> </div> </div> <!-- 常见问题 --> <div class="equipment-card"> <h4 class="mb-4">常见问题</h4> <div class="faq-item"> <div class="faq-question" onclick="toggleFaq(this)"> <span>如何租赁农机设备？</span> <i class="fas fa-chevron-down"></i> </div> <div class="faq-answer">
租赁农机设备的步骤很简单：1) 在平台上选择需要的设备；2) 选择租赁时间和数量；3) 在线支付租金；4) 等待设备配送到指定地点；5) 使用完毕后联系平台安排回收。整个过程简单高效，并提供全程技术支持。
</div> </div> <div class="faq-item"> <div class="faq-question" onclick="toggleFaq(this)"> <span>租赁设备需要交押金吗？</span> <i class="fas fa-chevron-down"></i> </div> <div class="faq-answer">
是的，租赁设备需要交纳一定金额的押金，押金金额根据设备类型和价值不同而有所差异，一般为设备价值的10%-20%。设备归还并检查无损后，押金将全额退还。如果设备有损坏，将从押金中扣除相应的维修费用。
</div> </div> <div class="faq-item"> <div class="faq-question" onclick="toggleFaq(this)"> <span>设备使用过程中出现故障怎么办？</span> <i class="fas fa-chevron-down"></i> </div> <div class="faq-answer">
如果设备在使用过程中出现故障，请立即联系我们的售后服务热线（************）。我们提供24小时技术支持服务，将在最短时间内安排技术人员上门维修或更换设备。如果是由于设备本身问题导致的故障，维修费用由我们承担。
</div> </div> <div class="faq-item"> <div class="faq-question" onclick="toggleFaq(this)"> <span>是否提供设备操作培训？</span> <i class="fas fa-chevron-down"></i> </div> <div class="faq-answer">
是的，我们为所有租赁的设备提供基本的操作指导。对于复杂的大型设备，我们会在配送时安排专业技术人员进行现场操作培训。此外，我们还提供设备操作手册和视频教程，方便用户随时查阅和学习。
</div> </div> <div class="faq-item"> <div class="faq-question" onclick="toggleFaq(this)"> <span>租赁期限可以延长吗？</span> <i class="fas fa-chevron-down"></i> </div> <div class="faq-answer">
可以的，如果您需要延长租赁期限，请提前至少一天联系我们的客服人员。在设备没有被其他用户预约的情况下，我们将为您办理延期手续。长期租赁客户还可以享受特别的折扣优惠。
</div> </div> </div> <!-- 联系我们 --> <div class="equipment-card"> <h4 class="mb-4">联系我们</h4> <div class="mb-3"> <div class="d-flex align-items-center mb-2"> <i class="fas fa-phone-alt text-primary me-2"></i> <span>客服热线：************</span> </div> <div class="d-flex align-items-center mb-2"> <i class="fas fa-envelope text-primary me-2"></i> <span>邮箱：<EMAIL></span> </div> <div class="d-flex align-items-center mb-2"> <i class="fas fa-clock text-primary me-2"></i> <span>服务时间：周一至周日 8:00-20:00</span> </div> </div> <div class="d-grid gap-2"> <button class="btn btn-outline-primary"><i class="fas fa-comments me-2"></i>在线咨询</button> </div> </div> </div> </div> </div> <!-- 页脚 -->
<div id="footer-container"></div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="js/login-config.js"></script>
<script src="js/components.js"></script>
<script>
// 使用 common.js 加载导航栏

// 切换FAQ显示/隐藏
function toggleFaq(element) {
const answer = element.nextElementSibling;
const icon = element.querySelector('i');
if (answer.classList.contains('active')) {
answer.classList.remove('active');
icon.classList.remove('fa-chevron-up');
icon.classList.add('fa-chevron-down');
} else {
answer.classList.add('active');
icon.classList.remove('fa-chevron-down');
icon.classList.add('fa-chevron-up');
}
}
// 筛选按钮点击事件
document.querySelectorAll('.filter-btn').forEach(btn => {
btn.addEventListener('click', function() {
document.querySelectorAll('.filter-btn').forEach(b => {
b.classList.remove('active');
});
this.classList.add('active');
});
});
// 日历日期点击事件
document.querySelectorAll('.calendar-day:not(.header):not(:empty)').forEach(day => {
day.addEventListener('click', function() {
if (!this.classList.contains('unavailable')) {
this.classList.toggle('selected');
}
});
});
// 预约按钮点击事件
document.querySelectorAll('.equipment-actions .btn').forEach(btn => {
btn.addEventListener('click', function() {
const equipmentName = this.closest('.equipment-item').querySelector('.equipment-title span').textContent;
alert(`正在预约 ${equipmentName}，请选择日期和时间。`);
document.querySelector('.equipment-card:nth-child(1)').scrollIntoView({ behavior: 'smooth' });
});
});
// 确认预约按钮点击事件
document.querySelector('.btn-success').addEventListener('click', function() {
alert('预约成功！请继续完成支付流程。');
});
// 加载更多设备按钮点击事件
document.querySelector('.btn-outline-primary').addEventListener('click', function() {
alert('正在加载更多设备，请稍后...');
});
// 在线咨询按钮点击事件
document.querySelector('.equipment-card:last-child .btn').addEventListener('click', function() {
alert('正在连接客服，请稍后...');
});
</script> </body> </html>