<!DOCTYPE html> <html lang="zh-CN"> <head> <meta charset="UTF-8"> <meta name="viewport" content="width=device-width, initial-scale=1.0"> <title>专家在线问诊 - 智慧农业平台</title> <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet"> <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet"> <link rel="stylesheet" href="css/common.css"> <link rel="stylesheet" href="css/navbar.css"> <style>
:root {
--primary-color: #28a745;
--secondary-color: #6c757d;
--light-bg: #f8f9fa;
--card-shadow: 0 4px 6px rgba(0,0,0,0.1);
--transition: all 0.3s ease;
--expert-color: #3498db;
}
body {
background-color: var(--light-bg);
font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}
.expert-header {
background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80');
background-size: cover;
background-position: center;
height: 200px;
color: white;
display: flex;
align-items: center;
margin-bottom: 30px;
border-radius: 10px;
}
.expert-card {
background: white;
padding: 20px;
border-radius: 10px;
box-shadow: var(--card-shadow);
margin-bottom: 30px;
min-height: auto;
}
.stat-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 20px;
transition: var(--transition);
}
.stat-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.stat-icon {
font-size: 2rem;
color: var(--expert-color);
margin-bottom: 10px;
}
.stat-value {
font-size: 1.5rem;
font-weight: bold;
color: var(--expert-color);
margin-bottom: 5px;
}
.stat-label {
font-size: 0.9rem;
color: var(--secondary-color);
}
.upload-container {
border: 2px dashed #ddd;
border-radius: 10px;
padding: 30px;
text-align: center;
margin-bottom: 20px;
cursor: pointer;
transition: var(--transition);
}
.upload-container:hover {
border-color: var(--primary-color);
}
.upload-icon {
font-size: 3rem;
color: var(--secondary-color);
margin-bottom: 15px;
}
.upload-text {
margin-bottom: 15px;
}
.upload-preview {
max-width: 100%;
max-height: 300px;
margin: 0 auto 15px;
display: none;
border-radius: 10px;
}
.expert-list {
display: flex;
flex-wrap: wrap;
gap: 15px;
margin-bottom: 20px;
}
.expert-item {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
width: calc(50% - 8px);
transition: var(--transition);
display: flex;
align-items: center;
cursor: pointer;
}
.expert-item:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.expert-avatar {
width: 60px;
height: 60px;
border-radius: 50%;
object-fit: cover;
margin-right: 15px;
}
.expert-info {
flex: 1;
}
.expert-name {
font-weight: bold;
margin-bottom: 5px;
}
.expert-title {
font-size: 0.9rem;
color: var(--secondary-color);
margin-bottom: 5px;
}
.expert-rating {
display: flex;
align-items: center;
}
.rating-stars {
color: #ffc107;
margin-right: 5px;
}
.rating-value {
font-size: 0.9rem;
color: var(--secondary-color);
}
.expert-status {
width: 10px;
height: 10px;
border-radius: 50%;
margin-left: 10px;
}
.status-online {
background-color: var(--primary-color);
}
.status-offline {
background-color: var(--secondary-color);
}
.expert-detail {
background: var(--light-bg);
padding: 20px;
border-radius: 10px;
margin-bottom: 20px;
display: flex;
flex-direction: column;
align-items: center;
}
.expert-detail-avatar {
width: 100px;
height: 100px;
border-radius: 50%;
object-fit: cover;
margin-bottom: 15px;
}
.expert-detail-name {
font-weight: bold;
font-size: 1.2rem;
margin-bottom: 5px;
}
.expert-detail-title {
color: var(--secondary-color);
margin-bottom: 15px;
}
.expert-detail-rating {
display: flex;
align-items: center;
margin-bottom: 15px;
}
.expert-detail-stats {
display: flex;
justify-content: space-around;
width: 100%;
margin-bottom: 15px;
}
.expert-stat {
text-align: center;
}
.expert-stat-value {
font-weight: bold;
font-size: 1.2rem;
}
.expert-stat-label {
font-size: 0.9rem;
color: var(--secondary-color);
}
.expert-detail-bio {
margin-bottom: 15px;
text-align: center;
}
.expert-detail-tags {
display: flex;
flex-wrap: wrap;
gap: 5px;
margin-bottom: 15px;
}
.expert-tag {
background-color: rgba(52, 152, 219, 0.1);
color: var(--expert-color);
padding: 3px 8px;
border-radius: 15px;
font-size: 0.8rem;
}
.chat-container {
background: var(--light-bg);
border-radius: 10px;
overflow: hidden;
display: flex;
flex-direction: column;
height: 500px;
}
.chat-header {
background-color: var(--expert-color);
color: white;
padding: 15px;
display: flex;
align-items: center;
}
.chat-header-avatar {
width: 40px;
height: 40px;
border-radius: 50%;
object-fit: cover;
margin-right: 15px;
}
.chat-header-info {
flex: 1;
}
.chat-header-name {
font-weight: bold;
}
.chat-header-status {
font-size: 0.9rem;
opacity: 0.8;
}
.chat-header-actions {
display: flex;
gap: 10px;
}
.chat-header-btn {
width: 30px;
height: 30px;
border-radius: 50%;
background-color: rgba(255, 255, 255, 0.2);
color: white;
display: flex;
align-items: center;
justify-content: center;
cursor: pointer;
transition: var(--transition);
}
.chat-header-btn:hover {
background-color: rgba(255, 255, 255, 0.3);
}
.chat-body {
flex: 1;
padding: 15px;
overflow-y: auto;
}
.chat-message {
margin-bottom: 15px;
display: flex;
}
.chat-message.incoming {
justify-content: flex-start;
}
.chat-message.outgoing {
justify-content: flex-end;
}
.message-avatar {
width: 40px;
height: 40px;
border-radius: 50%;
object-fit: cover;
margin-right: 10px;
}
.chat-message.outgoing .message-avatar {
display: none;
}
.message-content {
max-width: 70%;
}
.message-bubble {
padding: 10px 15px;
border-radius: 18px;
margin-bottom: 5px;
}
.chat-message.incoming .message-bubble {
background-color: white;
border-top-left-radius: 0;
}
.chat-message.outgoing .message-bubble {
background-color: var(--expert-color);
color: white;
border-top-right-radius: 0;
}
.message-time {
font-size: 0.8rem;
color: var(--secondary-color);
text-align: right;
}
.chat-message.outgoing .message-time {
color: rgba(255, 255, 255, 0.8);
}
.message-image {
max-width: 200px;
border-radius: 10px;
margin-bottom: 5px;
}
.chat-footer {
padding: 15px;
background-color: white;
display: flex;
align-items: center;
gap: 10px;
}
.chat-input {
flex: 1;
border: none;
background-color: var(--light-bg);
padding: 10px 15px;
border-radius: 20px;
outline: none;
}
.chat-btn {
width: 40px;
height: 40px;
border-radius: 50%;
background-color: var(--light-bg);
color: var(--secondary-color);
display: flex;
align-items: center;
justify-content: center;
cursor: pointer;
transition: var(--transition);
border: none;
}
.chat-btn:hover {
background-color: #e9ecef;
}
.chat-btn.send {
background-color: var(--expert-color);
color: white;
}
.chat-btn.send:hover {
background-color: #2980b9;
}
.consultation-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 15px;
transition: var(--transition);
}
.consultation-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.consultation-header {
display: flex;
justify-content: space-between;
align-items: center;
margin-bottom: 10px;
}
.consultation-title {
font-weight: bold;
}
.consultation-status {
display: inline-block;
padding: 3px 8px;
border-radius: 15px;
font-size: 0.8rem;
}
.status-pending {
background-color: rgba(255, 193, 7, 0.1);
color: #ffc107;
}
.status-active {
background-color: rgba(40, 167, 69, 0.1);
color: var(--primary-color);
}
.status-completed {
background-color: rgba(52, 152, 219, 0.1);
color: var(--expert-color);
}
.consultation-info {
display: flex;
margin-bottom: 10px;
}
.consultation-expert {
display: flex;
align-items: center;
margin-right: 15px;
}
.consultation-avatar {
width: 30px;
height: 30px;
border-radius: 50%;
object-fit: cover;
margin-right: 5px;
}
.consultation-name {
font-size: 0.9rem;
}
.consultation-time {
font-size: 0.9rem;
color: var(--secondary-color);
}
.consultation-preview {
font-size: 0.9rem;
color: var(--secondary-color);
margin-bottom: 10px;
white-space: nowrap;
overflow: hidden;
text-overflow: ellipsis;
}
.faq-item {
margin-bottom: 15px;
}
.faq-question {
font-weight: bold;
margin-bottom: 5px;
cursor: pointer;
display: flex;
justify-content: space-between;
align-items: center;
}
.faq-answer {
color: var(--secondary-color);
display: none;
padding: 10px 0;
}
.faq-answer.active {
display: block;
}
</style> </head> <body> <!-- 导航栏 -->
<div id="navbar-container"></div>
<!-- 主要内容 --> <div class="container py-5"> <div class="expert-header"> <div class="container"> <h1>专家在线问诊</h1> <p class="lead">上传作物图片，获取专业农技专家的诊断和建议，解决种植过程中遇到的各种问题</p> </div> </div> <!-- 顶部统计卡片 --> <div class="row mb-4"> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-user-md stat-icon"></i> <div class="stat-value">28</div> <div class="stat-label">在线专家</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-comments stat-icon"></i> <div class="stat-value">1,256</div> <div class="stat-label">已解决问题</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-clock stat-icon"></i> <div class="stat-value">15分钟</div> <div class="stat-label">平均响应时间</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-star stat-icon"></i> <div class="stat-value">4.8</div> <div class="stat-label">满意度评分</div> </div> </div> </div> <!-- 主要内容区域 --> <div class="row"> <!-- 左侧内容 --> <div class="col-md-8"> <!-- 图片上传区域 --> <div class="expert-card"> <h4 class="mb-4">问题描述</h4> <div class="upload-container" id="uploadContainer"> <i class="fas fa-cloud-upload-alt upload-icon"></i> <div class="upload-text">点击或拖拽图片到这里上传作物图片</div> <div class="upload-text text-secondary">支持 JPG, PNG 格式，大小不超过 10MB</div> <button class="btn btn-success">选择图片</button> <input type="file" id="fileUpload" style="display: none;" accept="image/*"> </div> <img src="" id="imagePreview" class="upload-preview"> <div class="mb-3 mt-3"> <label for="problemDescription" class="form-label">问题描述</label> <textarea class="form-control" id="problemDescription" rows="4" placeholder="请详细描述您遇到的问题，如作物种类、生长阶段、病形特征、发生时间等"></textarea> </div> <div class="mb-3"> <label class="form-label">问题类型</label> <select class="form-select" id="problemType"> <option selected>请选择问题类型</option> <option>病虫害识别</option> <option>养分缺失</option> <option>生长异常</option> <option>农事操作建议</option> <option>其他问题</option> </select> </div> <div class="mb-3"> <label class="form-label">紧急程度</label> <div> <div class="form-check form-check-inline"> <input class="form-check-input" type="radio" name="urgency" id="urgencyLow" value="low"> <label class="form-check-label" for="urgencyLow">一般</label> </div> <div class="form-check form-check-inline"> <input class="form-check-input" type="radio" name="urgency" id="urgencyMedium" value="medium" checked> <label class="form-check-label" for="urgencyMedium">重要</label> </div> <div class="form-check form-check-inline"> <input class="form-check-input" type="radio" name="urgency" id="urgencyHigh" value="high"> <label class="form-check-label" for="urgencyHigh">紧急</label> </div> </div> </div> <div class="d-grid gap-2"> <button class="btn btn-success" id="submitProblem">提交问题</button> </div> </div> <!-- 专家列表 --> <div class="expert-card"> <div class="d-flex justify-content-between align-items-center mb-4"> <h4>选择专家</h4> <div> <button class="btn btn-sm btn-outline-success me-2">全部</button> <button class="btn btn-sm btn-success me-2">在线</button> <button class="btn btn-sm btn-outline-success">推荐</button> </div> </div> <div class="expert-list"> <div class="expert-item"> <img src="images/expert1.jpg" alt="专家头像" class="expert-avatar"> <div class="expert-info"> <div class="expert-name">王农博士</div> <div class="expert-title">作物病理学专家 | 农业大学教授</div> <div class="expert-rating"> <div class="rating-stars"> <i class="fas fa-star"></i> <i class="fas fa-star"></i> <i class="fas fa-star"></i> <i class="fas fa-star"></i> <i class="fas fa-star-half-alt"></i> </div> <div class="rating-value">4.8 (256次评价)</div> <div class="expert-status status-online"></div> </div> </div> </div> <div class="expert-item"> <img src="images/expert2.jpg" alt="专家头像" class="expert-avatar"> <div class="expert-info"> <div class="expert-name">李农艺博士</div> <div class="expert-title">农业技术推广专家 | 农业科学院研究员</div> <div class="expert-rating"> <div class="rating-stars"> <i class="fas fa-star"></i> <i class="fas fa-star"></i> <i class="fas fa-star"></i> <i class="fas fa-star"></i> <i class="fas fa-star"></i> </div> <div class="rating-value">5.0 (189次评价)</div> <div class="expert-status status-online"></div> </div> </div> </div> <div class="expert-item"> <img src="images/expert3.jpg" alt="专家头像" class="expert-avatar"> <div class="expert-info"> <div class="expert-name">张农学博士</div> <div class="expert-title">土壤肥料专家 | 农业环境保护研究所副所长</div> <div class="expert-rating"> <div class="rating-stars"> <i class="fas fa-star"></i> <i class="fas fa-star"></i> <i class="fas fa-star"></i> <i class="fas fa-star"></i> <i class="far fa-star"></i> </div> <div class="rating-value">4.2 (145次评价)</div> <div class="expert-status status-online"></div> </div> </div> </div> <div class="expert-item"> <img src="images/expert4.jpg" alt="专家头像" class="expert-avatar"> <div class="expert-info"> <div class="expert-name">赵农技博士</div> <div class="expert-title">农业机械专家 | 农业工程学院教授</div> <div class="expert-rating"> <div class="rating-stars"> <i class="fas fa-star"></i> <i class="fas fa-star"></i> <i class="fas fa-star"></i> <i class="fas fa-star"></i> <i class="fas fa-star-half-alt"></i> </div> <div class="rating-value">4.6 (178次评价)</div> <div class="expert-status status-offline"></div> </div> </div> </div> </div> </div> <!-- 在线聊天 --> <div class="expert-card"> <h4 class="mb-4">在线咨询</h4> <div class="chat-container"> <div class="chat-header"> <img src="images/expert1.jpg" alt="专家头像" class="chat-header-avatar"> <div class="chat-header-info"> <div class="chat-header-name">王农博士</div> <div class="chat-header-status">在线</div> </div> <div class="chat-header-actions"> <div class="chat-header-btn"><i class="fas fa-phone"></i></div> <div class="chat-header-btn"><i class="fas fa-video"></i></div> <div class="chat-header-btn"><i class="fas fa-ellipsis-v"></i></div> </div> </div> <div class="chat-body" id="chatBody"> <div class="chat-message incoming"> <img src="images/expert1.jpg" alt="专家头像" class="message-avatar"> <div class="message-content"> <div class="message-bubble">您好，我是王农博士，作物病理学专家。请问您遇到了什么问题？</div> <div class="message-time">10:30</div> </div> </div> <div class="chat-message outgoing"> <div class="message-content"> <div class="message-bubble">您好，王博士。我的水稻叶片上出现了黄色斑点，而且叶片边缘卷曲，不知道是什么问题。</div> <div class="message-time">10:31</div> </div> </div> <div class="chat-message outgoing"> <div class="message-content"> <img src="images/disease1.jpg" alt="病害图片" class="message-image"> <div class="message-time">10:31</div> </div> </div> <div class="chat-message incoming"> <img src="images/expert1.jpg" alt="专家头像" class="message-avatar"> <div class="message-content"> <div class="message-bubble">根据您提供的图片和描述，这应该是稻飞虫导致的危害。稻飞虫是水稻主要害虫之一，成虫和若虫吸食水稻叶片汁液，导致叶片发黄、干枝。</div> <div class="message-time">10:33</div> </div> </div> <div class="chat-message incoming"> <img src="images/expert1.jpg" alt="专家头像" class="message-avatar"> <div class="message-content"> <div class="message-bubble">建议采取以下防治措施：
1. 使用生物农药《苯菌素》，每些用量50ml，稀释后均匀喷洒。
2. 在田间设置诱捕灯，诱杀成虫。
3. 加强田间管理，保持通风透光，减少病虫害发生条件。</div> <div class="message-time">10:34</div> </div> </div> <div class="chat-message outgoing"> <div class="message-content"> <div class="message-bubble">非常感谢您的详细解答！诱捕灯应该如何设置比较好？需要设置多少个？</div> <div class="message-time">10:36</div> </div> </div> <div class="chat-message incoming"> <img src="images/expert1.jpg" alt="专家头像" class="message-avatar"> <div class="message-content"> <div class="message-bubble">诱捕灯建议每些田地设置1-2个，高度以超过水稻株高50cm为宜。最好在傻瓜期到抚穗期间使用，每天晚上7点到凌晨5点开启。如果虫害发生面积较大，可以适当增加诱捕灯数量。</div> <div class="message-time">10:38</div> </div> </div> </div> <div class="chat-footer"> <button class="chat-btn"><i class="fas fa-paperclip"></i></button> <button class="chat-btn"><i class="fas fa-image"></i></button> <input type="text" class="chat-input" placeholder="输入消息..."> <button class="chat-btn send"><i class="fas fa-paper-plane"></i></button> </div> </div> </div> </div> <!-- 右侧内容 --> <div class="col-md-4"> <!-- 专家详情 --> <div class="expert-card"> <h4 class="mb-4">专家详情</h4> <div class="expert-detail"> <img src="images/expert1.jpg" alt="专家头像" class="expert-detail-avatar"> <div class="expert-detail-name">王农博士</div> <div class="expert-detail-title">作物病理学专家 | 农业大学教授</div> <div class="expert-detail-rating"> <div class="rating-stars"> <i class="fas fa-star"></i> <i class="fas fa-star"></i> <i class="fas fa-star"></i> <i class="fas fa-star"></i> <i class="fas fa-star-half-alt"></i> </div> <div class="rating-value">4.8 (256次评价)</div> </div> <div class="expert-detail-stats"> <div class="expert-stat"> <div class="expert-stat-value">15年</div> <div class="expert-stat-label">从业经验</div> </div> <div class="expert-stat"> <div class="expert-stat-value">568</div> <div class="expert-stat-label">解决问题</div> </div> <div class="expert-stat"> <div class="expert-stat-value">98%</div> <div class="expert-stat-label">满意度</div> </div> </div> <div class="expert-detail-bio">
王农博士是中国农业大学教授，专注于作物病理学研究，尤其擅长水稻、小麦等作物的病虫害识别与防治。发表论文超过50篇，获得国家发明专利多项。
</div> <div class="expert-detail-tags"> <div class="expert-tag">水稻病虫害</div> <div class="expert-tag">小麦病虫害</div> <div class="expert-tag">生物农药</div> <div class="expert-tag">绿色防控</div> <div class="expert-tag">有机种植</div> </div> <div class="d-grid gap-2"> <button class="btn btn-success">立即咨询</button> </div> </div> </div> <!-- 我的咨询 --> <div class="expert-card"> <h4 class="mb-4">我的咨询</h4> <div class="consultation-card"> <div class="consultation-header"> <div class="consultation-title">水稻叶片发黄问题</div> <div class="consultation-status status-active">进行中</div> </div> <div class="consultation-info"> <div class="consultation-expert"> <img src="images/expert1.jpg" alt="专家头像" class="consultation-avatar"> <div class="consultation-name">王农博士</div> </div> <div class="consultation-time">2023-06-20 10:30</div> </div> <div class="consultation-preview">王农博士: 根据您提供的图片和描述，这应该是稻飞虫导致的危害。稻飞虫是水稻主要害虫之一...</div> <div class="d-grid"> <button class="btn btn-outline-success btn-sm">继续咨询</button> </div> </div> <div class="consultation-card"> <div class="consultation-header"> <div class="consultation-title">小麦质量问题</div> <div class="consultation-status status-completed">已完成</div> </div> <div class="consultation-info"> <div class="consultation-expert"> <img src="images/expert2.jpg" alt="专家头像" class="consultation-avatar"> <div class="consultation-name">李农艺博士</div> </div> <div class="consultation-time">2023-06-15 14:45</div> </div> <div class="consultation-preview">李农艺博士: 您的小麦存在蛋白质含量偏低的问题，建议在抽穗期追施氮肥，每些用量...</div> <div class="d-grid"> <button class="btn btn-outline-success btn-sm">查看详情</button> </div> </div> <div class="consultation-card"> <div class="consultation-header"> <div class="consultation-title">现代豆种植建议</div> <div class="consultation-status status-pending">待回复</div> </div> <div class="consultation-info"> <div class="consultation-expert"> <img src="images/expert3.jpg" alt="专家头像" class="consultation-avatar"> <div class="consultation-name">张农学博士</div> </div> <div class="consultation-time">2023-06-18 09:15</div> </div> <div class="consultation-preview">您: 您好，我想咨询一下现代豆的种植建议，我的土地在华北地区，土壤偏碗性...</div> <div class="d-grid"> <button class="btn btn-outline-success btn-sm">查看详情</button> </div> </div> </div> <!-- 常见问题 --> <div class="expert-card"> <h4 class="mb-4">常见问题</h4> <div class="faq-item"> <div class="faq-question" onclick="toggleFaq(this)"> <span>如何选择适合的专家？</span> <i class="fas fa-chevron-down"></i> </div> <div class="faq-answer">
您可以根据专家的专业领域、评分和解决问题数量来选择适合的专家。每位专家都有自己的专长领域，如水稻病虫害、土壤肥料等，您可以选择与您问题相关的专家进行咨询。
</div> </div> <div class="faq-item"> <div class="faq-question" onclick="toggleFaq(this)"> <span>咨询费用是如何收取的？</span> <i class="fas fa-chevron-down"></i> </div> <div class="faq-answer">
我们提供两种咨询方式：基础咨询和高级咨询。基础咨询对所有用户免费，每天可以提问3个问题。高级咨询按次收费，专家会提供更详细的解决方案和后续跟进服务。
</div> </div> <div class="faq-item"> <div class="faq-question" onclick="toggleFaq(this)"> <span>如何提高问题解决效率？</span> <i class="fas fa-chevron-down"></i> </div> <div class="faq-answer">
为了提高问题解决效率，建议您：1) 提供清晰的作物图片，包括近景和全景；2) 详细描述问题发生的时间、地点、病形特征等；3) 提供作物生长环境、种植历史等背景信息。
</div> </div> <div class="faq-item"> <div class="faq-question" onclick="toggleFaq(this)"> <span>咨询结果不满意怎么办？</span> <i class="fas fa-chevron-down"></i> </div> <div class="faq-answer">
如果您对咨询结果不满意，可以在咨询结束后提交反馈。我们会安排其他专家进行二次评估，或者退还您的咨询费用。我们的目标是确保每位用户都能得到满意的解决方案。
</div> </div> </div> </div> </div> </div> <!-- 页脚 -->
<div id="footer-container"></div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="js/login-config.js"></script>
<script src="js/components.js"></script>
<script>
// 使用 common.js 加载导航栏

// 图片上传功能
const uploadContainer = document.getElementById('uploadContainer');
const fileUpload = document.getElementById('fileUpload');
const imagePreview = document.getElementById('imagePreview');
// 点击上传区域触发文件选择
uploadContainer.addEventListener('click', function() {
fileUpload.click();
});
// 文件选择后的处理
fileUpload.addEventListener('change', function() {
if (this.files && this.files[0]) {
const reader = new FileReader();
reader.onload = function(e) {
// 显示图片预览
imagePreview.src = e.target.result;
imagePreview.style.display = 'block';
}
reader.readAsDataURL(this.files[0]);
}
});
// 支持拖拽上传
['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
uploadContainer.addEventListener(eventName, preventDefaults, false);
});
function preventDefaults(e) {
e.preventDefault();
e.stopPropagation();
}
['dragenter', 'dragover'].forEach(eventName => {
uploadContainer.addEventListener(eventName, highlight, false);
});
['dragleave', 'drop'].forEach(eventName => {
uploadContainer.addEventListener(eventName, unhighlight, false);
});
function highlight() {
uploadContainer.style.borderColor = 'var(--primary-color)';
uploadContainer.style.backgroundColor = 'rgba(40, 167, 69, 0.1)';
}
function unhighlight() {
uploadContainer.style.borderColor = '#ddd';
uploadContainer.style.backgroundColor = 'white';
}
uploadContainer.addEventListener('drop', handleDrop, false);
function handleDrop(e) {
const dt = e.dataTransfer;
const files = dt.files;
if (files && files[0]) {
fileUpload.files = files;
const event = new Event('change');
fileUpload.dispatchEvent(event);
}
}
// 提交问题按钮点击事件
document.getElementById('submitProblem').addEventListener('click', function() {
const description = document.getElementById('problemDescription').value;
const problemType = document.getElementById('problemType').value;
if (!imagePreview.src || description.trim() === '' || problemType === '请选择问题类型') {
alert('请上传图片并填写完整的问题信息！');
return;
}
alert('问题已提交，专家将尽快回复！');
});
// 专家选择点击事件
document.querySelectorAll('.expert-item').forEach(item => {
item.addEventListener('click', function() {
const expertName = this.querySelector('.expert-name').textContent;
alert(`您已选择 ${expertName} 作为咨询专家！`);
});
});
// 发送消息按钮点击事件
document.querySelector('.chat-btn.send').addEventListener('click', function() {
const chatInput = document.querySelector('.chat-input');
const message = chatInput.value.trim();
if (message !== '') {
const chatBody = document.getElementById('chatBody');
const currentTime = new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
const messageHTML = `
<div class="chat-message outgoing"> <div class="message-content"> <div class="message-bubble">${message}</div> <div class="message-time">${currentTime}</div> </div> </div>
`;
chatBody.insertAdjacentHTML('beforeend', messageHTML);
chatBody.scrollTop = chatBody.scrollHeight;
chatInput.value = '';
// 模拟专家回复
setTimeout(function() {
const replyHTML = `
<div class="chat-message incoming"> <img src="images/expert1.jpg" alt="专家头像" class="message-avatar"> <div class="message-content"> <div class="message-bubble">我已收到您的消息，正在为您查询相关信息，请稍等。</div> <div class="message-time">${new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}</div> </div> </div>
`;
chatBody.insertAdjacentHTML('beforeend', replyHTML);
chatBody.scrollTop = chatBody.scrollHeight;
}, 1000);
}
});
// 聊天输入框回车事件
document.querySelector('.chat-input').addEventListener('keypress', function(e) {
if (e.key === 'Enter') {
document.querySelector('.chat-btn.send').click();
}
});
// 切换FAQ显示/隐藏
function toggleFaq(element) {
const answer = element.nextElementSibling;
const icon = element.querySelector('i');
if (answer.classList.contains('active')) {
answer.classList.remove('active');
icon.classList.remove('fa-chevron-up');
icon.classList.add('fa-chevron-down');
} else {
answer.classList.add('active');
icon.classList.remove('fa-chevron-down');
icon.classList.add('fa-chevron-up');
}
}
</script>