<!DOCTYPE html> <html lang="zh-CN"> <head> <meta charset="UTF-8"> <meta name="viewport" content="width=device-width, initial-scale=1.0"> <title>农事预警系统 - 智慧农业平台</title> <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet"> <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet"> <link rel="stylesheet" href="css/common.css"> <link rel="stylesheet" href="css/navbar.css"> <style>
:root {
--primary-color: #28a745;
--secondary-color: #6c757d;
--light-bg: #f8f9fa;
--card-shadow: 0 4px 6px rgba(0,0,0,0.1);
--transition: all 0.3s ease;
--alert-color: #dc3545;
}
body {
background-color: var(--light-bg);
font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}
.alert-header {
background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80');
background-size: cover;
background-position: center;
height: 200px;
color: white;
display: flex;
align-items: center;
margin-bottom: 30px;
border-radius: 10px;
}
.alert-card {
background: white;
padding: 20px;
border-radius: 10px;
box-shadow: var(--card-shadow);
margin-bottom: 30px;
min-height: auto;
}
.stat-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 20px;
transition: var(--transition);
}
.stat-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.stat-icon {
font-size: 2rem;
color: var(--alert-color);
margin-bottom: 10px;
}
.stat-value {
font-size: 2rem;
font-weight: bold;
color: var(--alert-color);
margin-bottom: 5px;
}
.stat-label {
font-size: 0.9rem;
color: var(--secondary-color);
}
.warning-item {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 15px;
transition: var(--transition);
border-left: 4px solid;
}
.warning-item:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.warning-high {
border-left-color: #dc3545;
}
.warning-medium {
border-left-color: #ffc107;
}
.warning-low {
border-left-color: #17a2b8;
}
.warning-header {
display: flex;
justify-content: space-between;
align-items: center;
margin-bottom: 10px;
}
.warning-title {
font-weight: bold;
display: flex;
align-items: center;
}
.warning-icon {
margin-right: 10px;
font-size: 1.2rem;
}
.warning-icon.high {
color: #dc3545;
}
.warning-icon.medium {
color: #ffc107;
}
.warning-icon.low {
color: #17a2b8;
}
.warning-time {
font-size: 0.9rem;
color: var(--secondary-color);
}
.warning-content {
margin-bottom: 10px;
color: var(--secondary-color);
}
.warning-actions {
display: flex;
gap: 10px;
}
.filter-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 20px;
}
.filter-title {
font-weight: bold;
margin-bottom: 10px;
}
.filter-options {
display: flex;
flex-wrap: wrap;
gap: 10px;
margin-bottom: 10px;
}
.filter-option {
display: inline-block;
padding: 5px 10px;
border-radius: 20px;
font-size: 0.9rem;
background-color: white;
color: var(--secondary-color);
cursor: pointer;
transition: var(--transition);
}
.filter-option:hover {
background-color: var(--primary-color);
color: white;
}
.filter-option.active {
background-color: var(--primary-color);
color: white;
}
.chart-container {
height: 300px;
width: 100%;
margin-bottom: 20px;
}
.nav-tabs .nav-link {
color: var(--secondary-color);
}
.nav-tabs .nav-link.active {
color: var(--primary-color);
font-weight: bold;
}
.tab-content {
padding: 20px 0;
}
.alert-badge {
display: inline-block;
padding: 5px 10px;
border-radius: 20px;
font-size: 0.8rem;
margin-right: 10px;
}
.alert-high {
background-color: #dc3545;
color: white;
}
.alert-medium {
background-color: #ffc107;
color: black;
}
.alert-low {
background-color: #17a2b8;
color: white;
}
</style> </head> <body> <!-- 导航栏 -->
<div id="navbar-container"></div>
<!-- 主要内容 --> <div class="container py-5"> <div class="alert-header"> <div class="container"> <h1>农事预警系统</h1> <p class="lead">实时监测农业生产环境，及时预警异常情况，减少农业风险</p> </div> </div> <!-- 顶部统计卡片 --> <div class="row mb-4"> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-exclamation-triangle stat-icon"></i> <div class="stat-value">12</div> <div class="stat-label">当前预警总数</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-thermometer-full stat-icon"></i> <div class="stat-value">5</div> <div class="stat-label">环境预警</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-bug stat-icon"></i> <div class="stat-value">4</div> <div class="stat-label">病虫害预警</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-calendar-alt stat-icon"></i> <div class="stat-value">3</div> <div class="stat-label">农事提醒</div> </div> </div> </div> <!-- 主要内容区域 --> <div class="row"> <!-- 左侧内容 --> <div class="col-md-8"> <!-- 预警列表 --> <div class="alert-card mb-4"> <h4 class="mb-4">预警列表</h4> <div class="warning-item warning-high"> <div class="warning-header"> <div class="warning-title"> <i class="fas fa-exclamation-circle warning-icon high"></i>
土壤湿度过低
</div> <div class="warning-time">2023-10-15 10:15</div> </div> <div class="warning-content">
A区块土壤湿度降至25%，低于警戒线(30%)，作物有脱水风险，建议立即灌溉。
</div> <div class="warning-actions"> <button class="btn btn-sm btn-danger">立即处理</button> <button class="btn btn-sm btn-outline-secondary">忽略</button> </div> </div> <div class="warning-item warning-high"> <div class="warning-header"> <div class="warning-title"> <i class="fas fa-exclamation-circle warning-icon high"></i>
检测到疑似病虫害
</div> <div class="warning-time">2023-10-15 09:30</div> </div> <div class="warning-content">
B区块图像识别系统检测到疑似白粉病迹象，建议立即检查并采取防治措施。
</div> <div class="warning-actions"> <button class="btn btn-sm btn-danger">立即处理</button> <button class="btn btn-sm btn-outline-secondary">忽略</button> </div> </div> <div class="warning-item warning-medium"> <div class="warning-header"> <div class="warning-title"> <i class="fas fa-exclamation-triangle warning-icon medium"></i>
温度异常波动
</div> <div class="warning-time">2023-10-15 08:45</div> </div> <div class="warning-content">
C区块温度在过去6小时内波动超过10°C，可能影响作物生长，建议检查温控系统。
</div> <div class="warning-actions"> <button class="btn btn-sm btn-warning">处理</button> <button class="btn btn-sm btn-outline-secondary">忽略</button> </div> </div> <div class="warning-item warning-medium"> <div class="warning-header"> <div class="warning-title"> <i class="fas fa-exclamation-triangle warning-icon medium"></i>
天气预警
</div> <div class="warning-time">2023-10-15 08:00</div> </div> <div class="warning-content">
气象部门发布强降雨预警，预计今晚至明天有大到暴雨，建议做好防涝准备。
</div> <div class="warning-actions"> <button class="btn btn-sm btn-warning">处理</button> <button class="btn btn-sm btn-outline-secondary">忽略</button> </div> </div> <div class="warning-item warning-low"> <div class="warning-header"> <div class="warning-title"> <i class="fas fa-info-circle warning-icon low"></i>
施肥提醒
</div> <div class="warning-time">2023-10-15 07:30</div> </div> <div class="warning-content">
根据种植计划，今天是D区块玉米的第二次追肥时间，建议按计划进行施肥作业。
</div> <div class="warning-actions"> <button class="btn btn-sm btn-info">处理</button> <button class="btn btn-sm btn-outline-secondary">忽略</button> </div> </div> <div class="warning-item warning-low"> <div class="warning-header"> <div class="warning-title"> <i class="fas fa-info-circle warning-icon low"></i>
设备维护提醒
</div> <div class="warning-time">2023-10-15 07:00</div> </div> <div class="warning-content">
灌溉系统已运行30天，根据维护计划，建议进行例行检查和清洗。
</div> <div class="warning-actions"> <button class="btn btn-sm btn-info">处理</button> <button class="btn btn-sm btn-outline-secondary">忽略</button> </div> </div> <div class="text-center mt-4"> <button class="btn btn-outline-primary">查看更多预警</button> </div> </div> <!-- 预警趋势 --> <div class="alert-card mb-4"> <h4 class="mb-4">预警趋势分析</h4> <div class="chart-container"> <img src="images/alert-trend.jpg" alt="预警趋势图" style="width: 100%; height: 100%; object-fit: cover;"> </div> <div class="row mt-3"> <div class="col-md-4"> <div class="text-center"> <div class="fw-bold">过去7天预警总数</div> <div class="fs-4 text-danger">78</div> <div class="text-muted">较上周增加12%</div> </div> </div> <div class="col-md-4"> <div class="text-center"> <div class="fw-bold">平均处理时间</div> <div class="fs-4 text-success">2.5小时</div> <div class="text-muted">较上周减少15%</div> </div> </div> <div class="col-md-4"> <div class="text-center"> <div class="fw-bold">预警解决率</div> <div class="fs-4 text-primary">92%</div> <div class="text-muted">较上周增加5%</div> </div> </div> </div> </div> </div> <!-- 右侧内容 --> <div class="col-md-4"> <!-- 筛选器 --> <div class="alert-card"> <h4 class="mb-4">预警筛选</h4> <div class="filter-card"> <div class="filter-title">预警类型</div> <div class="filter-options"> <div class="filter-option active">全部</div> <div class="filter-option">环境预警</div> <div class="filter-option">病虫害预警</div> <div class="filter-option">农事提醒</div> <div class="filter-option">设备预警</div> </div> </div> <div class="filter-card"> <div class="filter-title">预警级别</div> <div class="filter-options"> <div class="filter-option active">全部</div> <div class="filter-option">高级</div> <div class="filter-option">中级</div> <div class="filter-option">低级</div> </div> </div> <div class="filter-card"> <div class="filter-title">区块选择</div> <div class="filter-options"> <div class="filter-option active">全部</div> <div class="filter-option">A区块</div> <div class="filter-option">B区块</div> <div class="filter-option">C区块</div> <div class="filter-option">D区块</div> </div> </div> <div class="filter-card"> <div class="filter-title">时间范围</div> <div class="filter-options"> <div class="filter-option active">今天</div> <div class="filter-option">昨天</div> <div class="filter-option">本周</div> <div class="filter-option">本月</div> <div class="filter-option">自定义</div> </div> </div> <div class="d-grid mt-3"> <button class="btn btn-primary">应用筛选</button> </div> </div> <!-- 预警设置 --> <div class="alert-card mt-4"> <h4 class="mb-4">预警设置</h4> <div class="mb-3"> <label class="form-label">温度预警阈值</label> <div class="input-group"> <span class="input-group-text">最低</span> <input type="number" class="form-control" value="15"> <span class="input-group-text">°C</span> </div> </div> <div class="mb-3"> <div class="input-group"> <span class="input-group-text">最高</span> <input type="number" class="form-control" value="35"> <span class="input-group-text">°C</span> </div> </div> <div class="mb-3"> <label class="form-label">土壤湿度预警阈值</label> <div class="input-group"> <span class="input-group-text">最低</span> <input type="number" class="form-control" value="30"> <span class="input-group-text">%</span> </div> </div> <div class="mb-3"> <div class="input-group"> <span class="input-group-text">最高</span> <input type="number" class="form-control" value="80"> <span class="input-group-text">%</span> </div> </div> <div class="mb-3"> <label class="form-label">预警通知方式</label> <div class="form-check"> <input class="form-check-input" type="checkbox" id="notifyApp" checked> <label class="form-check-label" for="notifyApp">
APP推送
</label> </div> <div class="form-check"> <input class="form-check-input" type="checkbox" id="notifySMS" checked> <label class="form-check-label" for="notifySMS">
短信通知
</label> </div> <div class="form-check"> <input class="form-check-input" type="checkbox" id="notifyEmail"> <label class="form-check-label" for="notifyEmail">
邮件通知
</label> </div> <div class="form-check"> <input class="form-check-input" type="checkbox" id="notifyWechat" checked> <label class="form-check-label" for="notifyWechat">
微信通知
</label> </div> </div> <div class="d-grid mt-3"> <button class="btn btn-success">保存设置</button> </div> </div> </div> </div> </div> <!-- 页脚 -->
<div id="footer-container"></div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="js/login-config.js"></script>
<script src="js/components.js"></script>
<script>

// 筛选选项点击事件
document.querySelectorAll('.filter-option').forEach(option => {
option.addEventListener('click', function() {
const parent = this.parentElement;
parent.querySelectorAll('.filter-option').forEach(opt => {
opt.classList.remove('active');
});
this.classList.add('active');
});
});
// 应用筛选按钮点击事件
document.querySelector('.btn-primary').addEventListener('click', function() {
alert('正在应用筛选条件，请稍后...');
});
// 保存设置按钮点击事件
document.querySelector('.btn-success').addEventListener('click', function() {
alert('设置已保存成功！');
});
// 预警处理按钮点击事件
document.querySelectorAll('.warning-actions .btn-danger, .warning-actions .btn-warning, .warning-actions .btn-info').forEach(btn => {
btn.addEventListener('click', function() {
const warningTitle = this.closest('.warning-item').querySelector('.warning-title').textContent.trim();
alert(`正在处理预警：${warningTitle}，请稍后...`);
});
});
// 忽略按钮点击事件
document.querySelectorAll('.warning-actions .btn-outline-secondary').forEach(btn => {
btn.addEventListener('click', function() {
const warningItem = this.closest('.warning-item');
warningItem.style.opacity = '0.5';
setTimeout(() => {
warningItem.style.display = 'none';
}, 1000);
});
});
// 查看更多预警按钮点击事件
document.querySelector('.btn-outline-primary').addEventListener('click', function() {
alert('正在加载更多预警，请稍后...');
});

// 确保导航栏在页面加载完成后可见

// 页面加载完成后确保导航栏可见
window.onload = function() {
    // 确保导航栏可见
    const navbarContainer = document.getElementById('navbar-container');
    if (navbarContainer) {
        // 强制设置为可见
        navbarContainer.style.display = 'block';
        
        // 确保导航栏内容已加载
        if (!navbarContainer.innerHTML || navbarContainer.innerHTML.trim() === '') {
            // 如果导航栏内容为空，尝试重新加载
            if (typeof navbarHTML !== 'undefined') {
                navbarContainer.innerHTML = navbarHTML;
                
                // 重新应用登录状态和模块可见性
                if (typeof loadLoginState === 'function') loadLoginState();
                if (typeof updateNavbarDisplay === 'function') updateNavbarDisplay();
                if (typeof setActiveNavItem === 'function') setActiveNavItem();
                if (typeof setupLoginEvents === 'function') setupLoginEvents();
            }
        }
    }
};
</script> </body> </html>
