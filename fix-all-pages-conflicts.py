import os
import re

# 获取当前目录下所有的HTML文件
html_files = [f for f in os.listdir('.') if f.endswith('.html')]

# 更新每个HTML文件
for html_file in html_files:
    print(f"Processing {html_file}...")
    
    # 读取文件内容
    with open(html_file, 'r', encoding='utf-8', errors='ignore') as f:
        content = f.read()
    
    # 检查是否同时引用了components.js和components-loader.js
    if 'components.js' in content and 'components-loader.js' in content:
        # 删除components-loader.js引用
        content = re.sub(r'<script src="js/components-loader\.js"></script>\s*', '', content)
        print(f"  - Removed components-loader.js from {html_file}")
    
    # 检查是否引用了common.js和components.js
    if 'components.js' in content and 'common.js' in content:
        # 删除common.js引用
        content = re.sub(r'<script src="js/common\.js"></script>\s*', '', content)
        print(f"  - Removed common.js from {html_file}")
    
    # 保存更新后的文件
    with open(html_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"Completed processing {html_file}")

print('All HTML files have been updated to fix conflicts.')
