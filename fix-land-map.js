/**
 * 专门修复land-map.html页面的导航栏显示问题
 */

const fs = require('fs');

console.log('开始修复land-map.html页面的导航栏显示问题...');

try {
    // 读取文件内容
    let content = fs.readFileSync('land-map.html', 'utf8');
    const originalContent = content;
    
    // 备份原始文件
    fs.writeFileSync('land-map.html.bak', originalContent, 'utf8');
    console.log('已备份原文件: land-map.html.bak');
    
    // 1. 确保导航栏容器正确设置
    // 检查导航栏容器是否存在
    if (!content.includes('<div id="navbar-container"></div>')) {
        console.log('导航栏容器不存在，添加导航栏容器');
        content = content.replace(/<body>/, '<body>\n<!-- 导航栏 -->\n<div id="navbar-container"></div>');
    }
    
    // 2. 修复window.onload函数
    const onloadScript = `
// 页面加载完成后确保导航栏可见
window.onload = function() {
    console.log('land-map.html页面加载完成');
    
    // 确保导航栏可见
    const navbarContainer = document.getElementById('navbar-container');
    if (navbarContainer) {
        console.log('找到导航栏容器，设置为可见');
        // 强制设置为可见
        navbarContainer.style.display = 'block';
        
        // 确保导航栏内容已加载
        if (!navbarContainer.innerHTML || navbarContainer.innerHTML.trim() === '') {
            console.log('导航栏内容为空，尝试重新加载');
            // 如果导航栏内容为空，尝试重新加载
            if (typeof navbarHTML !== 'undefined') {
                navbarContainer.innerHTML = navbarHTML;
                
                // 重新应用登录状态和模块可见性
                if (typeof loadLoginState === 'function') {
                    console.log('重新加载登录状态');
                    loadLoginState();
                }
                if (typeof updateNavbarDisplay === 'function') {
                    console.log('更新导航栏显示');
                    updateNavbarDisplay();
                }
                if (typeof setActiveNavItem === 'function') {
                    console.log('设置当前页面的导航链接为激活状态');
                    setActiveNavItem();
                }
                if (typeof setupLoginEvents === 'function') {
                    console.log('绑定登录按钮事件');
                    setupLoginEvents();
                }
            } else {
                console.log('navbarHTML未定义，无法重新加载导航栏');
            }
        } else {
            console.log('导航栏内容已存在');
        }
    } else {
        console.log('未找到导航栏容器');
    }
};`;
    
    // 替换或添加window.onload函数
    if (content.includes('window.onload = function()')) {
        console.log('替换现有的window.onload函数');
        content = content.replace(/window\.onload\s*=\s*function\(\)\s*{[\s\S]*?};/g, onloadScript);
    } else {
        console.log('添加新的window.onload函数');
        // 在第一个script标签后添加
        const scriptIndex = content.indexOf('<script>', content.indexOf('</script>'));
        if (scriptIndex !== -1) {
            content = content.substring(0, scriptIndex + 8) + '\n' + onloadScript + '\n' + content.substring(scriptIndex + 8);
        } else {
            // 在最后一个script标签前添加
            const lastScriptIndex = content.lastIndexOf('<script>');
            if (lastScriptIndex !== -1) {
                content = content.substring(0, lastScriptIndex + 8) + '\n' + onloadScript + '\n' + content.substring(lastScriptIndex + 8);
            }
        }
    }
    
    // 3. 添加调试信息
    const debugScript = `
// 添加调试信息
console.log('land-map.html页面脚本开始执行');
document.addEventListener('DOMContentLoaded', function() {
    console.log('land-map.html DOMContentLoaded事件触发');
});`;
    
    // 在第一个script标签后添加调试信息
    const firstScriptIndex = content.indexOf('<script>');
    if (firstScriptIndex !== -1) {
        content = content.substring(0, firstScriptIndex + 8) + '\n' + debugScript + '\n' + content.substring(firstScriptIndex + 8);
    }
    
    // 4. 确保正确引用了components.js
    if (!content.includes('<script src="js/components.js"></script>')) {
        console.log('未找到components.js引用，添加components.js引用');
        // 在bootstrap.bundle.min.js后添加
        const bootstrapScriptIndex = content.indexOf('<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>');
        if (bootstrapScriptIndex !== -1) {
            content = content.substring(0, bootstrapScriptIndex + 91) + '\n<script src="js/login-config.js"></script>\n<script src="js/components.js"></script>' + content.substring(bootstrapScriptIndex + 91);
        }
    }
    
    // 保存修改后的文件
    fs.writeFileSync('land-map.html', content, 'utf8');
    console.log('成功修复land-map.html页面的导航栏显示问题');
} catch (error) {
    console.error('修复land-map.html页面时发生错误:', error.message);
}
