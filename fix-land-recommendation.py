import os

# 读取导航栏和页脚模板
with open('components/navbar.html', 'r', encoding='utf-8') as f:
    navbar_template = f.read().strip()

with open('components/footer.html', 'r', encoding='utf-8') as f:
    footer_template = f.read().strip()

# 读取 land-recommendation.html 文件
with open('land-recommendation.html', 'r', encoding='utf-8', errors='ignore') as f:
    content = f.read()

# 替换导航栏加载代码
start_marker = 'navbarContainer.innerHTML = `'
end_marker = '`;'
start_index = content.find(start_marker)
if start_index != -1:
    end_index = content.find(end_marker, start_index + len(start_marker))
    if end_index != -1:
        old_navbar_code = content[start_index + len(start_marker):end_index]
        new_content = content.replace(start_marker + old_navbar_code + end_marker, start_marker + navbar_template + end_marker)
        content = new_content

# 替换页脚加载代码
start_marker = 'footerContainer.innerHTML = `'
end_marker = '`;'
start_index = content.find(start_marker)
if start_index != -1:
    end_index = content.find(end_marker, start_index + len(start_marker))
    if end_index != -1:
        old_footer_code = content[start_index + len(start_marker):end_index]
        new_content = content.replace(start_marker + old_footer_code + end_marker, start_marker + footer_template + end_marker)
        content = new_content

# 保存更新后的文件
with open('land-recommendation.html', 'w', encoding='utf-8') as f:
    f.write(content)

print('land-recommendation.html has been updated with the new navbar and footer.')
