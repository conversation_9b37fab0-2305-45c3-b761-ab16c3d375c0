/**
 * 修复导航栏显示问题
 * 此脚本会检查所有HTML文件，确保它们都正确显示导航栏
 */

const fs = require('fs');
const path = require('path');

// 获取所有 HTML 文件
const htmlFiles = fs.readdirSync('.').filter(file => file.endsWith('.html'));

console.log(`找到 ${htmlFiles.length} 个需要处理的 HTML 文件`);

// 遍历所有 HTML 文件
let modifiedCount = 0;
htmlFiles.forEach(file => {
    console.log(`处理文件: ${file}`);
    
    try {
        // 读取文件内容
        let content = fs.readFileSync(file, 'utf8');
        let modified = false;
        
        // 1. 检查是否有冲突的DOMContentLoaded事件处理程序
        if (content.includes('document.addEventListener(\'DOMContentLoaded\'') && 
            content.includes('setActiveNavItem') && 
            !content.includes('js/components.js')) {
            
            console.log(`文件 ${file} 中发现冲突的DOMContentLoaded事件处理程序`);
            
            // 替换冲突的事件处理程序
            const regex = /document\.addEventListener\('DOMContentLoaded',\s*function\(\)\s*{[\s\S]*?}\);/g;
            content = content.replace(regex, '// 使用components.js中的DOMContentLoaded事件处理程序');
            modified = true;
        }
        
        // 2. 确保页面加载完成后导航栏可见
        if (content.includes('js/components.js') && !content.includes('window.onload = function()')) {
            console.log(`为文件 ${file} 添加window.onload事件处理程序`);
            
            // 在</script>前添加window.onload事件处理程序
            const scriptEndIndex = content.lastIndexOf('</script>');
            if (scriptEndIndex !== -1) {
                const onloadScript = `
// 确保导航栏在页面加载完成后可见
window.onload = function() {
    // 确保导航栏可见
    const navbarContainer = document.getElementById('navbar-container');
    if (navbarContainer) {
        navbarContainer.style.display = '';
    }
};
`;
                content = content.substring(0, scriptEndIndex) + onloadScript + content.substring(scriptEndIndex);
                modified = true;
            }
        }
        
        // 3. 确保导航栏容器存在且只有一个
        const navbarContainerCount = (content.match(/<div id="navbar-container"><\/div>/g) || []).length;
        if (navbarContainerCount > 1) {
            console.log(`文件 ${file} 中存在多个导航栏容器，进行修复`);
            
            // 只保留第一个导航栏容器
            let firstFound = false;
            content = content.replace(/<div id="navbar-container"><\/div>/g, match => {
                if (!firstFound) {
                    firstFound = true;
                    return match;
                }
                return '<!-- 移除重复的导航栏容器 -->';
            });
            modified = true;
        }
        
        // 如果进行了修改，保存文件
        if (modified) {
            fs.writeFileSync(file, content, 'utf8');
            console.log(`成功修改文件: ${file}`);
            modifiedCount++;
        } else {
            console.log(`文件 ${file} 不需要修改`);
        }
    } catch (error) {
        console.error(`处理文件 ${file} 时发生错误:`, error.message);
    }
});

console.log(`完成! 成功修改了 ${modifiedCount} 个文件`);
