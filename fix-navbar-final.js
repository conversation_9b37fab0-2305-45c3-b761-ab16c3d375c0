/**
 * 修复所有页面的导航栏显示问题 - 最终版
 */

const fs = require('fs');
const path = require('path');

// 获取所有 HTML 文件
const htmlFiles = fs.readdirSync('.').filter(file => file.endsWith('.html'));

console.log(`找到 ${htmlFiles.length} 个需要处理的 HTML 文件`);

// 通用的window.onload函数
const onloadScript = `
// 页面加载完成后确保导航栏可见
window.onload = function() {
    // 确保导航栏可见
    const navbarContainer = document.getElementById('navbar-container');
    if (navbarContainer) {
        // 强制设置为可见
        navbarContainer.style.display = 'block';
        
        // 确保导航栏内容已加载
        if (!navbarContainer.innerHTML || navbarContainer.innerHTML.trim() === '') {
            // 如果导航栏内容为空，尝试重新加载
            if (typeof navbarHTML !== 'undefined') {
                navbarContainer.innerHTML = navbarHTML;
                
                // 重新应用登录状态和模块可见性
                if (typeof loadLoginState === 'function') loadLoginState();
                if (typeof updateNavbarDisplay === 'function') updateNavbarDisplay();
                if (typeof setActiveNavItem === 'function') setActiveNavItem();
                if (typeof setupLoginEvents === 'function') setupLoginEvents();
            }
        }
    }
};`;

// 遍历所有 HTML 文件
let modifiedCount = 0;
htmlFiles.forEach(file => {
    console.log(`处理文件: ${file}`);
    
    try {
        // 读取文件内容
        let content = fs.readFileSync(file, 'utf8');
        let modified = false;
        
        // 1. 确保导航栏容器正确设置
        // 检查导航栏容器是否存在
        if (!content.includes('<div id="navbar-container"></div>') && !content.includes('<div id="navbar-container">')) {
            console.log(`文件 ${file} 中未找到导航栏容器，添加导航栏容器`);
            content = content.replace(/<body>/, '<body>\n<!-- 导航栏 -->\n<div id="navbar-container"></div>');
            modified = true;
        }
        
        // 2. 修复或添加window.onload函数
        if (content.includes('window.onload = function()')) {
            console.log(`替换文件 ${file} 中现有的window.onload函数`);
            content = content.replace(/window\.onload\s*=\s*function\(\)\s*{[\s\S]*?};/g, onloadScript);
            modified = true;
        } else {
            console.log(`为文件 ${file} 添加window.onload函数`);
            // 在最后一个script标签前添加
            const lastScriptEndIndex = content.lastIndexOf('</script>');
            if (lastScriptEndIndex !== -1) {
                content = content.substring(0, lastScriptEndIndex) + '\n' + onloadScript + '\n' + content.substring(lastScriptEndIndex);
                modified = true;
            }
        }
        
        // 3. 确保正确引用了components.js和login-config.js
        if (!content.includes('<script src="js/components.js"></script>')) {
            console.log(`文件 ${file} 中未找到components.js引用，添加components.js引用`);
            // 在bootstrap.bundle.min.js后添加
            const bootstrapScriptIndex = content.indexOf('<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>');
            if (bootstrapScriptIndex !== -1) {
                content = content.substring(0, bootstrapScriptIndex + 91) + '\n<script src="js/login-config.js"></script>\n<script src="js/components.js"></script>' + content.substring(bootstrapScriptIndex + 91);
                modified = true;
            }
        } else if (!content.includes('<script src="js/login-config.js"></script>')) {
            console.log(`文件 ${file} 中未找到login-config.js引用，添加login-config.js引用`);
            // 在components.js前添加
            const componentsScriptIndex = content.indexOf('<script src="js/components.js"></script>');
            if (componentsScriptIndex !== -1) {
                content = content.substring(0, componentsScriptIndex) + '<script src="js/login-config.js"></script>\n' + content.substring(componentsScriptIndex);
                modified = true;
            }
        }
        
        // 4. 确保导航栏容器只有一个
        const navbarContainerCount = (content.match(/<div id="navbar-container"><\/div>/g) || []).length + (content.match(/<div id="navbar-container">/g) || []).length - (content.match(/<div id="navbar-container">/g) || []).length;
        if (navbarContainerCount > 1) {
            console.log(`文件 ${file} 中存在多个导航栏容器，进行修复`);
            
            // 只保留第一个导航栏容器
            let firstFound = false;
            content = content.replace(/<div id="navbar-container">(?:<\/div>)?/g, match => {
                if (!firstFound) {
                    firstFound = true;
                    return '<div id="navbar-container"></div>';
                }
                return '<!-- 移除重复的导航栏容器 -->';
            });
            modified = true;
        }
        
        // 如果进行了修改，保存文件
        if (modified) {
            fs.writeFileSync(file, content, 'utf8');
            console.log(`成功修改文件: ${file}`);
            modifiedCount++;
        } else {
            console.log(`文件 ${file} 不需要修改`);
        }
    } catch (error) {
        console.error(`处理文件 ${file} 时发生错误:`, error.message);
    }
});

console.log(`完成! 成功修改了 ${modifiedCount} 个文件`);
