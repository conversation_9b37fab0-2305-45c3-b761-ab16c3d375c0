<!DOCTYPE html> <html lang="zh-CN"> <head> <meta charset="UTF-8"> <meta name="viewport" content="width=device-width, initial-scale=1.0"> <title>智慧农业数字化平台</title> <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet"> <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet"> <link rel="stylesheet" href="css/common.css"> <link rel="stylesheet" href="css/navbar.css"> <style>
        :root {
            --primary-color: #28a745;
            --secondary-color: #6c757d;
            --light-bg: #f8f9fa;
            --card-shadow: 0 4px 6px rgba(0,0,0,0.1);
            --transition: all 0.3s ease;
        }
        .hero-section {
            background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80');
            background-size: cover;
            background-position: center;
            height: 500px;
            color: white;
        }
        .feature-card {
            transition: transform 0.3s;
            margin-bottom: 20px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            cursor: pointer;
            color: inherit;
        }
        .feature-card:hover {
            transform: translateY(-5px);
        }
        .section-title {
            position: relative;
            margin-bottom: 40px;
        }
        .section-title:after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 50px;
            height: 3px;
            background-color: var(--primary-color);
        }
        .card-link {
            color: inherit;
            text-decoration: none;
        }
        .card-link:hover {
            color: inherit;
        }
        .card-link .card {
            border: 1px solid rgba(0,0,0,0.1);
        }
        .card-link:hover .card {
            border-color: var(--primary-color);
        }
        .navbar {
            padding: 0.8rem 0;
            background-color: #28a745 !important;
        }
        .navbar-brand {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .brand-logo {
            width: 40px;
            height: 40px;
        }
        .brand-logo img {
            width: 100%;
min-height: auto;
            object-fit: contain;
        }
        .navbar-nav {
            gap: 0.5rem;
        }
        .nav-link {
            font-size: 1rem;
            padding: 0.5rem 1rem !important;
            position: relative;
            color: rgba(255, 255, 255, 0.9) !important;
        }
        .nav-link:hover,
        .nav-link.active {
            color: #fff !important;
        }
        .nav-link::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 2px;
            background: white;
            transition: width 0.3s ease;
        }
        .nav-link:hover::after,
        .nav-link.active::after {
            width: 100%;
        }
        .navbar-toggler {
            border: none;
            padding: 0.5rem;
        }
        .navbar-toggler:focus {
            box-shadow: none;
        }
        @media (max-width: 991.98px) {
            .navbar-collapse {
                background-color: #28a745;
                padding: 1rem;
                margin-top: 0.5rem;
                border-radius: 0.25rem;
            }
            .nav-link {
                padding: 0.5rem 0 !important;
            }
        }
</style> </head> <body> <!-- 导航栏 -->
<div id="navbar-container"></div> <!-- 首页横幅 --> <section class="hero-section d-flex align-items-center"> <div class="container text-center"> <h1 class="display-4">智慧农业数字化平台</h1> <p class="lead">连接农业产业链，打造数字化农业生态</p> <a href="land-rental.html" class="btn btn-success btn-lg">立即体验</a> </div> </section> <!-- 主要功能模块 --> <section class="py-5"> <div class="container"> <h2 class="text-center section-title">模块一：土地租赁/种植订单</h2> <div class="row"> <div class="col-md-3"> <a href="land-rental.html" class="card-link"> <div class="card feature-card"> <div class="card-body"> <i class="fas fa-map-marked-alt fa-3x text-success mb-3"></i> <h5 class="card-title">土地租赁系统</h5> <p class="card-text">在线租赁优质农业土地，支持地图模式浏览</p> </div> </div> </a> </div> <div class="col-md-3"> <a href="planting-order.html" class="card-link"> <div class="card feature-card"> <div class="card-body"> <i class="fas fa-file-contract fa-3x text-success mb-3"></i> <h5 class="card-title">种植订单系统</h5> <p class="card-text">定制化种植订单，满足个性化需求</p> </div> </div> </a> </div> <div class="col-md-3"> <a href="land-selection.html" class="card-link"> <div class="card feature-card"> <div class="card-body"> <i class="fas fa-search-location fa-3x text-success mb-3"></i> <h5 class="card-title">土地选择页面</h5> <p class="card-text">多维度筛选，找到最适合的土地</p> </div> </div> </a> </div> <div class="col-md-3"> <a href="order-tracking.html" class="card-link"> <div class="card feature-card"> <div class="card-body"> <i class="fas fa-chart-line fa-3x text-success mb-3"></i> <h5 class="card-title">订单跟踪系统</h5> <p class="card-text">实时跟踪种植进度，全程透明可见</p> </div> </div> </a> </div> </div> </div> </section> <section class="py-5 bg-light"> <div class="container"> <h2 class="text-center section-title">模块二：农产品品牌化</h2> <div class="row"> <div class="col-md-3"> <a href="brand-creation.html" class="card-link"> <div class="card feature-card"> <div class="card-body"> <i class="fas fa-copyright fa-3x text-info mb-3"></i> <h5 class="card-title">品牌创建系统</h5> <p class="card-text">打造专属农产品品牌，提升市场竞争力</p> </div> </div> </a> </div> <div class="col-md-3"> <a href="packaging-design.html" class="card-link"> <div class="card feature-card"> <div class="card-body"> <i class="fas fa-box-open fa-3x text-info mb-3"></i> <h5 class="card-title">包装设计系统</h5> <p class="card-text">专业包装设计，提升产品价值感</p> </div> </div> </a> </div> <div class="col-md-3"> <a href="marketing-promotion.html" class="card-link"> <div class="card feature-card"> <div class="card-body"> <i class="fas fa-bullhorn fa-3x text-info mb-3"></i> <h5 class="card-title">营销推广系统</h5> <p class="card-text">多渠道营销推广，扩大品牌影响力</p> </div> </div> </a> </div> <div class="col-md-3"> <a href="brand-story.html" class="card-link"> <div class="card feature-card"> <div class="card-body"> <i class="fas fa-book-open fa-3x text-info mb-3"></i> <h5 class="card-title">品牌故事系统</h5> <p class="card-text">讲述品牌背后的故事，建立情感连接</p> </div> </div> </a> </div> </div> </div> </section> <section class="py-5"> <div class="container"> <h2 class="text-center section-title">模块三：全过程监控系统</h2> <div class="row"> <div class="col-md-4"> <a href="sensor-dashboard.html" class="card-link"> <div class="card feature-card"> <div class="card-body"> <i class="fas fa-microchip fa-3x text-warning mb-3"></i> <h5 class="card-title">智能传感数据看板</h5> <p class="card-text">实时监测土壤、气候等环境数据</p> </div> </div> </a> </div> <div class="col-md-4"> <a href="farm-alert.html" class="card-link"> <div class="card feature-card"> <div class="card-body"> <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i> <h5 class="card-title">农事预警系统</h5> <p class="card-text">异常情况及时预警，减少农业风险</p> </div> </div> </a> </div> <div class="col-md-4"> <a href="crop-recognition.html" class="card-link"> <div class="card feature-card"> <div class="card-body"> <i class="fas fa-camera fa-3x text-warning mb-3"></i> <h5 class="card-title">作物图像识别系统</h5> <p class="card-text">AI识别作物生长状况和病虫害</p> </div> </div> </a> </div> </div> <div class="row mt-4"> <div class="col-md-4"> <a href="blockchain-proof.html" class="card-link"> <div class="card feature-card"> <div class="card-body"> <i class="fas fa-cubes fa-3x text-warning mb-3"></i> <h5 class="card-title">区块链存证</h5> <p class="card-text">关键生产节点区块链存证，保障可信度</p> </div> </div> </a> </div> <div class="col-md-4"> <a href="remote-control.html" class="card-link"> <div class="card feature-card"> <div class="card-body"> <i class="fas fa-robot fa-3x text-warning mb-3"></i> <h5 class="card-title">远程控制农业设备</h5> <p class="card-text">远程操控灌溉、施肥等农业设备</p> </div> </div> </a> </div> <div class="col-md-4"> <a href="expert-consultation.html" class="card-link"> <div class="card feature-card"> <div class="card-body"> <i class="fas fa-user-md fa-3x text-warning mb-3"></i> <h5 class="card-title">专家在线问诊</h5> <p class="card-text">农业专家在线解答种植问题</p> </div> </div> </a> </div> </div> </div> </section> <section class="py-5 bg-light"> <div class="container"> <h2 class="text-center section-title">模块四：社群运营体系</h2> <div class="row"> <div class="col-md-4"> <a href="distribution-system.html" class="card-link"> <div class="card feature-card"> <div class="card-body"> <i class="fas fa-sitemap fa-3x text-danger mb-3"></i> <h5 class="card-title">分销系统总览</h5> <p class="card-text">完整的社群运营体系架构与功能</p> </div> </div> </a> </div> <div class="col-md-4"> <a href="commission-dashboard.html" class="card-link"> <div class="card feature-card"> <div class="card-body"> <i class="fas fa-money-bill-wave fa-3x text-danger mb-3"></i> <h5 class="card-title">佣金看板</h5> <p class="card-text">实时查看佣金收入与结算情况</p> </div> </div> </a> </div> <div class="col-md-4"> <a href="promotion-tools.html" class="card-link"> <div class="card feature-card"> <div class="card-body"> <i class="fas fa-tools fa-3x text-danger mb-3"></i> <h5 class="card-title">推广工具</h5> <p class="card-text">丰富的推广素材与工具支持</p> </div> </div> </a> </div> </div> <div class="row mt-4"> <div class="col-md-4"> <a href="invitation-ranking.html" class="card-link"> <div class="card feature-card"> <div class="card-body"> <i class="fas fa-trophy fa-3x text-danger mb-3"></i> <h5 class="card-title">邀请排行榜</h5> <p class="card-text">邀请好友排行榜与奖励机制</p> </div> </div> </a> </div> <div class="col-md-4"> <a href="consumer-conversion.html" class="card-link"> <div class="card feature-card"> <div class="card-body"> <i class="fas fa-exchange-alt fa-3x text-danger mb-3"></i> <h5 class="card-title">消费者转化工具</h5> <p class="card-text">提高消费者转化率的专业工具</p> </div> </div> </a> </div> <div class="col-md-4"> <a href="team-incentives.html" class="card-link"> <div class="card feature-card"> <div class="card-body"> <i class="fas fa-users fa-3x text-danger mb-3"></i> <h5 class="card-title">团队激励机制</h5> <p class="card-text">团队业绩奖励与激励体系</p> </div> </div> </a> </div> </div> </div> </section> <section class="py-5"> <div class="container"> <h2 class="text-center section-title">模块五：农资农机交易系统</h2> <div class="row"> <div class="col-md-4 mb-4"> <a href="equipment-rental.html" class="card-link"> <div class="card feature-card"> <div class="card-body"> <i class="fas fa-tractor fa-3x text-purple mb-3" style="color: #6610f2;"></i> <h5 class="card-title">智能农机设备租赁</h5> <p class="card-text">现代化农机设备在线租赁服务</p> </div> </div> </a> </div> <div class="col-md-4 mb-4"> <a href="agricultural-supplies.html" class="card-link"> <div class="card feature-card"> <div class="card-body"> <i class="fas fa-seedling fa-3x text-purple mb-3" style="color: #6610f2;"></i> <h5 class="card-title">农资采购金融服务</h5> <p class="card-text">农资采购与金融支持服务</p> </div> </div> </a> </div> <div class="col-md-4 mb-4"> <a href="used-equipment.html" class="card-link"> <div class="card feature-card"> <div class="card-body"> <i class="fas fa-recycle fa-3x text-purple mb-3" style="color: #6610f2;"></i> <h5 class="card-title">二手农机交易市场</h5> <p class="card-text">二手农机设备交易平台</p> </div> </div> </a> </div> <div class="col-md-6 mb-4"> <a href="subscription-service.html" class="card-link"> <div class="card feature-card"> <div class="card-body"> <i class="fas fa-calendar-alt fa-3x text-purple mb-3" style="color: #6610f2;"></i> <h5 class="card-title">农资订阅服务</h5> <p class="card-text">定期配送农资产品的订阅服务</p> </div> </div> </a> </div> <div class="col-md-6 mb-4"> <a href="ar-preview.html" class="card-link"> <div class="card feature-card"> <div class="card-body"> <i class="fas fa-vr-cardboard fa-3x text-purple mb-3" style="color: #6610f2;"></i> <h5 class="card-title">AR虚拟试用功能</h5> <p class="card-text">AR技术虚拟体验农机设备</p> </div> </div> </a> </div> </div> </div> </section> <section class="py-5 bg-light"> <div class="container"> <h2 class="text-center section-title">模块六：运营支撑体系</h2> <div class="row"> <div class="col-md-4 mb-4"> <a href="digital-farm.html" class="card-link"> <div class="card feature-card"> <div class="card-body"> <i class="fas fa-building fa-3x text-orange mb-3" style="color: #fd7e14;"></i> <h5 class="card-title">数字农场试点</h5> <p class="card-text">与政府合作的数字农场试点项目</p> </div> </div> </a> </div> <div class="col-md-4 mb-4"> <a href="referral-program.html" class="card-link"> <div class="card feature-card"> <div class="card-body"> <i class="fas fa-gift fa-3x text-orange mb-3" style="color: #fd7e14;"></i> <h5 class="card-title">邀请好友赠礼包</h5> <p class="card-text">邀请好友注册赠送农资礼包</p> </div> </div> </a> </div> <div class="col-md-4 mb-4"> <a href="kol-incubation.html" class="card-link"> <div class="card feature-card"> <div class="card-body"> <i class="fas fa-user-graduate fa-3x text-orange mb-3" style="color: #fd7e14;"></i> <h5 class="card-title">新农人KOL孵化</h5> <p class="card-text">培养农业领域的意见领袖</p> </div> </div> </a> </div> <div class="col-md-6 mb-4"> <a href="exclusive-cooperation.html" class="card-link"> <div class="card feature-card"> <div class="card-body"> <i class="fas fa-handshake fa-3x text-orange mb-3" style="color: #fd7e14;"></i> <h5 class="card-title">独家合作协议</h5> <p class="card-text">与基地签订独家合作协议</p> </div> </div> </a> </div> <div class="col-md-6 mb-4"> <a href="logistics-center.html" class="card-link"> <div class="card feature-card"> <div class="card-body"> <i class="fas fa-truck-loading fa-3x text-orange mb-3" style="color: #fd7e14;"></i> <h5 class="card-title">冷链物流服务</h5> <p class="card-text">接入顺丰/京东冷链物流服务</p> </div> </div> </a> </div> </div> </div> </section> <!-- 页脚 -->
<div id="footer-container"></div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js">
<script src="js/login-config.js"></script>
<script src="js/components.js"></script></script>
<script src="js/login-config.js"></script>
<script src="js/components.js">
// 确保导航栏在页面加载完成后可见

// 页面加载完成后确保导航栏可见
window.onload = function() {
    // 确保导航栏可见
    const navbarContainer = document.getElementById('navbar-container');
    if (navbarContainer) {
        // 强制设置为可见
        navbarContainer.style.display = 'block';
        
        // 确保导航栏内容已加载
        if (!navbarContainer.innerHTML || navbarContainer.innerHTML.trim() === '') {
            // 如果导航栏内容为空，尝试重新加载
            if (typeof navbarHTML !== 'undefined') {
                navbarContainer.innerHTML = navbarHTML;
                
                // 重新应用登录状态和模块可见性
                if (typeof loadLoginState === 'function') loadLoginState();
                if (typeof updateNavbarDisplay === 'function') updateNavbarDisplay();
                if (typeof setActiveNavItem === 'function') setActiveNavItem();
                if (typeof setupLoginEvents === 'function') setupLoginEvents();
            }
        }
    }
};
</script>



 </body> </html>