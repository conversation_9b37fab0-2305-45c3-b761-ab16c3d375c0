<!DOCTYPE html> <html lang="zh-CN"> <head> <meta charset="UTF-8"> <meta name="viewport" content="width=device-width, initial-scale=1.0"> <title>邀请排行榜与激励机制 - 智慧农业平台</title> <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet"> <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet"> <link rel="stylesheet" href="css/common.css"> <link rel="stylesheet" href="css/navbar.css"> <style>
:root {
--primary-color: #28a745;
--secondary-color: #6c757d;
--light-bg: #f8f9fa;
--card-shadow: 0 4px 6px rgba(0,0,0,0.1);
--transition: all 0.3s ease;
--distribution-color: #8e44ad;
--level1-color: #3498db;
--level2-color: #e67e22;
--gold-color: #f1c40f;
--silver-color: #bdc3c7;
--bronze-color: #e67e22;
}
body {
background-color: var(--light-bg);
font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}
.ranking-header {
background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80');
background-size: cover;
background-position: center;
height: 200px;
color: white;
display: flex;
align-items: center;
margin-bottom: 30px;
border-radius: 10px;
}
.ranking-card {
background: white;
padding: 20px;
border-radius: 10px;
box-shadow: var(--card-shadow);
margin-bottom: 30px;
min-height: auto;
}
.stat-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 20px;
transition: var(--transition);
}
.stat-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.stat-icon {
font-size: 2rem;
color: var(--distribution-color);
margin-bottom: 10px;
}
.stat-value {
font-size: 1.5rem;
font-weight: bold;
color: var(--distribution-color);
margin-bottom: 5px;
}
.stat-label {
font-size: 0.9rem;
color: var(--secondary-color);
}
.filter-btn {
padding: 5px 15px;
border-radius: 20px;
background-color: white;
border: 1px solid #ddd;
margin-right: 10px;
margin-bottom: 10px;
cursor: pointer;
transition: var(--transition);
}
.filter-btn.active {
background-color: var(--primary-color);
color: white;
border-color: var(--primary-color);
}
.filter-btn:hover:not(.active) {
background-color: #f0f0f0;
}
.rank-item {
display: flex;
align-items: center;
padding: 15px;
background-color: var(--light-bg);
border-radius: 10px;
margin-bottom: 15px;
transition: var(--transition);
}
.rank-item:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.rank-number {
width: 40px;
height: 40px;
border-radius: 50%;
display: flex;
align-items: center;
justify-content: center;
margin-right: 15px;
font-weight: bold;
color: white;
flex-shrink: 0;
}
.rank-1 {
background-color: var(--gold-color);
}
.rank-2 {
background-color: var(--silver-color);
}
.rank-3 {
background-color: var(--bronze-color);
}
.rank-other {
background-color: var(--secondary-color);
}
.rank-avatar {
width: 50px;
height: 50px;
border-radius: 50%;
object-fit: cover;
margin-right: 15px;
}
.rank-info {
flex: 1;
}
.rank-name {
font-weight: bold;
margin-bottom: 5px;
display: flex;
align-items: center;
}
.rank-badge {
display: inline-block;
padding: 2px 8px;
border-radius: 15px;
font-size: 0.7rem;
margin-left: 10px;
}
.badge-level1 {
background-color: rgba(52, 152, 219, 0.1);
color: var(--level1-color);
}
.badge-level2 {
background-color: rgba(230, 126, 34, 0.1);
color: var(--level2-color);
}
.rank-role {
font-size: 0.9rem;
color: var(--secondary-color);
}
.rank-stats {
display: flex;
gap: 15px;
font-size: 0.9rem;
margin-top: 5px;
}
.rank-stat {
display: flex;
align-items: center;
}
.rank-stat-icon {
color: var(--distribution-color);
margin-right: 5px;
}
.rank-value {
display: flex;
flex-direction: column;
align-items: flex-end;
margin-left: 15px;
}
.rank-score {
font-weight: bold;
font-size: 1.2rem;
color: var(--distribution-color);
}
.rank-label {
font-size: 0.8rem;
color: var(--secondary-color);
}
.reward-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 15px;
transition: var(--transition);
min-height: auto;
}
.reward-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.reward-header {
display: flex;
align-items: center;
margin-bottom: 15px;
}
.reward-icon {
width: 50px;
height: 50px;
border-radius: 10px;
background-color: var(--distribution-color);
color: white;
display: flex;
align-items: center;
justify-content: center;
font-size: 1.5rem;
margin-right: 15px;
flex-shrink: 0;
}
.reward-title {
font-weight: bold;
font-size: 1.1rem;
margin-bottom: 5px;
}
.reward-subtitle {
font-size: 0.9rem;
color: var(--secondary-color);
}
.reward-body {
margin-bottom: 15px;
}
.reward-description {
color: var(--secondary-color);
margin-bottom: 15px;
}
.reward-condition {
background-color: white;
padding: 10px;
border-radius: 5px;
margin-bottom: 10px;
}
.reward-condition-title {
font-weight: bold;
margin-bottom: 5px;
display: flex;
align-items: center;
}
.reward-condition-icon {
color: var(--distribution-color);
margin-right: 5px;
}
.reward-condition-text {
font-size: 0.9rem;
}
.reward-footer {
text-align: center;
}
.progress-container {
height: 8px;
background-color: #e9ecef;
border-radius: 4px;
margin-bottom: 5px;
overflow: hidden;
}
.progress-bar {
min-height: auto;
border-radius: 4px;
}
.progress-level1 {
background-color: var(--level1-color);
}
.progress-level2 {
background-color: var(--level2-color);
}
.progress-text {
display: flex;
justify-content: space-between;
font-size: 0.8rem;
color: var(--secondary-color);
}
.rule-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 15px;
transition: var(--transition);
}
.rule-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.rule-header {
display: flex;
justify-content: space-between;
align-items: center;
margin-bottom: 15px;
}
.rule-title {
font-weight: bold;
display: flex;
align-items: center;
}
.rule-icon {
color: var(--distribution-color);
margin-right: 10px;
}
.rule-body {
margin-bottom: 15px;
}
.rule-item {
display: flex;
margin-bottom: 10px;
}
.rule-number {
width: 24px;
height: 24px;
border-radius: 50%;
background-color: var(--distribution-color);
color: white;
display: flex;
align-items: center;
justify-content: center;
margin-right: 10px;
font-size: 0.8rem;
flex-shrink: 0;
}
.rule-content {
flex: 1;
}
.rule-text {
margin-bottom: 5px;
}
.rule-note {
font-size: 0.9rem;
color: var(--secondary-color);
}
.chart-container {
position: relative;
height: 300px;
width: 100%;
margin-bottom: 20px;
}
.faq-item {
margin-bottom: 15px;
}
.faq-question {
font-weight: bold;
margin-bottom: 5px;
cursor: pointer;
display: flex;
justify-content: space-between;
align-items: center;
}
.faq-answer {
color: var(--secondary-color);
display: none;
padding: 10px 0;
}
.faq-answer.active {
display: block;
}
.nav-tabs .nav-link {
color: var(--secondary-color);
}
.nav-tabs .nav-link.active {
color: var(--distribution-color);
font-weight: bold;
}
.tab-content {
padding: 20px 0;
}

/* 修复表格字体颜色问题 */
#invites, #conversion, #reward {
	color: #333;
}

.tab-pane .rank-name, .tab-pane .rank-role, .tab-pane .rank-stats, .tab-pane .rank-stat {
	color: #333;
}
</style> </head> <body> <!-- 导航栏 -->
<div id="navbar-container"></div>
<!-- 主要内容 --> <div class="container py-5"> <div class="ranking-header"> <div class="container"> <h1>邀请排行榜与激励机制</h1> <p class="lead">查看分销商邀请排行榜，了解激励机制规则，提高团队发展效率，获取更多奖励</p> </div> </div> <!-- 顶部统计卡片 --> <div class="row mb-4"> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-user-plus stat-icon"></i> <div class="stat-value">1,256</div> <div class="stat-label">本月邀请总数</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-trophy stat-icon"></i> <div class="stat-value">68</div> <div class="stat-label">活跃邀请者</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-gift stat-icon"></i> <div class="stat-value">25,680</div> <div class="stat-label">本月奖励金额</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-chart-line stat-icon"></i> <div class="stat-value">+18.5%</div> <div class="stat-label">邀请增长率</div> </div> </div> </div> <!-- 主要内容区域 --> <div class="row"> <!-- 左侧内容 --> <div class="col-md-8"> <!-- 邀请排行榜 --> <div class="ranking-card"> <div class="d-flex justify-content-between align-items-center mb-4"> <h4>邀请排行榜</h4> <div> <button class="filter-btn">日</button> <button class="filter-btn">周</button> <button class="filter-btn active">月</button> <button class="filter-btn">年</button> </div> </div> <ul class="nav nav-tabs" id="rankingTabs" role="tablist"> <li class="nav-item" role="presentation"> <button class="nav-link active" id="invites-tab" data-bs-toggle="tab" data-bs-target="#invites" type="button" role="tab" aria-controls="invites" aria-selected="true">邀请数量排行</button> </li> <li class="nav-item" role="presentation"> <button class="nav-link" id="conversion-tab" data-bs-toggle="tab" data-bs-target="#conversion" type="button" role="tab" aria-controls="conversion" aria-selected="false">转化率排行</button> </li> <li class="nav-item" role="presentation"> <button class="nav-link" id="reward-tab" data-bs-toggle="tab" data-bs-target="#reward" type="button" role="tab" aria-controls="reward" aria-selected="false">奖励金额排行</button> </li> </ul> <div class="tab-content" id="rankingTabsContent"> <!-- 邀请数量排行选项卡 --> <div class="tab-pane fade show active" id="invites" role="tabpanel" aria-labelledby="invites-tab"> <div class="tab-content pt-3"> <div class="rank-item"> <div class="rank-number rank-1">1</div> <img src="images/avatar1.jpg" alt="分销商头像" class="rank-avatar"> <div class="rank-info"> <div class="rank-name">
王地主 <span class="rank-badge badge-level1">一级</span> </div> <div class="rank-role">浙江区域 | 加入时间：2023-01-15</div> <div class="rank-stats"> <div class="rank-stat"> <i class="fas fa-user-plus rank-stat-icon"></i> <span>邀请人数：32人</span> </div> <div class="rank-stat"> <i class="fas fa-percentage rank-stat-icon"></i> <span>转化率：68%</span> </div> </div> </div> <div class="rank-value"> <div class="rank-score">32</div> <div class="rank-label">邀请人数</div> </div> </div> <div class="rank-item"> <div class="rank-number rank-2">2</div> <img src="images/avatar2.jpg" alt="分销商头像" class="rank-avatar"> <div class="rank-info"> <div class="rank-name">
李地主 <span class="rank-badge badge-level1">一级</span> </div> <div class="rank-role">江苏区域 | 加入时间：2023-02-20</div> <div class="rank-stats"> <div class="rank-stat"> <i class="fas fa-user-plus rank-stat-icon"></i> <span>邀请人数：28人</span> </div> <div class="rank-stat"> <i class="fas fa-percentage rank-stat-icon"></i> <span>转化率：72%</span> </div> </div> </div> <div class="rank-value"> <div class="rank-score">28</div> <div class="rank-label">邀请人数</div> </div> </div> <div class="rank-item"> <div class="rank-number rank-3">3</div> <img src="images/avatar3.jpg" alt="分销商头像" class="rank-avatar"> <div class="rank-info"> <div class="rank-name">
张地主 <span class="rank-badge badge-level1">一级</span> </div> <div class="rank-role">安徽区域 | 加入时间：2023-01-25</div> <div class="rank-stats"> <div class="rank-stat"> <i class="fas fa-user-plus rank-stat-icon"></i> <span>邀请人数：25人</span> </div> <div class="rank-stat"> <i class="fas fa-percentage rank-stat-icon"></i> <span>转化率：64%</span> </div> </div> </div> <div class="rank-value"> <div class="rank-score">25</div> <div class="rank-label">邀请人数</div> </div> </div> <div class="rank-item"> <div class="rank-number rank-other">4</div> <img src="images/avatar4.jpg" alt="分销商头像" class="rank-avatar"> <div class="rank-info"> <div class="rank-name">
赵地主 <span class="rank-badge badge-level1">一级</span> </div> <div class="rank-role">山东区域 | 加入时间：2023-03-10</div> <div class="rank-stats"> <div class="rank-stat"> <i class="fas fa-user-plus rank-stat-icon"></i> <span>邀请人数：20人</span> </div> <div class="rank-stat"> <i class="fas fa-percentage rank-stat-icon"></i> <span>转化率：70%</span> </div> </div> </div> <div class="rank-value"> <div class="rank-score">20</div> <div class="rank-label">邀请人数</div> </div> </div> <div class="rank-item"> <div class="rank-number rank-other">5</div> <img src="images/avatar5.jpg" alt="分销商头像" class="rank-avatar"> <div class="rank-info"> <div class="rank-name">
孙地主 <span class="rank-badge badge-level1">一级</span> </div> <div class="rank-role">河南区域 | 加入时间：2023-02-05</div> <div class="rank-stats"> <div class="rank-stat"> <i class="fas fa-user-plus rank-stat-icon"></i> <span>邀请人数：18人</span> </div> <div class="rank-stat"> <i class="fas fa-percentage rank-stat-icon"></i> <span>转化率：66%</span> </div> </div> </div> <div class="rank-value"> <div class="rank-score">18</div> <div class="rank-label">邀请人数</div> </div> </div> <div class="text-center mt-3"> <button class="btn btn-outline-primary">查看完整排行榜</button> </div> </div> </div> <!-- 转化率排行选项卡 --> <div class="tab-pane fade" id="conversion" role="tabpanel" aria-labelledby="conversion-tab"> <div class="tab-content pt-3"> <div class="rank-item"> <div class="rank-number rank-1">1</div> <img src="images/avatar2.jpg" alt="分销商头像" class="rank-avatar"> <div class="rank-info"> <div class="rank-name">
李地主 <span class="rank-badge badge-level1">一级</span> </div> <div class="rank-role">江苏区域 | 加入时间：2023-02-20</div> <div class="rank-stats"> <div class="rank-stat"> <i class="fas fa-user-plus rank-stat-icon"></i> <span>邀请人数：28人</span> </div> <div class="rank-stat"> <i class="fas fa-user-check rank-stat-icon"></i> <span>转化人数：20人</span> </div> </div> </div> <div class="rank-value"> <div class="rank-score">72%</div> <div class="rank-label">转化率</div> </div> </div> <div class="rank-item"> <div class="rank-number rank-2">2</div> <img src="images/avatar4.jpg" alt="分销商头像" class="rank-avatar"> <div class="rank-info"> <div class="rank-name">
赵地主 <span class="rank-badge badge-level1">一级</span> </div> <div class="rank-role">山东区域 | 加入时间：2023-03-10</div> <div class="rank-stats"> <div class="rank-stat"> <i class="fas fa-user-plus rank-stat-icon"></i> <span>邀请人数：20人</span> </div> <div class="rank-stat"> <i class="fas fa-user-check rank-stat-icon"></i> <span>转化人数：14人</span> </div> </div> </div> <div class="rank-value"> <div class="rank-score">70%</div> <div class="rank-label">转化率</div> </div> </div> <div class="rank-item"> <div class="rank-number rank-3">3</div> <img src="images/avatar1.jpg" alt="分销商头像" class="rank-avatar"> <div class="rank-info"> <div class="rank-name">
王地主 <span class="rank-badge badge-level1">一级</span> </div> <div class="rank-role">浙江区域 | 加入时间：2023-01-15</div> <div class="rank-stats"> <div class="rank-stat"> <i class="fas fa-user-plus rank-stat-icon"></i> <span>邀请人数：32人</span> </div> <div class="rank-stat"> <i class="fas fa-user-check rank-stat-icon"></i> <span>转化人数：22人</span> </div> </div> </div> <div class="rank-value"> <div class="rank-score">68%</div> <div class="rank-label">转化率</div> </div> </div> <div class="text-center mt-3"> <button class="btn btn-outline-primary">查看完整排行榜</button> </div> </div> </div> <!-- 奖励金额排行选项卡 --> <div class="tab-pane fade" id="reward" role="tabpanel" aria-labelledby="reward-tab"> <div class="tab-content pt-3"> <div class="rank-item"> <div class="rank-number rank-1">1</div> <img src="images/avatar1.jpg" alt="分销商头像" class="rank-avatar"> <div class="rank-info"> <div class="rank-name">
王地主 <span class="rank-badge badge-level1">一级</span> </div> <div class="rank-role">浙江区域 | 加入时间：2023-01-15</div> <div class="rank-stats"> <div class="rank-stat"> <i class="fas fa-user-plus rank-stat-icon"></i> <span>邀请人数：32人</span> </div> <div class="rank-stat"> <i class="fas fa-user-check rank-stat-icon"></i> <span>转化人数：22人</span> </div> </div> </div> <div class="rank-value"> <div class="rank-score">6,800元</div> <div class="rank-label">奖励金额</div> </div> </div> <div class="rank-item"> <div class="rank-number rank-2">2</div> <img src="images/avatar2.jpg" alt="分销商头像" class="rank-avatar"> <div class="rank-info"> <div class="rank-name">
李地主 <span class="rank-badge badge-level1">一级</span> </div> <div class="rank-role">江苏区域 | 加入时间：2023-02-20</div> <div class="rank-stats"> <div class="rank-stat"> <i class="fas fa-user-plus rank-stat-icon"></i> <span>邀请人数：28人</span> </div> <div class="rank-stat"> <i class="fas fa-user-check rank-stat-icon"></i> <span>转化人数：20人</span> </div> </div> </div> <div class="rank-value"> <div class="rank-score">5,600元</div> <div class="rank-label">奖励金额</div> </div> </div> <div class="rank-item"> <div class="rank-number rank-3">3</div> <img src="images/avatar3.jpg" alt="分销商头像" class="rank-avatar"> <div class="rank-info"> <div class="rank-name">
张地主 <span class="rank-badge badge-level1">一级</span> </div> <div class="rank-role">安徽区域 | 加入时间：2023-01-25</div> <div class="rank-stats"> <div class="rank-stat"> <i class="fas fa-user-plus rank-stat-icon"></i> <span>邀请人数：25人</span> </div> <div class="rank-stat"> <i class="fas fa-user-check rank-stat-icon"></i> <span>转化人数：16人</span> </div> </div> </div> <div class="rank-value"> <div class="rank-score">4,800元</div> <div class="rank-label">奖励金额</div> </div> </div> <div class="text-center mt-3"> <button class="btn btn-outline-primary">查看完整排行榜</button> </div> </div> </div> </div> </div> <!-- 邀请激励机制 --> <div class="ranking-card"> <h4 class="mb-4">邀请激励机制</h4> <div class="row"> <div class="col-md-6 mb-4"> <div class="reward-card"> <div class="reward-header"> <div class="reward-icon"> <i class="fas fa-gift"></i> </div> <div> <div class="reward-title">邀请新人奖励</div> <div class="reward-subtitle">每邀请一位新分销商加入平台</div> </div> </div> <div class="reward-body"> <div class="reward-description">
成功邀请新的分销商加入平台，并完成实名认证后，您将获得直接现金奖励。奖励金额根据被邀请人的级别而定。
</div> <div class="reward-condition"> <div class="reward-condition-title"> <i class="fas fa-user-tie reward-condition-icon"></i> <span>邀请一级分销商（地主）</span> </div> <div class="reward-condition-text">奖励金额：200元/人</div> </div> <div class="reward-condition"> <div class="reward-condition-title"> <i class="fas fa-user-friends reward-condition-icon"></i> <span>邀请二级分销商（村民）</span> </div> <div class="reward-condition-text">奖励金额：50元/人</div> </div> </div> <div class="reward-footer"> <button class="btn btn-success">立即邀请</button> </div> </div> </div> <div class="col-md-6 mb-4"> <div class="reward-card"> <div class="reward-header"> <div class="reward-icon"> <i class="fas fa-chart-line"></i> </div> <div> <div class="reward-title">邀请转化奖励</div> <div class="reward-subtitle">被邀请人完成首单交易</div> </div> </div> <div class="reward-body"> <div class="reward-description">
当您邀请的分销商完成首单交易后，您将获得额外的转化奖励。奖励金额为首单金额的一定比例。
</div> <div class="reward-condition"> <div class="reward-condition-title"> <i class="fas fa-user-tie reward-condition-icon"></i> <span>一级分销商首单转化</span> </div> <div class="reward-condition-text">奖励金额：首单金额的10%</div> </div> <div class="reward-condition"> <div class="reward-condition-title"> <i class="fas fa-user-friends reward-condition-icon"></i> <span>二级分销商首单转化</span> </div> <div class="reward-condition-text">奖励金额：首单金额的5%</div> </div> </div> <div class="reward-footer"> <button class="btn btn-success">查看转化记录</button> </div> </div> </div> <div class="col-md-6 mb-4"> <div class="reward-card"> <div class="reward-header"> <div class="reward-icon"> <i class="fas fa-trophy"></i> </div> <div> <div class="reward-title">邀请排行奖励</div> <div class="reward-subtitle">每月邀请数量排行榜奖励</div> </div> </div> <div class="reward-body"> <div class="reward-description">
每月根据邀请数量进行排名，排名靠前的分销商将获得额外的现金奖励。
</div> <div class="reward-condition"> <div class="reward-condition-title"> <i class="fas fa-medal reward-condition-icon" style="color: var(--gold-color);"></i> <span>第一名</span> </div> <div class="reward-condition-text">奖励金额：3,000元</div> </div> <div class="reward-condition"> <div class="reward-condition-title"> <i class="fas fa-medal reward-condition-icon" style="color: var(--silver-color);"></i> <span>第二名</span> </div> <div class="reward-condition-text">奖励金额：2,000元</div> </div> <div class="reward-condition"> <div class="reward-condition-title"> <i class="fas fa-medal reward-condition-icon" style="color: var(--bronze-color);"></i> <span>第三名</span> </div> <div class="reward-condition-text">奖励金额：1,000元</div> </div> </div> <div class="reward-footer"> <button class="btn btn-success">查看历史获奖记录</button> </div> </div> </div> <div class="col-md-6 mb-4"> <div class="reward-card"> <div class="reward-header"> <div class="reward-icon"> <i class="fas fa-award"></i> </div> <div> <div class="reward-title">邀请里程碑奖励</div> <div class="reward-subtitle">邀请人数达到特定里程碑</div> </div> </div> <div class="reward-body"> <div class="reward-description">
当您的邀请人数达到特定里程碑时，将获得额外的里程碑奖励。这些奖励可以叠加获得。
</div> <div class="reward-condition"> <div class="reward-condition-title"> <i class="fas fa-flag-checkered reward-condition-icon"></i> <span>邀请人数达到10人</span> </div> <div class="reward-condition-text">奖励金额：500元</div> </div> <div class="reward-condition"> <div class="reward-condition-title"> <i class="fas fa-flag-checkered reward-condition-icon"></i> <span>邀请人数达到30人</span> </div> <div class="reward-condition-text">奖励金额：2,000元</div> </div> <div class="reward-condition"> <div class="reward-condition-title"> <i class="fas fa-flag-checkered reward-condition-icon"></i> <span>邀请人数达到50人</span> </div> <div class="reward-condition-text">奖励金额：5,000元</div> </div> </div> <div class="progress-container mt-3"> <div class="progress-bar progress-level1" style="width: 64%;"></div> </div> <div class="progress-text"> <span>当前邀请人数：32人</span> <span>下一里程碑：50人</span> </div> <div class="reward-footer mt-3"> <button class="btn btn-success">查看我的里程碑</button> </div> </div> </div> </div> </div> </div> <!-- 右侧内容 --> <div class="col-md-4"> <!-- 我的邀请数据 --> <div class="ranking-card"> <h4 class="mb-4">我的邀请数据</h4> <div class="chart-container"> <canvas id="invitationChart"></canvas> </div> <div class="d-flex justify-content-between mt-3"> <div> <div class="stat-value">32</div> <div class="stat-label">总邀请人数</div> </div> <div> <div class="stat-value">22</div> <div class="stat-label">转化人数</div> </div> <div> <div class="stat-value">68%</div> <div class="stat-label">转化率</div> </div> </div> </div> <!-- 邀请规则 --> <div class="ranking-card"> <h4 class="mb-4">邀请规则</h4> <div class="rule-card"> <div class="rule-header"> <div class="rule-title"> <i class="fas fa-book rule-icon"></i> <span>基本规则</span> </div> </div> <div class="rule-body"> <div class="rule-item"> <div class="rule-number">1</div> <div class="rule-content"> <div class="rule-text">每个分销商都有唯一的邀请码，可用于邀请新的分销商加入。</div> <div class="rule-note">邀请码可以在个人中心查看和复制。</div> </div> </div> <div class="rule-item"> <div class="rule-number">2</div> <div class="rule-content"> <div class="rule-text">一级分销商可以邀请一级和二级分销商，二级分销商只能邀请二级分销商。</div> <div class="rule-note">不同级别的分销商有不同的邀请权限。</div> </div> </div> <div class="rule-item"> <div class="rule-number">3</div> <div class="rule-content"> <div class="rule-text">被邀请人需要完成实名认证才能计入邀请成功。</div> <div class="rule-note">实名认证包括手机号验证和身份信息验证。</div> </div> </div> <div class="rule-item"> <div class="rule-number">4</div> <div class="rule-content"> <div class="rule-text">邀请奖励将在被邀请人完成实名认证后自动发放。</div> <div class="rule-note">奖励将直接计入您的佣金账户，可与其他佣金一起提现。</div> </div> </div> <div class="rule-item"> <div class="rule-number">5</div> <div class="rule-content"> <div class="rule-text">严禁虚假邀请、刷单等作弊行为。</div> <div class="rule-note">如发现作弊行为，平台将冻结账户并取消相关奖励。</div> </div> </div> </div> </div> </div> <!-- 邀请方式 --> <div class="ranking-card"> <h4 class="mb-4">邀请方式</h4> <div class="mb-4"> <div class="mb-3"> <label class="form-label">您的专属邀请码</label> <div class="input-group"> <input type="text" class="form-control" value="INVITE123456" readonly> <button class="btn btn-outline-success" id="copyInviteCode"><i class="fas fa-copy"></i></button> </div> </div> <div class="mb-3"> <label class="form-label">邀请链接</label> <div class="input-group"> <input type="text" class="form-control" value="https://smart-farm.com/register?invite=INVITE123456" readonly> <button class="btn btn-outline-success" id="copyInviteLink"><i class="fas fa-copy"></i></button> </div> </div> <div class="mb-3"> <label class="form-label">分享到：</label> <div class="d-flex gap-2"> <button class="btn btn-outline-success"><i class="fab fa-weixin me-1"></i>微信</button> <button class="btn btn-outline-success"><i class="fab fa-weibo me-1"></i>微博</button> <button class="btn btn-outline-success"><i class="fas fa-comment me-1"></i>短信</button> <button class="btn btn-outline-success"><i class="fas fa-qrcode me-1"></i>二维码</button> </div> </div> </div> <div class="text-center"> <button class="btn btn-success"><i class="fas fa-user-plus me-1"></i>生成邀请海报</button> </div> </div> <!-- 常见问题 --> <div class="ranking-card"> <h4 class="mb-4">常见问题</h4> <div class="faq-item"> <div class="faq-question" onclick="toggleFaq(this)"> <span>如何邀请新的分销商？</span> <i class="fas fa-chevron-down"></i> </div> <div class="faq-answer">
您可以通过分享您的邀请码或邀请链接来邀请新的分销商。被邀请人需要在注册时输入您的邀请码，或直接点击您的邀请链接进行注册。您还可以生成邀请海报，分享到各个社交平台。
</div> </div> <div class="faq-item"> <div class="faq-question" onclick="toggleFaq(this)"> <span>邀请奖励什么时候发放？</span> <i class="fas fa-chevron-down"></i> </div> <div class="faq-answer">
邀请新人奖励将在被邀请人完成实名认证后自动发放。转化奖励将在被邀请人完成首单交易后发放。排行榜奖励将在每月初根据上月排名发放。里程碑奖励将在达到相应里程碑时自动发放。
</div> </div> <div class="faq-item"> <div class="faq-question" onclick="toggleFaq(this)"> <span>如何提高邀请转化率？</span> <i class="fas fa-chevron-down"></i> </div> <div class="faq-answer">
提高邀请转化率的关键是选择正确的目标群体和有效的沟通。建议您选择对农业有兴趣或有销售渠道的人群，并清晰地介绍平台的产品优势和分销收益。使用海报、视频等多种形式展示成功案例，增强说服力。定期跟进被邀请人，解决其疑问和障碍。
</div> </div> <div class="faq-item"> <div class="faq-question" onclick="toggleFaq(this)"> <span>邀请奖励有上限吗？</span> <i class="fas fa-chevron-down"></i> </div> <div class="faq-answer">
邀请新人奖励和转化奖励没有数量上限，您可以邀请任意数量的新分销商。排行榜奖励每月只有前三名可以获得。里程碑奖励每个里程碑只能获得一次，但不同里程碑的奖励可以叠加获得。所有奖励将计入您的佣金账户，可与其他佣金一起提现。
</div> </div> </div> </div> </div> </div> <!-- 页脚 -->
<div id="footer-container"></div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="js/login-config.js"></script>
<script src="js/components.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script> <script>
// 使用 common.js 加载导航栏
// 初始化邀请数据图表
const invitationChartCtx = document.getElementById('invitationChart').getContext('2d');
const invitationChart = new Chart(invitationChartCtx, {
type: 'line',
data: {
labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
datasets: [
{
label: '邀请人数',
data: [3, 5, 8, 12, 18, 32],
borderColor: 'rgba(52, 152, 219, 1)',
backgroundColor: 'rgba(52, 152, 219, 0.1)',
tension: 0.4,
fill: true
},
{
label: '转化人数',
data: [2, 3, 5, 8, 12, 22],
borderColor: 'rgba(46, 204, 113, 1)',
backgroundColor: 'rgba(46, 204, 113, 0.1)',
tension: 0.4,
fill: true
}
]
},
options: {
responsive: true,
maintainAspectRatio: false,
plugins: {
legend: {
position: 'top',
},
tooltip: {
mode: 'index',
intersect: false
}
},
scales: {
y: {
beginAtZero: true,
ticks: {
precision: 0
}
}
}
}
});
// 切换FAQ显示/隐藏
function toggleFaq(element) {
const answer = element.nextElementSibling;
const icon = element.querySelector('i');
if (answer.classList.contains('active')) {
answer.classList.remove('active');
icon.classList.remove('fa-chevron-up');
icon.classList.add('fa-chevron-down');
} else {
answer.classList.add('active');
icon.classList.remove('fa-chevron-down');
icon.classList.add('fa-chevron-up');
}
}
// 筛选按钮点击事件
document.querySelectorAll('.filter-btn').forEach(btn => {
btn.addEventListener('click', function() {
document.querySelectorAll('.filter-btn').forEach(b => {
b.classList.remove('active');
});
this.classList.add('active');
});
});
// 复制邀请码按钮点击事件
document.getElementById('copyInviteCode').addEventListener('click', function() {
const inviteCode = this.previousElementSibling.value;
navigator.clipboard.writeText(inviteCode).then(() => {
alert('邀请码已复制到剪贴板！');
});
});
// 复制邀请链接按钮点击事件
document.getElementById('copyInviteLink').addEventListener('click', function() {
const inviteLink = this.previousElementSibling.value;
navigator.clipboard.writeText(inviteLink).then(() => {
alert('邀请链接已复制到剪贴板！');
});
});
// 分享按钮点击事件
document.querySelectorAll('.btn-outline-success').forEach(btn => {
if (btn.querySelector('.fab') || btn.querySelector('.fa-qrcode')) {
btn.addEventListener('click', function() {
const platform = this.textContent.trim();
alert(`正在分享到${platform}，请稍后...`);
});
}
});
// 生成邀请海报按钮点击事件
document.querySelector('.btn-success').addEventListener('click', function() {
alert('正在生成邀请海报，请稍后...');
});
</script> </body> </html>