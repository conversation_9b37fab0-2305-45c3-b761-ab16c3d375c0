<!DOCTYPE html> <html lang="zh-CN"> <head> <meta charset="UTF-8"> <meta name="viewport" content="width=device-width, initial-scale=1.0"> <title>灌溉控制 - 智慧农业平台</title> <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet"> <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet"> <link rel="stylesheet" href="css/common.css"> <link rel="stylesheet" href="css/navbar.css"> <script src="https://cdn.jsdelivr.net/npm/chart.js"></script> <style>
:root {
--primary-color: #28a745;
--secondary-color: #6c757d;
--light-bg: #f8f9fa;
--card-shadow: 0 4px 6px rgba(0,0,0,0.1);
--transition: all 0.3s ease;
}
body {
background-color: var(--light-bg);
font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}
.irrigation-header {
background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80');
background-size: cover;
background-position: center;
height: 200px;
color: white;
display: flex;
align-items: center;
margin-bottom: 30px;
border-radius: 10px;
}
.irrigation-card {
background: white;
padding: 20px;
border-radius: 10px;
box-shadow: var(--card-shadow);
margin-bottom: 30px;
min-height: auto;
}
.stat-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 20px;
transition: var(--transition);
}
.stat-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.stat-icon {
font-size: 2rem;
color: var(--primary-color);
margin-bottom: 10px;
}
.stat-value {
font-size: 1.5rem;
font-weight: bold;
color: var(--primary-color);
margin-bottom: 5px;
}
.stat-label {
font-size: 0.9rem;
color: var(--secondary-color);
}
.chart-container {
position: relative;
height: 300px;
width: 100%;
}
.map-container {
height: 400px;
border-radius: 10px;
overflow: hidden;
margin-bottom: 20px;
}
#map {
min-height: auto;
width: 100%;
}
.filter-btn {
padding: 5px 15px;
border-radius: 20px;
background-color: white;
border: 1px solid #ddd;
margin-right: 10px;
margin-bottom: 10px;
cursor: pointer;
transition: var(--transition);
}
.filter-btn.active {
background-color: var(--primary-color);
color: white;
border-color: var(--primary-color);
}
.filter-btn:hover:not(.active) {
background-color: #f0f0f0;
}
.valve-card {
display: flex;
align-items: center;
padding: 15px;
border-radius: 10px;
background: var(--light-bg);
margin-bottom: 15px;
transition: var(--transition);
}
.valve-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.valve-icon {
width: 50px;
height: 50px;
background-color: white;
border-radius: 50%;
display: flex;
align-items: center;
justify-content: center;
margin-right: 15px;
color: var(--primary-color);
font-size: 1.5rem;
}
.valve-info {
flex: 1;
}
.valve-name {
font-weight: bold;
margin-bottom: 5px;
}
.valve-status {
font-size: 0.9rem;
color: var(--secondary-color);
}
.valve-status.on {
color: var(--primary-color);
}
.valve-status.off {
color: #dc3545;
}
.valve-actions {
margin-left: 15px;
}
.toggle-switch {
position: relative;
display: inline-block;
width: 60px;
height: 34px;
}
.toggle-switch input {
opacity: 0;
width: 0;
height: 0;
}
.toggle-slider {
position: absolute;
cursor: pointer;
top: 0;
left: 0;
right: 0;
bottom: 0;
background-color: #ccc;
transition: .4s;
border-radius: 34px;
}
.toggle-slider:before {
position: absolute;
content: "";
height: 26px;
width: 26px;
left: 4px;
bottom: 4px;
background-color: white;
transition: .4s;
border-radius: 50%;
}
input:checked + .toggle-slider {
background-color: var(--primary-color);
}
input:focus + .toggle-slider {
box-shadow: 0 0 1px var(--primary-color);
}
input:checked + .toggle-slider:before {
transform: translateX(26px);
}
.irrigation-data-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 15px;
transition: var(--transition);
}
.irrigation-data-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.irrigation-data-title {
font-weight: bold;
margin-bottom: 10px;
display: flex;
align-items: center;
}
.irrigation-data-icon {
margin-right: 10px;
color: var(--primary-color);
}
.irrigation-data-value {
font-size: 1.5rem;
font-weight: bold;
margin-bottom: 5px;
}
.irrigation-data-desc {
font-size: 0.9rem;
color: var(--secondary-color);
}
.irrigation-data-status {
display: inline-block;
padding: 3px 8px;
border-radius: 15px;
font-size: 0.8rem;
margin-left: 10px;
}
.status-normal {
background-color: rgba(40, 167, 69, 0.2);
color: var(--primary-color);
}
.status-warning {
background-color: rgba(255, 193, 7, 0.2);
color: #ffc107;
}
.status-danger {
background-color: rgba(220, 53, 69, 0.2);
color: #dc3545;
}
.recommendation-card {
background-color: rgba(40, 167, 69, 0.1);
border-left: 4px solid var(--primary-color);
padding: 15px;
margin-bottom: 15px;
border-radius: 0 5px 5px 0;
}
.recommendation-title {
font-weight: bold;
margin-bottom: 10px;
display: flex;
align-items: center;
}
.recommendation-icon {
color: var(--primary-color);
margin-right: 10px;
}
.recommendation-content {
color: var(--secondary-color);
}
.alert-item {
padding: 10px 15px;
border-radius: 5px;
margin-bottom: 10px;
display: flex;
align-items: center;
}
.alert-icon {
margin-right: 15px;
font-size: 1.2rem;
}
.alert-content {
flex: 1;
}
.alert-title {
font-weight: bold;
margin-bottom: 5px;
}
.alert-desc {
font-size: 0.9rem;
}
.alert-warning {
background-color: rgba(255, 193, 7, 0.2);
border-left: 4px solid #ffc107;
}
.alert-danger {
background-color: rgba(220, 53, 69, 0.2);
border-left: 4px solid #dc3545;
}
.alert-info {
background-color: rgba(13, 202, 240, 0.2);
border-left: 4px solid #0dcaf0;
}
.history-item {
display: flex;
margin-bottom: 15px;
padding-bottom: 15px;
border-bottom: 1px solid #eee;
}
.history-icon {
width: 40px;
height: 40px;
background-color: var(--light-bg);
border-radius: 50%;
display: flex;
align-items: center;
justify-content: center;
margin-right: 15px;
color: var(--primary-color);
}
.history-content {
flex: 1;
}
.history-title {
font-weight: bold;
margin-bottom: 5px;
}
.history-time {
font-size: 0.9rem;
color: var(--secondary-color);
margin-bottom: 5px;
}
.history-desc {
font-size: 0.9rem;
}
.schedule-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 15px;
transition: var(--transition);
}
.schedule-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.schedule-title {
font-weight: bold;
margin-bottom: 10px;
display: flex;
align-items: center;
justify-content: space-between;
}
.schedule-icon {
color: var(--primary-color);
margin-right: 10px;
}
.schedule-time {
font-size: 0.9rem;
color: var(--secondary-color);
margin-bottom: 10px;
}
.schedule-desc {
margin-bottom: 15px;
}
.progress-container {
height: 10px;
background-color: #e9ecef;
border-radius: 5px;
margin-bottom: 10px;
overflow: hidden;
}
.progress-bar {
min-height: auto;
background-color: var(--primary-color);
border-radius: 5px;
}
.progress-text {
font-size: 0.9rem;
color: var(--secondary-color);
display: flex;
justify-content: space-between;
}
</style> </head> <body> <!-- 导航栏 -->
<div id="navbar-container"></div>
<!-- 主要内容 --> <div class="container py-5"> <div class="irrigation-header"> <div class="container"> <h1>灌溉控制</h1> <p class="lead">智能控制农田灌溉系统，根据土壤湿度和天气情况自动调节灌溉量，提高水资源利用效率</p> </div> </div> <!-- 顶部统计卡片 --> <div class="row mb-4"> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-tint stat-icon"></i> <div class="stat-value">35%</div> <div class="stat-label">平均土壤湿度</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-faucet stat-icon"></i> <div class="stat-value">3</div> <div class="stat-label">活动电磁阀</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-water stat-icon"></i> <div class="stat-value">120 m³</div> <div class="stat-label">今日灵溪量</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-calendar-alt stat-icon"></i> <div class="stat-value">2</div> <div class="stat-label">待执行灵溪计划</div> </div> </div> </div> <!-- 主要内容区域 --> <div class="row"> <!-- 左侧内容 --> <div class="col-md-8"> <!-- 灵溪系统地图 --> <div class="irrigation-card"> <div class="d-flex justify-content-between align-items-center mb-4"> <h4>灵溪系统地图</h4> <div> <button class="filter-btn active">全部</button> <button class="filter-btn">电磁阀</button> <button class="filter-btn">水泵</button> <button class="filter-btn">传感器</button> </div> </div> <div class="map-container"> <div id="map"></div> </div> </div> <!-- 灵溪数据图表 --> <div class="irrigation-card"> <div class="d-flex justify-content-between align-items-center mb-4"> <h4>灵溪数据趋势</h4> <div> <button class="btn btn-sm btn-outline-success me-2">日</button> <button class="btn btn-sm btn-success me-2">周</button> <button class="btn btn-sm btn-outline-success">月</button> </div> </div> <div class="chart-container"> <canvas id="irrigationChart"></canvas> </div> </div> <!-- 电磁阀控制 --> <div class="irrigation-card"> <div class="d-flex justify-content-between align-items-center mb-4"> <h4>电磁阀控制</h4> <a href="#" class="btn btn-sm btn-outline-success">查看全部</a> </div> <div class="valve-card"> <div class="valve-icon"> <i class="fas fa-faucet"></i> </div> <div class="valve-info"> <div class="valve-name">电磁阀 #001 - A区块</div> <div class="valve-status on">开启中 - 流量: 2.5 m³/h - 已运行: 30分钟</div> </div> <div class="valve-actions"> <label class="toggle-switch"> <input type="checkbox" checked> <span class="toggle-slider"></span> </label> </div> </div> <div class="valve-card"> <div class="valve-icon"> <i class="fas fa-faucet"></i> </div> <div class="valve-info"> <div class="valve-name">电磁阀 #002 - B区块</div> <div class="valve-status on">开启中 - 流量: 2.0 m³/h - 已运行: 15分钟</div> </div> <div class="valve-actions"> <label class="toggle-switch"> <input type="checkbox" checked> <span class="toggle-slider"></span> </label> </div> </div> <div class="valve-card"> <div class="valve-icon"> <i class="fas fa-faucet"></i> </div> <div class="valve-info"> <div class="valve-name">电磁阀 #003 - C区块</div> <div class="valve-status on">开启中 - 流量: 1.8 m³/h - 已运行: 10分钟</div> </div> <div class="valve-actions"> <label class="toggle-switch"> <input type="checkbox" checked> <span class="toggle-slider"></span> </label> </div> </div> <div class="valve-card"> <div class="valve-icon"> <i class="fas fa-faucet"></i> </div> <div class="valve-info"> <div class="valve-name">电磁阀 #004 - D区块</div> <div class="valve-status off">已关闭 - 上次运行: 2小时前</div> </div> <div class="valve-actions"> <label class="toggle-switch"> <input type="checkbox"> <span class="toggle-slider"></span> </label> </div> </div> <div class="valve-card"> <div class="valve-icon"> <i class="fas fa-faucet"></i> </div> <div class="valve-info"> <div class="valve-name">电磁阀 #005 - E区块</div> <div class="valve-status off">已关闭 - 上次运行: 5小时前</div> </div> <div class="valve-actions"> <label class="toggle-switch"> <input type="checkbox"> <span class="toggle-slider"></span> </label> </div> </div> </div> </div> <!-- 右侧内容 --> <div class="col-md-4"> <!-- 当前灵溪数据 --> <div class="irrigation-card"> <h4 class="mb-4">当前灵溪数据</h4> <div class="irrigation-data-card"> <div class="irrigation-data-title"> <i class="fas fa-tint irrigation-data-icon"></i> <span>土壤湿度</span> <span class="irrigation-data-status status-warning">偏低</span> </div> <div class="irrigation-data-value">35%</div> <div class="irrigation-data-desc">适宜范围：40% - 60%</div> </div> <div class="irrigation-data-card"> <div class="irrigation-data-title"> <i class="fas fa-thermometer-half irrigation-data-icon"></i> <span>土壤温度</span> <span class="irrigation-data-status status-normal">正常</span> </div> <div class="irrigation-data-value">22.5°C</div> <div class="irrigation-data-desc">适宜范围：18°C - 25°C</div> </div> <div class="irrigation-data-card"> <div class="irrigation-data-title"> <i class="fas fa-water irrigation-data-icon"></i> <span>灵溪流量</span> <span class="irrigation-data-status status-normal">正常</span> </div> <div class="irrigation-data-value">6.3 m³/h</div> <div class="irrigation-data-desc">当前总流量</div> </div> <div class="irrigation-data-card"> <div class="irrigation-data-title"> <i class="fas fa-tachometer-alt irrigation-data-icon"></i> <span>水泵压力</span> <span class="irrigation-data-status status-normal">正常</span> </div> <div class="irrigation-data-value">0.35 MPa</div> <div class="irrigation-data-desc">适宜范围：0.3 - 0.4 MPa</div> </div> </div> <!-- 灵溪计划 --> <div class="irrigation-card"> <h4 class="mb-4">灵溪计划</h4> <div class="schedule-card"> <div class="schedule-title"> <div> <i class="fas fa-calendar-alt schedule-icon"></i> <span>A区块定时灵溪</span> </div> <div class="form-check form-switch"> <input class="form-check-input" type="checkbox" id="scheduleSwitch1" checked> </div> </div> <div class="schedule-time">每天 06:00 - 06:30</div> <div class="schedule-desc">持续时间30分钟，流量2.5 m³/h</div> <div class="progress-container"> <div class="progress-bar" style="width: 100%;"></div> </div> <div class="progress-text"> <span>下次执行：明天 06:00</span> <span>状态：正常</span> </div> </div> <div class="schedule-card"> <div class="schedule-title"> <div> <i class="fas fa-calendar-alt schedule-icon"></i> <span>B区块定时灵溪</span> </div> <div class="form-check form-switch"> <input class="form-check-input" type="checkbox" id="scheduleSwitch2" checked> </div> </div> <div class="schedule-time">每天 18:00 - 18:30</div> <div class="schedule-desc">持续时间30分钟，流量2.0 m³/h</div> <div class="progress-container"> <div class="progress-bar" style="width: 50%;"></div> </div> <div class="progress-text"> <span>下次执行：今天 18:00</span> <span>状态：待执行</span> </div> </div> <div class="schedule-card"> <div class="schedule-title"> <div> <i class="fas fa-calendar-alt schedule-icon"></i> <span>C区块定时灵溪</span> </div> <div class="form-check form-switch"> <input class="form-check-input" type="checkbox" id="scheduleSwitch3" checked> </div> </div> <div class="schedule-time">每周一、三、五 07:00 - 07:30</div> <div class="schedule-desc">持续时间30分钟，流量1.8 m³/h</div> <div class="progress-container"> <div class="progress-bar" style="width: 75%;"></div> </div> <div class="progress-text"> <span>下次执行：后天 07:00</span> <span>状态：待执行</span> </div> </div> <div class="text-center mt-3"> <button class="btn btn-success">添加新计划</button> </div> </div> <!-- 智能建议 --> <div class="irrigation-card"> <h4 class="mb-4">智能建议</h4> <div class="recommendation-card"> <div class="recommendation-title"> <i class="fas fa-lightbulb recommendation-icon"></i> <span>灵溪建议</span> </div> <div class="recommendation-content"> <p>当前土壤湿度偏低，建议在上午进行灵溪，每些电磁阀开启30分钟。</p> <button class="btn btn-sm btn-success mt-2">立即执行</button> </div> </div> <div class="recommendation-card"> <div class="recommendation-title"> <i class="fas fa-lightbulb recommendation-icon"></i> <span>节水建议</span> </div> <div class="recommendation-content"> <p>根据天气预报，未来三天将有降雨，建议调整灵溪计划，减少灵溪量。</p> <button class="btn btn-sm btn-success mt-2">自动调整</button> </div> </div> </div> <!-- 预警信息 --> <div class="irrigation-card"> <h4 class="mb-4">预警信息</h4> <div class="alert-item alert-warning"> <div class="alert-icon"> <i class="fas fa-exclamation-triangle"></i> </div> <div class="alert-content"> <div class="alert-title">土壤湿度偏低</div> <div class="alert-desc">A区块土壤湿度低于35%，建议及时灵溪</div> </div> </div> <div class="alert-item alert-info"> <div class="alert-icon"> <i class="fas fa-info-circle"></i> </div> <div class="alert-content"> <div class="alert-title">天气预报</div> <div class="alert-desc">未来三天将有降雨，可能影响灵溪计划</div> </div> </div> </div> </div> </div> </div> <!-- 页脚 -->
<div id="footer-container"></div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="js/login-config.js"></script>
<script src="js/components.js"></script>
<script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script> <script>
// 使用 common.js 加载导航栏
});
});
// 初始化地图
var map = L.map('map').setView([30.7741, 120.7551], 14);
L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
attribution: '© OpenStreetMap contributors'
}).addTo(map);
// 添加灵溪系统标记
const irrigationSystem = [
{ id: 1, name: "电磁阀 #001", position: [30.7741, 120.7551], type: "valve", status: "on", value: "2.5 m³/h" },
{ id: 2, name: "电磁阀 #002", position: [30.7841, 120.7651], type: "valve", status: "on", value: "2.0 m³/h" },
{ id: 3, name: "电磁阀 #003", position: [30.7641, 120.7451], type: "valve", status: "on", value: "1.8 m³/h" },
{ id: 4, name: "电磁阀 #004", position: [30.7541, 120.7351], type: "valve", status: "off", value: "0.0 m³/h" },
{ id: 5, name: "电磁阀 #005", position: [30.7941, 120.7751], type: "valve", status: "off", value: "0.0 m³/h" },
{ id: 6, name: "水泵 #001", position: [30.7691, 120.7501], type: "pump", status: "on", value: "0.35 MPa" },
{ id: 7, name: "土壤湿度传感器 #001", position: [30.7731, 120.7541], type: "sensor", status: "normal", value: "38%" },
{ id: 8, name: "土壤湿度传感器 #002", position: [30.7831, 120.7641], type: "sensor", status: "normal", value: "42%" },
{ id: 9, name: "土壤湿度传感器 #003", position: [30.7631, 120.7441], type: "sensor", status: "normal", value: "40%" },
{ id: 10, name: "土壤湿度传感器 #004", position: [30.7531, 120.7341], type: "sensor", status: "normal", value: "36%" },
{ id: 11, name: "土壤湿度传感器 #005", position: [30.7931, 120.7741], type: "sensor", status: "warning", value: "25%" }
];
irrigationSystem.forEach(item => {
// 设置图标颜色
let color = '#28a745';
if (item.type === 'valve') {
color = item.status === 'on' ? '#28a745' : '#6c757d';
} else if (item.type === 'pump') {
color = item.status === 'on' ? '#0dcaf0' : '#6c757d';
} else if (item.type === 'sensor') {
color = item.status === 'normal' ? '#28a745' : '#ffc107';
}
// 创建标记
const marker = L.marker(item.position)
.bindPopup(`
<div class="text-center"> <h6>${item.name}</h6> <p class="mb-1">类型: ${item.type}</p> <p class="mb-1">状态: ${item.status}</p> <p class="mb-1">当前值: ${item.value}</p> <button class="btn btn-sm btn-success mt-2">查看详情</button> </div>
`)
.addTo(map);
});
// 初始化灵溪数据图表
var ctx = document.getElementById('irrigationChart').getContext('2d');
var irrigationChart = new Chart(ctx, {
type: 'line',
data: {
labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
datasets: [
{
label: '土壤湿度 (%)',
data: [45, 42, 40, 38, 36, 35, 35],
borderColor: '#0dcaf0',
backgroundColor: 'rgba(13, 202, 240, 0.1)',
tension: 0.4,
fill: true
},
{
label: '灵溪量 (m³)',
data: [80, 90, 100, 110, 120, 120, 120],
borderColor: '#28a745',
backgroundColor: 'rgba(40, 167, 69, 0.1)',
tension: 0.4,
fill: true
}
]
},
options: {
responsive: true,
maintainAspectRatio: false,
plugins: {
legend: {
position: 'top',
},
tooltip: {
mode: 'index',
intersect: false
}
},
scales: {
y: {
beginAtZero: false
}
}
}
});
// 筛选按钮点击事件
document.querySelectorAll('.filter-btn').forEach(btn => {
btn.addEventListener('click', function() {
document.querySelectorAll('.filter-btn').forEach(b => {
b.classList.remove('active');
});
this.classList.add('active');
});
});
// 电磁阀开关点击事件
document.querySelectorAll('.toggle-switch input').forEach(toggle => {
toggle.addEventListener('change', function() {
const valveCard = this.closest('.valve-card');
const valveName = valveCard.querySelector('.valve-name').textContent;
const valveStatus = valveCard.querySelector('.valve-status');
if (this.checked) {
valveStatus.classList.remove('off');
valveStatus.classList.add('on');
valveStatus.textContent = '开启中 - 流量: 2.0 m³/h - 已运行: 0分钟';
alert(`已开启${valveName}！`);
} else {
valveStatus.classList.remove('on');
valveStatus.classList.add('off');
valveStatus.textContent = '已关闭 - 上次运行: 刚刚';
alert(`已关闭${valveName}！`);
}
});
});
// 立即执行按钮点击事件
document.querySelector('.recommendation-card .btn-success').addEventListener('click', function() {
alert('已发送灵溪指令，电磁阀将在几秒后开启！');
});
// 添加新计划按钮点击事件
document.querySelector('.irrigation-card .btn-success').addEventListener('click', function() {
alert('请设置新的灵溪计划！');
});
</script>
