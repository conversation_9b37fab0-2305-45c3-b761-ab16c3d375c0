/**
 * 统一的导航栏和页脚组件
 * 使用内联方式，避免CORS问题
 */

// 用户登录状态
let isLoggedIn = false;
let currentUser = null;

// 从localStorage加载登录状态
function loadLoginState() {
    const savedUser = localStorage.getItem('smartAgriUser');
    if (savedUser) {
        currentUser = JSON.parse(savedUser);
        isLoggedIn = true;
        return true;
    }
    return false;
}

// 保存登录状态到localStorage
function saveLoginState(user) {
    localStorage.setItem('smartAgriUser', JSON.stringify(user));
    currentUser = user;
    isLoggedIn = true;
}

// 清除登录状态
function clearLoginState() {
    localStorage.removeItem('smartAgriUser');
    currentUser = null;
    isLoggedIn = false;
}

// 登录模态框HTML
const loginModalHTML = `
<!-- 登录模态框 -->
<div class="modal fade" id="loginModal" tabindex="-1" aria-labelledby="loginModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="loginModalLabel">用户登录</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="loginForm">
                    <div class="mb-3">
                        <label for="username" class="form-label">用户名</label>
                        <input type="text" class="form-control" id="username" placeholder="请输入用户名" required>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">密码</label>
                        <input type="password" class="form-control" id="password" placeholder="请输入密码" required>
                    </div>
                </form>
                <div id="loginMessage" class="alert alert-danger mt-3 d-none"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-success" id="loginSubmitBtn">登录</button>
            </div>
        </div>
    </div>
</div>
`;

// 导航栏HTML代码
const navbarHTML = `
<!-- 导航栏 -->
<nav class="navbar navbar-expand-lg navbar-dark">
    <div class="container">
        <a class="navbar-brand" href="index.html">智慧农业平台</a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav ms-auto">
                <li class="nav-item dropdown module1-nav">
                    <a class="nav-link dropdown-toggle" href="#" id="module1Dropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false" data-page="module1">基地土地招租与订单种植</a>
                    <ul class="dropdown-menu" aria-labelledby="module1Dropdown">
                        <li><a class="dropdown-item" href="land-map.html">动态地图分区浏览</a></li>
                        <li><a class="dropdown-item" href="land-detail.html">土地标签信息展示</a></li>
                        <li><a class="dropdown-item" href="land-publish.html">土地招租信息发布</a></li>
                        <li><a class="dropdown-item" href="land-selection.html">用户在线选地并收藏</a></li>
                        <li><a class="dropdown-item" href="land-contract.html">在线签约流程</a></li>
                        <li><a class="dropdown-item" href="planting-plan.html">自动生成种植计划模板</a></li>
                        <li><a class="dropdown-item" href="planting-dashboard.html">全周期种植看板展示</a></li>
                        <li><a class="dropdown-item" href="land-management.html">土地托管服务申请</a></li>
                        <li><a class="dropdown-item" href="land-recommendation.html">AI地块推荐系统</a></li>
                    </ul>
                </li>
                <li class="nav-item dropdown module2-nav">
                    <a class="nav-link dropdown-toggle" href="#" id="module2Dropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false" data-page="module2">农产品品牌化运营</a>
                    <ul class="dropdown-menu" aria-labelledby="module2Dropdown">
                        <li><a class="dropdown-item" href="brand-story.html">品牌故事展示模块</a></li>
                        <li><a class="dropdown-item" href="product-traceability.html">产品溯源系统</a></li>
                        <li><a class="dropdown-item" href="certification.html">认证系统接入</a></li>
                        <li><a class="dropdown-item" href="brand-customization.html">品牌联名定制功能</a></li>
                        <li><a class="dropdown-item" href="live-streaming.html">直播带货与基地实景直播</a></li>
                    </ul>
                </li>
                <li class="nav-item dropdown module3-nav">
                    <a class="nav-link dropdown-toggle" href="#" id="module3Dropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false" data-page="module3">全过程监控系统</a>
                    <ul class="dropdown-menu" aria-labelledby="module3Dropdown">
                        <li><a class="dropdown-item" href="monitoring-dashboard.html">智能传感数据看板</a></li>
                        <li><a class="dropdown-item" href="alert-system.html">农事预警系统</a></li>
                        <li><a class="dropdown-item" href="crop-recognition.html">作物图像识别系统</a></li>
                        <li><a class="dropdown-item" href="blockchain-verification.html">关键节点区块链存证</a></li>
                        <li><a class="dropdown-item" href="remote-control.html">远程控制农业设备功能</a></li>
                        <li><a class="dropdown-item" href="expert-consultation.html">专家在线问诊</a></li>
                    </ul>
                </li>
                <li class="nav-item dropdown module4-nav">
                    <a class="nav-link dropdown-toggle" href="#" id="module4Dropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false" data-page="module4">社群运营体系</a>
                    <ul class="dropdown-menu" aria-labelledby="module4Dropdown">
                        <li><a class="dropdown-item" href="distribution-system.html">双层分销结构设置</a></li>
                        <li><a class="dropdown-item" href="commission-dashboard.html">分销佣金可视化看板</a></li>
                        <li><a class="dropdown-item" href="promotion-tools.html">分销推广工具</a></li>
                        <li><a class="dropdown-item" href="invitation-ranking.html">邀请排行榜与激励机制</a></li>
                        <li><a class="dropdown-item" href="consumer-conversion.html">消费者转化机制</a></li>
                        <li><a class="dropdown-item" href="team-incentives.html">区域团队激励</a></li>
                    </ul>
                </li>
                <li class="nav-item dropdown module5-nav">
                    <a class="nav-link dropdown-toggle" href="#" id="module5Dropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false" data-page="module5">农资农机交易系统</a>
                    <ul class="dropdown-menu" aria-labelledby="module5Dropdown">
                        <li><a class="dropdown-item" href="equipment-rental.html">智能农机设备租赁系统</a></li>
                        <li><a class="dropdown-item" href="agricultural-supplies.html">农资采购金融服务</a></li>
                        <li><a class="dropdown-item" href="used-equipment.html">二手农机交易市场</a></li>
                        <li><a class="dropdown-item" href="subscription-service.html">农资订阅服务</a></li>
                        <li><a class="dropdown-item" href="ar-preview.html">AR虚拟试用功能</a></li>
                    </ul>
                </li>
                <li class="nav-item dropdown module6-nav">
                    <a class="nav-link dropdown-toggle" href="#" id="module6Dropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false" data-page="module6">运营支撑体系</a>
                    <ul class="dropdown-menu" aria-labelledby="module6Dropdown">
                        <li><a class="dropdown-item" href="digital-farm.html">与政府合作数字农场试点</a></li>
                        <li><a class="dropdown-item" href="referral-program.html">邀请好友赠农资礼包</a></li>
                        <li><a class="dropdown-item" href="kol-incubation.html">新农人KOL孵化计划</a></li>
                        <li><a class="dropdown-item" href="exclusive-cooperation.html">与基地签独家合作协议</a></li>
                        <li><a class="dropdown-item" href="logistics-center.html">接入顺丰/京东冷链物流</a></li>
                    </ul>
                </li>
                <li class="nav-item ms-2 d-flex align-items-center">
                    <button id="loginButton" class="btn btn-outline-light px-3" type="button" data-bs-toggle="modal" data-bs-target="#loginModal" style="min-width: 100px; white-space: nowrap; height: 38px;">
                        <i class="fas fa-user me-1"></i><span id="loginButtonText">登录</span>
                    </button>
                </li>
            </ul>
        </div>
    </div>
</nav>
`;

// 页脚HTML代码
const footerHTML = `
<!-- 页脚 -->
<footer class="bg-dark text-white py-4">
    <div class="container">
        <div class="row">
            <div class="col-md-6">
                <h5>智慧农业数字化平台</h5>
                <p>连接农业产业链，打造数字化农业生态</p>
            </div>
            <div class="col-md-6 text-md-end">
                <p>联系我们：<EMAIL></p>
                <p>服务热线：400-888-8888</p>
            </div>
        </div>
    </div>
</footer>
`;

// 页面名称与导航模块的映射关系
const moduleMapping = {
    'index': 'index',
    '': 'index',
    'land-map': 'module1',
    'land-detail': 'module1',
    'land-publish': 'module1',
    'land-selection': 'module1',
    'land-contract': 'module1',
    'planting-plan': 'module1',
    'planting-dashboard': 'module1',
    'land-management': 'module1',
    'land-recommendation': 'module1',
    'land-rental': 'module1',
    'planting-order': 'module1',
    'order-tracking': 'module1',

    'brand-story': 'module2',
    'product-traceability': 'module2',
    'certification': 'module2',
    'brand-customization': 'module2',
    'live-streaming': 'module2',
    'brand-creation': 'module2',
    'packaging-design': 'module2',
    'marketing-promotion': 'module2',

    'monitoring-dashboard': 'module3',
    'alert-system': 'module3',
    'crop-recognition': 'module3',
    'blockchain-verification': 'module3',
    'remote-control': 'module3',
    'expert-consultation': 'module3',
    'sensor-dashboard': 'module3',
    'farm-alert': 'module3',
    'blockchain-proof': 'module3',

    'distribution-system': 'module4',
    'commission-dashboard': 'module4',
    'promotion-tools': 'module4',
    'invitation-ranking': 'module4',
    'consumer-conversion': 'module4',
    'team-incentives': 'module4',

    'equipment-rental': 'module5',
    'agricultural-supplies': 'module5',
    'used-equipment': 'module5',
    'subscription-service': 'module5',
    'ar-preview': 'module5',

    'digital-farm': 'module6',
    'referral-program': 'module6',
    'kol-incubation': 'module6',
    'exclusive-cooperation': 'module6',
    'logistics-center': 'module6'
};

/**
 * 在页面加载时初始化导航栏和页脚
 */
document.addEventListener('DOMContentLoaded', function() {
    // 确保loginConfig已加载
    if (typeof loginConfig === 'undefined') {
        console.error('loginConfig未定义，请确保已加载js/login-config.js');
        return;
    }

    // 加载导航栏
    const navbarContainer = document.getElementById('navbar-container');
    if (navbarContainer) {
        navbarContainer.innerHTML = navbarHTML;

        // 添加登录模态框到body
        if (!document.getElementById('loginModal')) {
            const modalContainer = document.createElement('div');
            modalContainer.innerHTML = loginModalHTML;
            document.body.appendChild(modalContainer);
        }

        // 加载登录状态
        loadLoginState();

        // 先更新导航栏显示（隐藏不应该显示的模块）
        updateNavbarDisplay();

        // 设置当前页面的导航链接为激活状态
        setActiveNavItem();

        // 绑定登录按钮事件
        setupLoginEvents();

        // 添加全局点击事件监听器，确保在任何点击后都重新检查模块可见性
        document.addEventListener('click', function() {
            setTimeout(updateNavbarDisplay, 10);
        });
    }

    // 加载页脚
    const footerContainer = document.getElementById('footer-container');
    if (footerContainer) {
        footerContainer.innerHTML = footerHTML;
    }
});

/**
 * 设置登录相关事件
 */
function setupLoginEvents() {
    // 登录按钮点击事件
    const loginSubmitBtn = document.getElementById('loginSubmitBtn');
    if (loginSubmitBtn) {
        loginSubmitBtn.addEventListener('click', handleLogin);
    }

    // 更新登录按钮显示
    updateLoginButton();
}

/**
 * 处理登录操作
 */
function handleLogin() {
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;
    const loginMessage = document.getElementById('loginMessage');

    // 简单验证
    if (!username || !password) {
        loginMessage.textContent = '用户名和密码不能为空';
        loginMessage.classList.remove('d-none');
        return;
    }

    // 模拟登录验证 (实际项目中应该调用后端API)
    if (password === '123456') {
        // 登录成功
        const user = {
            username: username,
            displayName: username,
            role: 'user'
        };

        // 保存登录状态
        saveLoginState(user);

        // 更新UI
        updateNavbarDisplay();
        updateLoginButton();

        // 关闭模态框
        const loginModal = bootstrap.Modal.getInstance(document.getElementById('loginModal'));
        loginModal.hide();

        // 清空表单
        document.getElementById('loginForm').reset();
        loginMessage.classList.add('d-none');
    } else {
        // 登录失败
        loginMessage.textContent = '用户名或密码错误';
        loginMessage.classList.remove('d-none');
    }
}

/**
 * 处理登出操作
 */
function handleLogout() {
    // 清除登录状态
    clearLoginState();

    // 更新UI
    updateNavbarDisplay();
    updateLoginButton();

    // 如果当前页面需要登录才能访问，则跳转到首页
    const currentPage = window.location.pathname.split('/').pop().replace('.html', '');
    const currentModule = moduleMapping[currentPage] || '';

    if (currentModule && !loginConfig.guestModules.includes(currentModule)) {
        window.location.href = 'index.html';
    }
}

/**
 * 更新登录按钮显示
 */
function updateLoginButton() {
    const loginButton = document.getElementById('loginButton');
    const loginButtonText = document.getElementById('loginButtonText');

    if (!loginButton || !loginButtonText) return;

    if (isLoggedIn) {
        // 已登录状态
        loginButton.classList.remove('btn-outline-light');
        loginButton.classList.add('btn-light');
        loginButton.removeAttribute('data-bs-toggle');
        loginButton.removeAttribute('data-bs-target');
        loginButton.onclick = handleLogout;
        loginButton.style.minWidth = '120px'; // 设置最小宽度，避免按钮变形
        loginButton.style.whiteSpace = 'nowrap'; // 确保文本不换行
        loginButton.style.height = '38px'; // 保持高度一致
        loginButton.classList.add('px-3'); // 保持内边距一致
        // 创建更紧凑的显示格式
        loginButtonText.innerHTML = `<span>${currentUser.displayName}</span> <small style="opacity:0.8;">(退出)</small>`;
    } else {
        // 未登录状态
        loginButton.classList.remove('btn-light');
        loginButton.classList.add('btn-outline-light');
        loginButton.setAttribute('data-bs-toggle', 'modal');
        loginButton.setAttribute('data-bs-target', '#loginModal');
        loginButton.onclick = null;
        loginButton.style.minWidth = '100px'; // 保持最小宽度一致
        loginButton.style.whiteSpace = 'nowrap'; // 保持文本不换行
        loginButtonText.innerHTML = '登录';
    }
}

/**
 * 更新导航栏显示
 */
function updateNavbarDisplay() {
    // 根据登录状态显示/隐藏导航项
    if (typeof loginConfig !== 'undefined') {
        const modules = isLoggedIn ? loginConfig.userModules : loginConfig.guestModules;

        // 更新模块可见性
        document.querySelectorAll('.module1-nav, .module2-nav, .module3-nav, .module4-nav, .module5-nav, .module6-nav').forEach(nav => {
            nav.style.display = 'none';
        });

        // 显示当前用户可见的模块
        modules.forEach(module => {
            const moduleNav = document.querySelector(`.${module}-nav`);
            if (moduleNav) {
                moduleNav.style.display = '';
            }
        });

        // 添加事件监听器，防止点击菜单项时显示所有模块
        document.querySelectorAll('.dropdown-toggle').forEach(toggle => {
            toggle.addEventListener('click', function(e) {
                // 阻止事件冒泡，确保点击菜单项不会影响其他模块的显示
                e.stopPropagation();

                // 重新应用模块可见性设置
                setTimeout(function() {
                    updateNavbarDisplay();
                }, 10);
            });
        });
    }
}

/**
 * 设置当前页面的导航链接为激活状态
 */
function setActiveNavItem() {
    // 获取当前页面的文件名（不含路径和扩展名）
    const currentPath = window.location.pathname;
    const currentPage = currentPath.split('/').pop().replace('.html', '');

    // 根据当前页面查找对应的模块
    const currentModule = moduleMapping[currentPage] || '';

    // 设置导航栏激活状态
    if (currentModule) {
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            const page = link.getAttribute('data-page');
            if (page === currentModule) {
                link.classList.add('active');
            }
        });
    }

    // 设置二级菜单激活状态
    const menuItems = document.querySelectorAll('.dropdown-item');
    menuItems.forEach(item => {
        if (item.getAttribute('href') === currentPage + '.html') {
            item.classList.add('active');
            // 找到父级下拉菜单并设置激活状态
            const parentDropdown = item.closest('.dropdown').querySelector('.dropdown-toggle');
            if (parentDropdown) {
                parentDropdown.classList.add('active');
            }
        }
    });
}
