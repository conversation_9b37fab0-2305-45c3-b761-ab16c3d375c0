/**
 * 登录配置文件
 * 用于配置未登录和不同用户角色登录后显示的页面
 */

// 所有页面定义
const allPages = {
    module1: [
        { file: 'land-map.html', name: '动态地图分区浏览' },
        { file: 'land-detail.html', name: '土地标签信息展示' },
        { file: 'land-publish.html', name: '土地招租信息发布' },
        { file: 'land-selection.html', name: '用户在线选地并收藏' },
        { file: 'land-contract.html', name: '在线签约流程' },
        { file: 'planting-plan.html', name: '自动生成种植计划模板' },
        { file: 'planting-dashboard.html', name: '全周期种植看板展示' },
        { file: 'land-management.html', name: '土地托管服务申请' },
        { file: 'land-recommendation.html', name: 'AI地块推荐系统' }
    ],
    module2: [
        { file: 'brand-story.html', name: '品牌故事展示模块' },
        { file: 'product-traceability.html', name: '产品溯源系统' },
        { file: 'certification.html', name: '认证系统接入' },
        { file: 'brand-customization.html', name: '品牌联名定制功能' },
        { file: 'live-streaming.html', name: '直播带货与基地实景直播' }
    ],
    module3: [
        { file: 'monitoring-dashboard.html', name: '智能传感数据看板' },
        { file: 'alert-system.html', name: '农事预警系统' },
        { file: 'crop-recognition.html', name: '作物图像识别系统' },
        { file: 'blockchain-verification.html', name: '关键节点区块链存证' },
        { file: 'remote-control.html', name: '远程控制农业设备功能' },
        { file: 'expert-consultation.html', name: '专家在线问诊' }
    ],
    module4: [
        { file: 'distribution-system.html', name: '双层分销结构设置' },
        { file: 'commission-dashboard.html', name: '分销佣金可视化看板' },
        { file: 'promotion-tools.html', name: '分销推广工具' },
        { file: 'invitation-ranking.html', name: '邀请排行榜与激励机制' },
        { file: 'consumer-conversion.html', name: '消费者转化机制' },
        { file: 'team-incentives.html', name: '区域团队激励' }
    ],
    module5: [
        { file: 'equipment-rental.html', name: '智能农机设备租赁系统' },
        { file: 'agricultural-supplies.html', name: '农资采购金融服务' },
        { file: 'used-equipment.html', name: '二手农机交易市场' },
        { file: 'subscription-service.html', name: '农资订阅服务' },
        { file: 'ar-preview.html', name: 'AR虚拟试用功能' }
    ],
    module6: [
        { file: 'digital-farm.html', name: '与政府合作数字农场试点' },
        { file: 'referral-program.html', name: '邀请好友赠农资礼包' },
        { file: 'kol-incubation.html', name: '新农人KOL孵化计划' },
        { file: 'exclusive-cooperation.html', name: '与基地签独家合作协议' },
        { file: 'logistics-center.html', name: '接入顺丰/京东冷链物流' }
    ]
};

// 用户账号配置
const userAccounts = {
    'user': {
        password: '123456',
        role: 'user',
        displayName: '普通用户',
        permissions: {
            module1: ['land-map.html', 'land-detail.html', 'land-selection.html'],
            module2: ['brand-story.html', 'product-traceability.html']
        }
    },
    'admin': {
        password: '123456',
        role: 'admin',
        displayName: '管理员',
        permissions: {
            module1: 'all', // 'all' 表示该模块下的所有页面
            module2: 'all',
            module3: 'all',
            module4: 'all',
            module5: 'all',
            module6: 'all'
        }
    },
    'landlord': {
        password: '123456',
        role: 'landlord',
        displayName: '地主',
        permissions: {
            module3: ['monitoring-dashboard.html', 'alert-system.html', 'crop-recognition.html', 'remote-control.html']
        }
    },
    'farmer': {
        password: '123456',
        role: 'farmer',
        displayName: '农民',
        permissions: {
            module4: ['distribution-system.html', 'commission-dashboard.html', 'promotion-tools.html']
        }
    }
};

// 登录配置
const loginConfig = {
    // 未登录时显示的模块和页面
    guestPermissions: {
        module1: ['land-map.html', 'land-detail.html', 'land-selection.html'],
        module2: ['brand-story.html', 'product-traceability.html']
    },

    // 根据用户角色获取可见模块
    getModulesForUser: function(user) {
        if (!user || !user.role) {
            return Object.keys(this.guestPermissions);
        }

        const account = userAccounts[user.username];
        return account ? Object.keys(account.permissions) : Object.keys(this.guestPermissions);
    },

    // 根据用户角色获取模块下的可见页面
    getPagesForUserModule: function(user, module) {
        if (!user || !user.role) {
            // 未登录用户
            return this.guestPermissions[module] || [];
        }

        const account = userAccounts[user.username];
        if (!account || !account.permissions[module]) {
            return [];
        }

        const modulePermissions = account.permissions[module];
        if (modulePermissions === 'all') {
            // 返回该模块下的所有页面
            return allPages[module] ? allPages[module].map(page => page.file) : [];
        } else if (Array.isArray(modulePermissions)) {
            // 返回指定的页面列表
            return modulePermissions;
        }

        return [];
    },

    // 检查用户是否有权限访问特定页面
    hasPagePermission: function(user, pageFile) {
        // 根据页面文件名确定所属模块
        const module = this.getModuleByPageFile(pageFile);
        if (!module) {
            return false;
        }

        const allowedPages = this.getPagesForUserModule(user, module);
        return allowedPages.includes(pageFile);
    },

    // 根据页面文件名获取所属模块
    getModuleByPageFile: function(pageFile) {
        for (const [module, pages] of Object.entries(allPages)) {
            if (pages.some(page => page.file === pageFile)) {
                return module;
            }
        }
        return null;
    },

    // 获取页面的显示名称
    getPageDisplayName: function(pageFile) {
        for (const [module, pages] of Object.entries(allPages)) {
            const page = pages.find(p => p.file === pageFile);
            if (page) {
                return page.name;
            }
        }
        return pageFile;
    },

    // 验证用户登录
    validateUser: function(username, password) {
        const account = userAccounts[username];
        if (account && account.password === password) {
            return {
                username: username,
                role: account.role,
                displayName: account.displayName,
                permissions: account.permissions
            };
        }
        return null;
    },

    // 获取用户权限摘要
    getUserPermissionsSummary: function(user) {
        if (!user || !user.role) {
            return {
                modules: Object.keys(this.guestPermissions),
                totalPages: Object.values(this.guestPermissions).flat().length
            };
        }

        const modules = this.getModulesForUser(user);
        let totalPages = 0;

        modules.forEach(module => {
            const pages = this.getPagesForUserModule(user, module);
            totalPages += pages.length;
        });

        return {
            modules: modules,
            totalPages: totalPages
        };
    }
};
