/**
 * 登录配置文件
 * 用于配置未登录和不同用户角色登录后显示的页面
 */

// 用户账号配置
const userAccounts = {
    'user': {
        password: '123456',
        role: 'user',
        displayName: '普通用户',
        modules: ['module1', 'module2']
    },
    'admin': {
        password: '123456',
        role: 'admin',
        displayName: '管理员',
        modules: ['module1', 'module2', 'module3', 'module4', 'module5', 'module6']
    },
    'landlord': {
        password: '123456',
        role: 'landlord',
        displayName: '地主',
        modules: ['module3']
    },
    'farmer': {
        password: '123456',
        role: 'farmer',
        displayName: '农民',
        modules: ['module4']
    }
};

// 登录配置
const loginConfig = {
    // 未登录时显示的模块
    guestModules: ['module1', 'module2'],

    // 未登录时显示的页面（按模块分组）
    guestPages: {
        module1: [
            'land-map.html',
            'land-detail.html',
            'land-selection.html'
        ],
        module2: [
            'brand-story.html',
            'product-traceability.html'
        ],
        module3: [],
        module4: [],
        module5: [],
        module6: []
    },

    // 根据用户角色获取可见模块
    getModulesForUser: function(user) {
        if (!user || !user.role) {
            return this.guestModules;
        }

        const account = userAccounts[user.username];
        return account ? account.modules : this.guestModules;
    },

    // 验证用户登录
    validateUser: function(username, password) {
        const account = userAccounts[username];
        if (account && account.password === password) {
            return {
                username: username,
                role: account.role,
                displayName: account.displayName,
                modules: account.modules
            };
        }
        return null;
    }
};
