<!DOCTYPE html> <html lang="zh-CN"> <head> <meta charset="UTF-8"> <meta name="viewport" content="width=device-width, initial-scale=1.0"> <title>新农人KOL孵化计划 - 智慧农业平台</title> <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet"> <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet"> <link rel="stylesheet" href="css/common.css"> <link rel="stylesheet" href="css/navbar.css"> <style>
:root {
--primary-color: #28a745;
--secondary-color: #6c757d;
--light-bg: #f8f9fa;
--card-shadow: 0 4px 6px rgba(0,0,0,0.1);
--transition: all 0.3s ease;
--kol-color: #e74c3c;
}
body {
background-color: var(--light-bg);
font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}
.kol-header {
background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80');
background-size: cover;
background-position: center;
height: 300px;
color: white;
display: flex;
align-items: center;
margin-bottom: 30px;
border-radius: 10px;
}
.kol-card {
background: white;
padding: 20px;
border-radius: 10px;
box-shadow: var(--card-shadow);
margin-bottom: 30px;
min-height: auto;
}
.stat-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 20px;
transition: var(--transition);
}
.stat-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.stat-icon {
font-size: 2rem;
color: var(--kol-color);
margin-bottom: 10px;
}
.stat-value {
font-size: 1.5rem;
font-weight: bold;
color: var(--kol-color);
margin-bottom: 5px;
}
.stat-label {
font-size: 0.9rem;
color: var(--secondary-color);
}
.kol-profile {
background: var(--light-bg);
border-radius: 10px;
overflow: hidden;
margin-bottom: 20px;
transition: var(--transition);
min-height: auto;
border: 1px solid #eee;
position: relative;
}
.kol-profile:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.kol-badge {
position: absolute;
top: 10px;
right: 10px;
background-color: var(--kol-color);
color: white;
padding: 5px 10px;
border-radius: 20px;
font-size: 0.8rem;
z-index: 1;
}
.kol-cover {
width: 100%;
height: 150px;
object-fit: cover;
}
.kol-avatar-container {
position: relative;
margin-top: -50px;
text-align: center;
margin-bottom: 10px;
}
.kol-avatar {
width: 100px;
height: 100px;
border-radius: 50%;
border: 4px solid white;
object-fit: cover;
}
.kol-info {
padding: 0 15px 15px;
text-align: center;
}
.kol-name {
font-weight: bold;
font-size: 1.2rem;
margin-bottom: 5px;
}
.kol-title {
color: var(--kol-color);
font-size: 0.9rem;
margin-bottom: 10px;
}
.kol-desc {
color: var(--secondary-color);
margin-bottom: 15px;
font-size: 0.9rem;
height: 60px;
overflow: hidden;
}
.kol-stats {
display: flex;
justify-content: center;
gap: 15px;
margin-bottom: 15px;
}
.kol-stat {
text-align: center;
}
.kol-stat-value {
font-weight: bold;
color: var(--kol-color);
}
.kol-stat-label {
font-size: 0.8rem;
color: var(--secondary-color);
}
.kol-platforms {
display: flex;
justify-content: center;
gap: 10px;
margin-bottom: 15px;
}
.kol-platform {
width: 30px;
height: 30px;
border-radius: 50%;
display: flex;
align-items: center;
justify-content: center;
color: white;
font-size: 1rem;
}
.platform-douyin {
background-color: #000000;
}
.platform-kuaishou {
background-color: #ff5e00;
}
.platform-bilibili {
background-color: #fb7299;
}
.platform-weibo {
background-color: #e6162d;
}
.platform-xiaohongshu {
background-color: #fe2c55;
}
.kol-actions {
display: flex;
gap: 10px;
}
.step-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 15px;
transition: var(--transition);
position: relative;
}
.step-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.step-header {
display: flex;
align-items: center;
margin-bottom: 15px;
}
.step-number {
width: 40px;
height: 40px;
border-radius: 50%;
background-color: var(--kol-color);
color: white;
display: flex;
align-items: center;
justify-content: center;
font-weight: bold;
margin-right: 15px;
flex-shrink: 0;
}
.step-title {
font-weight: bold;
font-size: 1.1rem;
}
.step-body {
margin-bottom: 15px;
}
.step-description {
color: var(--secondary-color);
margin-bottom: 15px;
}
.step-connector {
position: absolute;
left: 20px;
top: 55px;
width: 2px;
height: calc(100% - 40px);
background-color: var(--kol-color);
z-index: 0;
}
.benefit-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 15px;
transition: var(--transition);
min-height: auto;
}
.benefit-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.benefit-header {
display: flex;
align-items: center;
margin-bottom: 15px;
}
.benefit-icon {
width: 50px;
height: 50px;
border-radius: 10px;
background-color: var(--kol-color);
color: white;
display: flex;
align-items: center;
justify-content: center;
font-size: 1.5rem;
margin-right: 15px;
flex-shrink: 0;
}
.benefit-title {
font-weight: bold;
font-size: 1.1rem;
margin-bottom: 5px;
}
.benefit-subtitle {
font-size: 0.9rem;
color: var(--secondary-color);
}
.benefit-body {
margin-bottom: 15px;
}
.benefit-description {
color: var(--secondary-color);
margin-bottom: 15px;
}
.faq-item {
margin-bottom: 15px;
}
.faq-question {
font-weight: bold;
margin-bottom: 5px;
cursor: pointer;
display: flex;
justify-content: space-between;
align-items: center;
}
.faq-answer {
color: var(--secondary-color);
display: none;
padding: 10px 0;
}
.faq-answer.active {
display: block;
}
.course-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 15px;
transition: var(--transition);
display: flex;
align-items: center;
}
.course-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.course-icon {
width: 60px;
height: 60px;
border-radius: 10px;
background-color: var(--kol-color);
color: white;
display: flex;
align-items: center;
justify-content: center;
font-size: 1.5rem;
margin-right: 15px;
flex-shrink: 0;
}
.course-info {
flex: 1;
}
.course-title {
font-weight: bold;
margin-bottom: 5px;
}
.course-desc {
font-size: 0.9rem;
color: var(--secondary-color);
}
.success-story {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 15px;
transition: var(--transition);
}
.success-story:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.success-header {
display: flex;
align-items: center;
margin-bottom: 10px;
}
.success-avatar {
width: 60px;
height: 60px;
border-radius: 50%;
object-fit: cover;
margin-right: 15px;
}
.success-user {
flex: 1;
}
.success-name {
font-weight: bold;
margin-bottom: 0;
}
.success-title {
font-size: 0.9rem;
color: var(--kol-color);
}
.success-content {
color: var(--secondary-color);
margin-bottom: 10px;
font-size: 0.9rem;
}
.success-stats {
display: flex;
gap: 15px;
font-size: 0.9rem;
}
.success-stat {
display: flex;
align-items: center;
}
.success-stat i {
color: var(--kol-color);
margin-right: 5px;
}
.application-form {
background: var(--light-bg);
padding: 20px;
border-radius: 10px;
}
.timeline {
position: relative;
padding-left: 30px;
margin-bottom: 20px;
}
.timeline-line {
position: absolute;
left: 15px;
top: 0;
bottom: 0;
width: 2px;
background-color: var(--kol-color);
}
.timeline-item {
position: relative;
margin-bottom: 20px;
}
.timeline-dot {
position: absolute;
left: -30px;
top: 0;
width: 20px;
height: 20px;
border-radius: 50%;
background-color: var(--kol-color);
border: 3px solid white;
}
.timeline-date {
font-weight: bold;
margin-bottom: 5px;
color: var(--kol-color);
}
.timeline-content {
background-color: white;
padding: 15px;
border-radius: 10px;
box-shadow: var(--card-shadow);
}
.timeline-title {
font-weight: bold;
margin-bottom: 10px;
}
.timeline-desc {
color: var(--secondary-color);
font-size: 0.9rem;
}
.chart-container {
position: relative;
height: 300px;
width: 100%;
margin-bottom: 20px;
}
.nav-tabs .nav-link {
color: var(--secondary-color);
}
.nav-tabs .nav-link.active {
color: var(--kol-color);
font-weight: bold;
}
.tab-content {
padding: 20px 0;
}
</style> </head> <body> <!-- 导航栏 -->
<div id="navbar-container"></div>
<!-- 主要内容 --> <div class="container py-5"> <div class="kol-header"> <div class="container"> <h1>新农人KOL孵化计划</h1> <p class="lead">培养农业领域的意见领袖，通过专业培训、资源支持和平台赋能，帮助有潜力的农民成为农业知识传播者和行业影响力人物</p> <div class="mt-4"> <button class="btn btn-danger btn-lg me-2"><i class="fas fa-user-plus me-2"></i>立即申请</button> <button class="btn btn-outline-light btn-lg"><i class="fas fa-graduation-cap me-2"></i>了解课程</button> </div> </div> </div> <!-- 顶部统计卡片 --> <div class="row mb-4"> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-users stat-icon"></i> <div class="stat-value">256</div> <div class="stat-label">已培养KOL数量</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-play-circle stat-icon"></i> <div class="stat-value">3,580万</div> <div class="stat-label">内容累计播放量</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-heart stat-icon"></i> <div class="stat-value">1,260万</div> <div class="stat-label">累计获赞数量</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-shopping-cart stat-icon"></i> <div class="stat-value">5,680万</div> <div class="stat-label">带货销售额(元)</div> </div> </div> </div> <!-- 主要内容区域 --> <div class="row"> <!-- 左侧内容 --> <div class="col-md-8"> <!-- 优秀KOL展示 --> <div class="kol-card mb-4"> <h4 class="mb-4">优秀新农人 KOL</h4> <div class="row"> <div class="col-md-4 mb-4"> <div class="kol-profile"> <div class="kol-badge">种植达人</div> <img src="images/kol-cover1.jpg" alt="封面图" class="kol-cover"> <div class="kol-avatar-container"> <img src="images/kol1.jpg" alt="头像" class="kol-avatar"> </div> <div class="kol-info"> <div class="kol-name">王农哥</div> <div class="kol-title">水稻种植专家 | 智慧农业大使</div> <div class="kol-desc">从事水稻种植20年，擅长种植技术、病虫害防治和农业智能化应用，带领当地农民增产增收</div> <div class="kol-stats"> <div class="kol-stat"> <div class="kol-stat-value">128万</div> <div class="kol-stat-label">粉丝</div> </div> <div class="kol-stat"> <div class="kol-stat-value">560万</div> <div class="kol-stat-label">播放量</div> </div> <div class="kol-stat"> <div class="kol-stat-value">86万</div> <div class="kol-stat-label">点赞</div> </div> </div> <div class="kol-platforms"> <div class="kol-platform platform-douyin"><i class="fab fa-tiktok"></i></div> <div class="kol-platform platform-kuaishou"><i class="fas fa-play"></i></div> <div class="kol-platform platform-bilibili"><i class="fas fa-play-circle"></i></div> </div> <div class="kol-actions"> <button class="btn btn-danger w-100"><i class="fas fa-user me-2"></i>查看主页</button> </div> </div> </div> </div> <div class="col-md-4 mb-4"> <div class="kol-profile"> <div class="kol-badge">果园达人</div> <img src="images/kol-cover2.jpg" alt="封面图" class="kol-cover"> <div class="kol-avatar-container"> <img src="images/kol2.jpg" alt="头像" class="kol-avatar"> </div> <div class="kol-info"> <div class="kol-name">李果园</div> <div class="kol-title">果树种植专家 | 智慧农业大使</div> <div class="kol-desc">经营果园15年，擅长果树种植、嘉接技术和有机种植，开设线上果园课堂，帮助万名果农提升技术</div> <div class="kol-stats"> <div class="kol-stat"> <div class="kol-stat-value">96万</div> <div class="kol-stat-label">粉丝</div> </div> <div class="kol-stat"> <div class="kol-stat-value">420万</div> <div class="kol-stat-label">播放量</div> </div> <div class="kol-stat"> <div class="kol-stat-value">65万</div> <div class="kol-stat-label">点赞</div> </div> </div> <div class="kol-platforms"> <div class="kol-platform platform-douyin"><i class="fab fa-tiktok"></i></div> <div class="kol-platform platform-xiaohongshu"><i class="fas fa-book"></i></div> <div class="kol-platform platform-weibo"><i class="fab fa-weibo"></i></div> </div> <div class="kol-actions"> <button class="btn btn-danger w-100"><i class="fas fa-user me-2"></i>查看主页</button> </div> </div> </div> </div> <div class="col-md-4 mb-4"> <div class="kol-profile"> <div class="kol-badge">养殖达人</div> <img src="images/kol-cover3.jpg" alt="封面图" class="kol-cover"> <div class="kol-avatar-container"> <img src="images/kol3.jpg" alt="头像" class="kol-avatar"> </div> <div class="kol-info"> <div class="kol-name">张养殖家</div> <div class="kol-title">生态养殖专家 | 智慧农业大使</div> <div class="kol-desc">从事生态养殖10年，擅长生态养鸡、养猪和水产养殖，开发多项养殖专利技术，带动当地农民致富</div> <div class="kol-stats"> <div class="kol-stat"> <div class="kol-stat-value">85万</div> <div class="kol-stat-label">粉丝</div> </div> <div class="kol-stat"> <div class="kol-stat-value">380万</div> <div class="kol-stat-label">播放量</div> </div> <div class="kol-stat"> <div class="kol-stat-value">58万</div> <div class="kol-stat-label">点赞</div> </div> </div> <div class="kol-platforms"> <div class="kol-platform platform-douyin"><i class="fab fa-tiktok"></i></div> <div class="kol-platform platform-kuaishou"><i class="fas fa-play"></i></div> <div class="kol-platform platform-bilibili"><i class="fas fa-play-circle"></i></div> </div> <div class="kol-actions"> <button class="btn btn-danger w-100"><i class="fas fa-user me-2"></i>查看主页</button> </div> </div> </div> </div> </div> <div class="text-center mt-3"> <button class="btn btn-outline-danger">查看更多新农人 KOL</button> </div> </div> <!-- 孵化计划介绍 --> <div class="kol-card mb-4"> <h4 class="mb-4">孵化计划介绍</h4> <div class="row"> <div class="col-md-6 mb-4"> <div class="benefit-card"> <div class="benefit-header"> <div class="benefit-icon"> <i class="fas fa-graduation-cap"></i> </div> <div> <div class="benefit-title">专业培训</div> <div class="benefit-subtitle">提升内容创作能力</div> </div> </div> <div class="benefit-body"> <div class="benefit-description">
提供内容创作、短视频拍摄、直播技巧、账号运营等专业培训课程，帮助新农人提升内容创作能力和传播效果。课程由行业专家和知名创作者授课，理论与实践相结合。
</div> </div> </div> </div> <div class="col-md-6 mb-4"> <div class="benefit-card"> <div class="benefit-header"> <div class="benefit-icon"> <i class="fas fa-tools"></i> </div> <div> <div class="benefit-title">设备支持</div> <div class="benefit-subtitle">提供专业创作设备</div> </div> </div> <div class="benefit-body"> <div class="benefit-description">
为入选的新农人 KOL 提供专业的创作设备，包括高清摄像机、微跟云台、专业录音设备、照明设备等。同时提供视频剪辑软件和移动创作工具，提升内容创作效率和质量。
</div> </div> </div> </div> <div class="col-md-6 mb-4"> <div class="benefit-card"> <div class="benefit-header"> <div class="benefit-icon"> <i class="fas fa-bullhorn"></i> </div> <div> <div class="benefit-title">平台推广</div> <div class="benefit-subtitle">提供全方位平台曝光</div> </div> </div> <div class="benefit-body"> <div class="benefit-description">
为新农人 KOL 提供智慧农业平台的全方位推广支持，包括平台首页推荐、官方账号转发、线下活动邀请等。同时帮助对接各大短视频平台的创作者计划，提升账号成长速度。
</div> </div> </div> </div> <div class="col-md-6 mb-4"> <div class="benefit-card"> <div class="benefit-header"> <div class="benefit-icon"> <i class="fas fa-coins"></i> </div> <div> <div class="benefit-title">商业变现</div> <div class="benefit-subtitle">提供多元化收入模式</div> </div> </div> <div class="benefit-body"> <div class="benefit-description">
帮助新农人 KOL 实现内容的商业变现，包括平台流量分成、广告合作、农产品带货、知识付费等多元化收入模式。提供专业的商业化运营指导，帮助新农人提升收入水平。
</div> </div> </div> </div> </div> </div> <!-- 申请流程 --> <div class="kol-card mb-4"> <h4 class="mb-4">申请流程</h4> <div class="step-card"> <div class="step-header"> <div class="step-number">1</div> <div class="step-title">提交申请</div> </div> <div class="step-body"> <div class="step-description">
点击“立即申请”按钮，填写申请表格，提供个人信息、农业背景、内容创作经验和作品样例等资料。如果您已经有自己的社交媒体账号，请提供相关链接。
</div> </div> <div class="step-connector"></div> </div> <div class="step-card"> <div class="step-header"> <div class="step-number">2</div> <div class="step-title">初审筛选</div> </div> <div class="step-body"> <div class="step-description">
我们的评审团队将对所有申请进行初审，从农业专业背景、内容创作潜力、个人特色等方面进行评估。初审通过的申请者将进入下一轮面试环节。
</div> </div> <div class="step-connector"></div> </div> <div class="step-card"> <div class="step-header"> <div class="step-number">3</div> <div class="step-title">线上面试</div> </div> <div class="step-body"> <div class="step-description">
通过初审的申请者将参加线上面试，与我们的内容团队和农业专家进行深入交流。面试内容包括农业知识问答、内容创作理念、现场内容即兴创作等环节。
</div> </div> <div class="step-connector"></div> </div> <div class="step-card"> <div class="step-header"> <div class="step-number">4</div> <div class="step-title">结果公布</div> </div> <div class="step-body"> <div class="step-description">
面试结束后，我们将在 7 个工作日内公布入选名单。入选的新农人 KOL 将收到正式通知和合作协议，并安排后续的培训和支持计划。
</div> </div> <div class="step-connector"></div> </div> <div class="step-card"> <div class="step-header"> <div class="step-number">5</div> <div class="step-title">入院培训</div> </div> <div class="step-body"> <div class="step-description">
入选的新农人 KOL 将参加为期一周的入院集中培训，学习内容创作、短视频拍摄、直播技巧、账号运营等技能。培训结束后，将正式成为智慧农业平台的签约 KOL。
</div> </div> </div> </div> <!-- 成功案例 --> <div class="kol-card mb-4"> <h4 class="mb-4">成功案例</h4> <div class="success-story"> <div class="success-header"> <img src="images/success1.jpg" alt="成功案例" class="success-avatar"> <div class="success-user"> <h5 class="success-name">王农哥</h5> <div class="success-title">水稻种植专家 | 第一期孵化计划成员</div> </div> </div> <div class="success-content">
加入孵化计划前，我只是一个普通的水稻种植户，虽然有丰富的种植经验，但不知道如何分享出去。通过孵化计划的培训和支持，我学会了如何创作有趣、有用的短视频内容，将我的种植技术和经验分享给更多农民。现在我的抖音账号粉丝超过100万，每月仅通过带货和广告合作就能收入超过3万元，比我以前纯种地的收入还高。
</div> <div class="success-stats"> <div class="success-stat"><i class="fas fa-user-plus"></i> 粉丝增长: 0 → 128万</div> <div class="success-stat"><i class="fas fa-coins"></i> 月收入: 5千 → 3万+</div> </div> </div> <div class="success-story"> <div class="success-header"> <img src="images/success2.jpg" alt="成功案例" class="success-avatar"> <div class="success-user"> <h5 class="success-name">李果园</h5> <div class="success-title">果树种植专家 | 第二期孵化计划成员</div> </div> </div> <div class="success-content">
我经营果园已有15年，一直想把自己的果树种植技术分享出去，但若要自己摸索创作和运营，实在是太困难了。加入孵化计划后，我得到了专业的指导和设备支持，很快就在小红书和抖音上积累了一定粉丝。现在我不仅能销售自己的水果，还开设了线上果园课堂，帮助其他果农提升技术。这不仅带来了经济收益，也让我在行业内获得了很高的声誉。
</div> <div class="success-stats"> <div class="success-stat"><i class="fas fa-user-plus"></i> 粉丝增长: 0 → 96万</div> <div class="success-stat"><i class="fas fa-coins"></i> 月收入: 8千 → 2.5万+</div> </div> </div> <div class="success-story"> <div class="success-header"> <img src="images/success3.jpg" alt="成功案例" class="success-avatar"> <div class="success-user"> <h5 class="success-name">张养殖家</h5> <div class="success-title">生态养殖专家 | 第三期孵化计划成员</div> </div> </div> <div class="success-content">
我一直在探索生态养殖的新模式，开发了一些独特的养殖技术，但若要将这些技术推广出去却不知道从何入手。加入孵化计划后，我学会了如何通过短视频和直播展示我的养殖模式和成果。现在我不仅在多个平台拥有大量粉丝，还开发了自己的养殖技术课程和养殖设备，并与多家企业建立了合作关系。孵化计划开启了我事业发展的新道路。
</div> <div class="success-stats"> <div class="success-stat"><i class="fas fa-user-plus"></i> 粉丝增长: 0 → 85万</div> <div class="success-stat"><i class="fas fa-coins"></i> 月收入: 1万 → 4万+</div> </div> </div> </div> </div> <!-- 右侧内容 --> <div class="col-md-4"> <!-- 申请表单 --> <div class="kol-card"> <h4 class="mb-4">申请表单</h4> <div class="application-form"> <div class="mb-3"> <label class="form-label">姓名</label> <input type="text" class="form-control" placeholder="请输入您的真实姓名"> </div> <div class="mb-3"> <label class="form-label">手机号码</label> <input type="tel" class="form-control" placeholder="请输入您的手机号码"> </div> <div class="mb-3"> <label class="form-label">年龄</label> <select class="form-select"> <option>请选择您的年龄段</option> <option>18-25岁</option> <option>26-35岁</option> <option>36-45岁</option> <option>46-55岁</option> <option>56岁及以上</option> </select> </div> <div class="mb-3"> <label class="form-label">农业领域</label> <select class="form-select"> <option>请选择您的农业领域</option> <option>种植业</option> <option>养殖业</option> <option>农产品加工</option> <option>农业技术</option> <option>其他</option> </select> </div> <div class="mb-3"> <label class="form-label">从事农业年限</label> <select class="form-select"> <option>请选择您从事农业的年限</option> <option>1-3年</option> <option>3-5年</option> <option>5-10年</option> <option>10年以上</option> </select> </div> <div class="mb-3"> <label class="form-label">社交媒体账号</label> <input type="text" class="form-control" placeholder="请输入您的社交媒体账号链接（如有）"> </div> <div class="mb-3"> <label class="form-label">自我介绍</label> <textarea class="form-control" rows="4" placeholder="请简要介绍您的农业背景、专长领域和特色优势"></textarea> </div> <div class="mb-3"> <label class="form-label">申请理由</label> <textarea class="form-control" rows="4" placeholder="请说明您为什么想成为新农人 KOL，以及您对内容创作的想法"></textarea> </div> <div class="d-grid"> <button class="btn btn-danger">提交申请</button> </div> </div> </div> <!-- 培训课程 --> <div class="kol-card mt-4"> <h4 class="mb-4">培训课程</h4> <div class="course-card"> <div class="course-icon"> <i class="fas fa-video"></i> </div> <div class="course-info"> <div class="course-title">短视频内容创作</div> <div class="course-desc">学习如何策划、拍摄和剪辑有吸引力的短视频内容，抓住用户注意力</div> </div> </div> <div class="course-card"> <div class="course-icon"> <i class="fas fa-camera"></i> </div> <div class="course-info"> <div class="course-title">视频拍摄与剪辑</div> <div class="course-desc">掌握专业的视频拍摄技巧和剪辑软件的使用，提升内容质量</div> </div> </div> <div class="course-card"> <div class="course-icon"> <i class="fas fa-microphone"></i> </div> <div class="course-info"> <div class="course-title">直播技巧与表达</div> <div class="course-desc">学习直播技巧、表达方式和与粉丝互动的方法，提升直播效果</div> </div> </div> <div class="course-card"> <div class="course-icon"> <i class="fas fa-chart-line"></i> </div> <div class="course-info"> <div class="course-title">账号运营与数据分析</div> <div class="course-desc">掌握账号运营策略和数据分析方法，了解粉丝需求和内容效果</div> </div> </div> <div class="course-card"> <div class="course-icon"> <i class="fas fa-shopping-cart"></i> </div> <div class="course-info"> <div class="course-title">内容变现与商业合作</div> <div class="course-desc">学习如何将内容转化为商业价值，包括带货、广告合作和知识付费</div> </div> </div> </div> <!-- 常见问题 --> <div class="kol-card mt-4"> <h4 class="mb-4">常见问题</h4> <div class="faq-item"> <div class="faq-question" onclick="toggleFaq(this)"> <span>申请条件是什么？</span> <i class="fas fa-chevron-down"></i> </div> <div class="faq-answer">
申请成为新农人 KOL 的基本条件包括：1) 年满18周岁；2) 至少有一年以上的农业相关经验；3) 对内容创作和知识分享有浓厚兴趣；4) 愿意学习新知识和技能；5) 能够每周至少投入 10 小时用于内容创作。我们不要求申请者必须有现成的社交媒体账号或粉丝基础，但需要在农业领域有一定的专业知识和实践经验。
</div> </div> <div class="faq-item"> <div class="faq-question" onclick="toggleFaq(this)"> <span>孵化计划的周期是多久？</span> <i class="fas fa-chevron-down"></i> </div> <div class="faq-answer">
新农人 KOL 孵化计划的完整周期为一年。其中包括一周的入院集中培训和后续 11 个月的持续指导和支持。在这一年中，我们将提供定期的线上课程、一寱一指导、内容评审和运营建议。一年期满后，表现优秀的 KOL 可以继续签约，成为智慧农业平台的长期合作伙伴。
</div> </div> <div class="faq-item"> <div class="faq-question" onclick="toggleFaq(this)"> <span>入选后需要支付费用吗？</span> <i class="fas fa-chevron-down"></i> </div> <div class="faq-answer">
新农人 KOL 孵化计划是完全免费的，入选的新农人不需要支付任何费用。相反，我们将为入选的 KOL 提供专业的培训、设备支持、平台推广和商业合作机会。作为回报，我们期望入选的 KOL 能够定期创作优质内容，并在智慧农业平台上分享。具体的内容合作要求将在合作协议中详细说明。
</div> </div> <div class="faq-item"> <div class="faq-question" onclick="toggleFaq(this)"> <span>如何提高入选成功率？</span> <i class="fas fa-chevron-down"></i> </div> <div class="faq-answer">
提高入选成功率的建议：1) 充分展示您的农业专业背景和实践经验，包括具体的成就和案例；2) 如果有现成的内容创作作品，请提供样例链接；3) 清晰地表达您对内容创作的想法和独特的视角；4) 展示您的学习意愿和成长潜力；5) 说明您如何将自己的农业知识转化为有价值的内容，帮助更多农民。我们更看重申请者的潜力和独特性，而非现有的粉丝数量。
</div> </div> <div class="faq-item"> <div class="faq-question" onclick="toggleFaq(this)"> <span>入选后能获得哪些收益？</span> <i class="fas fa-chevron-down"></i> </div> <div class="faq-answer">
入选新农人 KOL 孵化计划后，您将获得以下收益：1) 免费的专业内容创作培训，价值超过 10,000 元；2) 专业创作设备支持，包括摄像机、微跟云台、录音设备等；3) 平台流量支持和推广资源；4) 内容变现机会，包括平台流量分成、广告合作、农产品带货等；5) 行业人脉资源和合作机会；6) 成为智慧农业平台的官方合作伙伴，提升个人品牌价值。根据我们的数据，孵化计划的 KOL 平均月收入可达 2-4 万元。
</div> </div> </div> <!-- 申请时间表 --> <div class="kol-card mt-4"> <h4 class="mb-4">申请时间表</h4> <div class="timeline"> <div class="timeline-line"></div> <div class="timeline-item"> <div class="timeline-dot"></div> <div class="timeline-date">2023年10月15日</div> <div class="timeline-content"> <div class="timeline-title">申请开始</div> <div class="timeline-desc">第四期新农人 KOL 孵化计划正式开始招募</div> </div> </div> <div class="timeline-item"> <div class="timeline-dot"></div> <div class="timeline-date">2023年11月15日</div> <div class="timeline-content"> <div class="timeline-title">申请截止</div> <div class="timeline-desc">第四期新农人 KOL 孵化计划申请截止</div> </div> </div> <div class="timeline-item"> <div class="timeline-dot"></div> <div class="timeline-date">2023年11月20-30日</div> <div class="timeline-content"> <div class="timeline-title">面试阶段</div> <div class="timeline-desc">初审通过的申请者参加线上面试</div> </div> </div> <div class="timeline-item"> <div class="timeline-dot"></div> <div class="timeline-date">2023年12月10日</div> <div class="timeline-content"> <div class="timeline-title">结果公布</div> <div class="timeline-desc">公布第四期新农人 KOL 孵化计划入选名单</div> </div> </div> <div class="timeline-item"> <div class="timeline-dot"></div> <div class="timeline-date">2024年1月15-21日</div> <div class="timeline-content"> <div class="timeline-title">入院培训</div> <div class="timeline-desc">入选的新农人 KOL 参加为期一周的入院集中培训</div> </div> </div> </div> </div> </div> </div> </div> <!-- 页脚 -->
<div id="footer-container"></div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="js/login-config.js"></script>
<script src="js/components.js"></script>
<script>
// 使用 common.js 加载导航栏

// 切换FAQ显示/隐藏
function toggleFaq(element) {
const answer = element.nextElementSibling;
const icon = element.querySelector('i');
if (answer.classList.contains('active')) {
answer.classList.remove('active');
icon.classList.remove('fa-chevron-up');
icon.classList.add('fa-chevron-down');
} else {
answer.classList.add('active');
icon.classList.remove('fa-chevron-down');
icon.classList.add('fa-chevron-up');
}
}
// 查看主页按钮点击事件
document.querySelectorAll('.kol-actions .btn').forEach(btn => {
btn.addEventListener('click', function() {
const kolName = this.closest('.kol-profile').querySelector('.kol-name').textContent;
alert(`正在查看 ${kolName} 的主页，请稍后...`);
});
});
// 查看更多新农人 KOL按钮点击事件
document.querySelector('.btn-outline-danger').addEventListener('click', function() {
alert('正在加载更多新农人 KOL，请稍后...');
});
// 提交申请按钮点击事件
document.querySelector('.application-form .btn-danger').addEventListener('click', function(e) {
e.preventDefault();
alert('您的申请已提交成功！我们将尽快审核并与您联系。');
});
// 立即申请按钮点击事件
document.querySelector('.btn-danger.btn-lg').addEventListener('click', function() {
document.querySelector('.kol-card:nth-child(1)').scrollIntoView({ behavior: 'smooth' });
});
// 了解课程按钮点击事件
document.querySelector('.btn-outline-light.btn-lg').addEventListener('click', function() {
document.querySelector('.kol-card:nth-child(2)').scrollIntoView({ behavior: 'smooth' });
});

// 确保导航栏在页面加载完成后可见
window.onload = function() {
    // 确保导航栏可见
    const navbarContainer = document.getElementById('navbar-container');
    if (navbarContainer) {
        navbarContainer.style.display = '';
    }
};
</script> </body> </html>