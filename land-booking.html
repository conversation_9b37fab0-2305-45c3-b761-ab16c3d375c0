<!DOCTYPE html> <html lang="zh-CN"> <head> <meta charset="UTF-8"> <meta name="viewport" content="width=device-width, initial-scale=1.0"> <title>预约看地 - 智慧农业平台</title> <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet"> <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet"> <link rel="stylesheet" href="css/common.css"> <link rel="stylesheet" href="css/navbar.css"> <style>
.booking-form {
background: #fff;
padding: 30px;
border-radius: 10px;
box-shadow: 0 0 20px rgba(0,0,0,0.1);
}
.form-section {
margin-bottom: 30px;
}
.success-message {
display: none;
background: #d4edda;
color: #155724;
padding: 20px;
border-radius: 5px;
margin-bottom: 20px;
}
.navbar-brand {
display: flex;
align-items: center;
gap: 10px;
}
.brand-logo {
width: 40px;
height: 40px;
}
.brand-logo img {
width: 100%;
min-height: auto;
object-fit: contain;
}
</style> </head> <body> <!-- 导航栏容器 --> <div id="navbar-container"></div> <!-- 导航栏 -->
<div id="navbar-container"></div>
<!-- 主要内容 --> <div class="container py-5"> <div class="row justify-content-center"> <div class="col-md-8"> <h2 class="text-center mb-4">预约看地</h2> <!-- 预约成功提示 --> <div class="success-message" id="successMessage"> <h4><i class="fas fa-check-circle"></i> 预约成功！</h4> <p>我们的工作人员将在24小时内与您联系确认具体看地时间。</p> </div> <!-- 预约表单 --> <div class="booking-form"> <form id="bookingForm" onsubmit="return handleSubmit(event)"> <!-- 基本信息 --> <div class="form-section"> <h5 class="mb-3">基本信息</h5> <div class="mb-3"> <label class="form-label">预约土地</label> <input type="text" class="form-control" id="landTitle" readonly> </div> <div class="mb-3"> <label class="form-label">土地位置</label> <input type="text" class="form-control" id="landLocation" readonly> </div> <div class="mb-3"> <label class="form-label">土地面积</label> <input type="text" class="form-control" id="landArea" readonly> </div> </div> <!-- 联系人信息 --> <div class="form-section"> <h5 class="mb-3">联系人信息</h5> <div class="mb-3"> <label class="form-label">姓名 <span class="text-danger">*</span></label> <input type="text" class="form-control" required> </div> <div class="mb-3"> <label class="form-label">手机号码 <span class="text-danger">*</span></label> <input type="tel" class="form-control" pattern="[0-9]{11}" required> </div> <div class="mb-3"> <label class="form-label">电子邮箱</label> <input type="email" class="form-control"> </div> </div> <!-- 预约信息 --> <div class="form-section"> <h5 class="mb-3">预约信息</h5> <div class="mb-3"> <label class="form-label">期望看地日期 <span class="text-danger">*</span></label> <input type="date" class="form-control" required min=""> </div> <div class="mb-3"> <label class="form-label">期望看地时间段 <span class="text-danger">*</span></label> <select class="form-select" required> <option value="">请选择时间段</option> <option value="morning">上午 (9:00-12:00)</option> <option value="afternoon">下午 (14:00-17:00)</option> </select> </div> <div class="mb-3"> <label class="form-label">种植意向</label> <select class="form-select"> <option value="">请选择种植作物</option> <option value="rice">水稻</option> <option value="wheat">小麦</option> <option value="vegetables">蔬菜</option> <option value="fruits">水果</option> <option value="other">其他</option> </select> </div> <div class="mb-3"> <label class="form-label">备注信息</label> <textarea class="form-control" rows="3" placeholder="请输入您的其他要求或问题"></textarea> </div> </div> <!-- 提交按钮 --> <div class="text-center"> <button type="submit" class="btn btn-success btn-lg px-5">提交预约</button> </div> </form> </div> </div> </div> </div> <!-- 页脚 -->
<div id="footer-container"></div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="js/login-config.js"></script>
<script src="js/components.js"></script>
<script>
// 设置日期最小值为今天
document.querySelector('input[type="date"]').min = new Date().toISOString().split('T')[0];
// 从 URL 获取土地信息并填充
window.onload = function() {
const urlParams = new URLSearchParams(window.location.search);
const landId = urlParams.get('id');
// 这里应该根据 landId 从后端获取数据
// 目前使用模拟数据
const landData = {
title: '浙江嘉兴水稻基地',
location: '浙江省嘉兴市南湖区',
area: '300亩'
};
document.getElementById('landTitle').value = landData.title;
document.getElementById('landLocation').value = landData.location;
document.getElementById('landArea').value = landData.area;
}
// 处理表单提交
function handleSubmit(event) {
event.preventDefault();
// 这里应该添加表单数据验证和提交到后端的逻辑
// 显示成功消息
document.getElementById('successMessage').style.display = 'block';
document.getElementById('bookingForm').style.display = 'none';
// 滚动到顶部
window.scrollTo(0, 0);
return false;
}
// 设置当前页面的导航链接为激活状态
document.querySelectorAll('.nav-link').forEach(link => {
const page = link.getAttribute('data-page');
if (page === 'land') {
link.classList.add('active');
}
});
</script> </body> </html> 