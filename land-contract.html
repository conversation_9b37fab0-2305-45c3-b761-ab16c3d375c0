<!DOCTYPE html> <html lang="zh-CN"> <head> <meta charset="UTF-8"> <meta name="viewport" content="width=device-width, initial-scale=1.0"> <title>在线签约 - 智慧农业平台</title> <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet"> <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet"> <link rel="stylesheet" href="css/common.css"> <link rel="stylesheet" href="css/navbar.css"> <style>
:root {
--primary-color: #28a745;
--secondary-color: #6c757d;
--light-bg: #f8f9fa;
--card-shadow: 0 4px 6px rgba(0,0,0,0.1);
--transition: all 0.3s ease;
}
body {
background-color: var(--light-bg);
font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}
.contract-header {
background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80');
background-size: cover;
background-position: center;
height: 200px;
color: white;
display: flex;
align-items: center;
margin-bottom: 30px;
border-radius: 10px;
}
.step-container {
background: white;
padding: 30px;
border-radius: 10px;
box-shadow: var(--card-shadow);
margin-bottom: 30px;
}
.step-indicator {
display: flex;
justify-content: space-between;
margin-bottom: 30px;
}
.step {
flex: 1;
text-align: center;
padding: 15px;
position: relative;
}
.step::after {
content: '';
position: absolute;
top: 50%;
right: 0;
width: 100%;
height: 2px;
background-color: #ddd;
transform: translateY(-50%);
z-index: -1;
}
.step:last-child::after {
display: none;
}
.step-number {
width: 40px;
height: 40px;
background-color: #ddd;
border-radius: 50%;
display: flex;
align-items: center;
justify-content: center;
margin: 0 auto 10px;
font-weight: bold;
color: white;
position: relative;
z-index: 1;
}
.step.active .step-number {
background-color: var(--primary-color);
}
.step.completed .step-number {
background-color: var(--primary-color);
}
.step-title {
font-size: 0.9rem;
color: var(--secondary-color);
}
.step.active .step-title {
color: var(--primary-color);
font-weight: bold;
}
.contract-preview {
background: white;
padding: 30px;
border-radius: 10px;
box-shadow: var(--card-shadow);
margin-bottom: 30px;
max-height: 500px;
overflow-y: auto;
}
.contract-content {
font-family: 'Times New Roman', Times, serif;
line-height: 1.6;
}
.contract-title {
text-align: center;
font-weight: bold;
margin-bottom: 20px;
}
.contract-section {
margin-bottom: 20px;
}
.contract-section-title {
font-weight: bold;
margin-bottom: 10px;
}
.signature-area {
border: 1px dashed #ddd;
padding: 20px;
text-align: center;
margin-top: 20px;
border-radius: 10px;
}
.signature-pad {
width: 100%;
height: 150px;
background: #f9f9f9;
border-radius: 5px;
margin-bottom: 15px;
cursor: crosshair;
}
.payment-methods {
display: flex;
flex-wrap: wrap;
gap: 15px;
margin-top: 20px;
}
.payment-method {
flex: 1;
min-width: 120px;
padding: 15px;
border: 1px solid #ddd;
border-radius: 10px;
text-align: center;
cursor: pointer;
transition: var(--transition);
}
.payment-method:hover {
border-color: var(--primary-color);
transform: translateY(-5px);
}
.payment-method.active {
border-color: var(--primary-color);
background-color: rgba(40, 167, 69, 0.1);
}
.payment-icon {
font-size: 2rem;
margin-bottom: 10px;
}
.payment-name {
font-size: 0.9rem;
}
.summary-item {
display: flex;
justify-content: space-between;
margin-bottom: 10px;
padding-bottom: 10px;
border-bottom: 1px solid #eee;
}
.summary-total {
font-weight: bold;
font-size: 1.2rem;
color: var(--primary-color);
}
.planting-plan-preview {
background: #f9f9f9;
padding: 20px;
border-radius: 10px;
margin-top: 20px;
}
.plan-item {
display: flex;
justify-content: space-between;
margin-bottom: 10px;
padding-bottom: 10px;
border-bottom: 1px solid #eee;
}
.plan-date {
font-weight: bold;
min-width: 100px;
}
.plan-task {
flex: 1;
padding: 0 15px;
}
.plan-status {
min-width: 80px;
text-align: right;
}
</style> </head> <body> <!-- 导航栏 -->
<div id="navbar-container"></div>
<!-- 主要内容 --> <div class="container py-5"> <div class="contract-header"> <div class="container"> <h1>在线签约</h1> <p class="lead">完成土地租赁合同签署，开始您的种植计划</p> </div> </div> <!-- 步骤指示器 --> <div class="step-indicator"> <div class="step completed"> <div class="step-number">1</div> <div class="step-title">选择土地</div> </div> <div class="step active"> <div class="step-number">2</div> <div class="step-title">签署合同</div> </div> <div class="step"> <div class="step-number">3</div> <div class="step-title">支付定金</div> </div> <div class="step"> <div class="step-number">4</div> <div class="step-title">种植计划</div> </div> <div class="step"> <div class="step-number">5</div> <div class="step-title">完成</div> </div> </div> <div class="row"> <div class="col-md-8"> <!-- 合同预览 --> <div class="step-container"> <h4 class="mb-4">土地租赁合同</h4> <div class="contract-preview"> <div class="contract-content"> <div class="contract-title">农业土地租赁合同</div> <div class="contract-section"> <div class="contract-section-title">一、合同双方</div> <p>甲方（出租方）：浙江农业发展有限公司</p> <p>乙方（承租方）：张三</p> </div> <div class="contract-section"> <div class="contract-section-title">二、租赁土地基本情况</div> <p>1. 土地名称：浙江嘉兴水稻基地</p> <p>2. 土地位置：浙江省嘉兴市南湖区</p> <p>3. 土地面积：300亩</p> <p>4. 土地类型：水田</p> <p>5. 适宜作物：水稻、小麦</p> </div> <div class="contract-section"> <div class="contract-section-title">三、租赁期限</div> <p>1. 租赁期限：3年</p> <p>2. 起始日期：2023年6月1日</p> <p>3. 终止日期：2026年5月31日</p> </div> <div class="contract-section"> <div class="contract-section-title">四、租金及支付方式</div> <p>1. 租金标准：2000元/亩/年</p> <p>2. 总租金：600,000元/年</p> <p>3. 支付方式：按年支付，每年6月1日前支付当年租金</p> <p>4. 定金：总租金的20%，即120,000元</p> </div> <div class="contract-section"> <div class="contract-section-title">五、双方权利与义务</div> <p>1. 甲方权利与义务：</p> <p>   a) 按照合同约定将土地交付乙方使用；</p> <p>   b) 不得干涉乙方正常的生产经营活动；</p> <p>   c) 协助乙方解决生产经营中的困难。</p> <p>2. 乙方权利与义务：</p> <p>   a) 按照合同约定支付租金；</p> <p>   b) 合理利用土地，不得破坏土地资源；</p> <p>   c) 租赁期满后，将土地恢复原状交还甲方。</p> </div> <div class="contract-section"> <div class="contract-section-title">六、合同变更与解除</div> <p>1. 经双方协商一致，可以变更或解除合同；</p> <p>2. 因不可抗力导致合同无法履行的，可以变更或解除合同；</p> <p>3. 一方违约，另一方有权要求继续履行或解除合同。</p> </div> <div class="contract-section"> <div class="contract-section-title">七、违约责任</div> <p>1. 甲方违约，应赔偿乙方因此造成的损失；</p> <p>2. 乙方逾期支付租金，每逾期一日，按应付租金的0.05%支付滞纳金；</p> <p>3. 乙方擅自改变土地用途或破坏土地资源的，甲方有权解除合同并要求赔偿损失。</p> </div> <div class="contract-section"> <div class="contract-section-title">八、争议解决</div> <p>因本合同引起的争议，双方应协商解决；协商不成的，提交嘉兴市仲裁委员会仲裁。</p> </div> <div class="contract-section"> <div class="contract-section-title">九、其他</div> <p>1. 本合同一式两份，甲乙双方各执一份，具有同等法律效力；</p> <p>2. 本合同自双方签字盖章之日起生效。</p> </div> <div class="contract-section"> <div class="row"> <div class="col-md-6"> <p>甲方（盖章）：</p> <p>代表人（签字）：</p> <p>日期：</p> </div> <div class="col-md-6"> <p>乙方（签字）：</p> <p>日期：</p> </div> </div> </div> </div> </div> <!-- 电子签名区域 --> <div class="signature-area"> <h5>电子签名</h5> <p class="text-muted">请在下方签名区域进行签名</p> <div class="signature-pad" id="signaturePad"></div> <div class="d-flex justify-content-between"> <button class="btn btn-outline-secondary" id="clearSignature">清除签名</button> <button class="btn btn-success" id="confirmSignature">确认签名</button> </div> </div> <div class="d-flex justify-content-between mt-4"> <button class="btn btn-outline-secondary" onclick="prevStep()">上一步</button> <button class="btn btn-success" onclick="nextStep()">下一步：支付定金</button> </div> </div> </div> <div class="col-md-4"> <!-- 合同摘要 --> <div class="step-container"> <h4 class="mb-4">合同摘要</h4> <div class="summary-item"> <div>土地名称</div> <div>浙江嘉兴水稻基地</div> </div> <div class="summary-item"> <div>土地面积</div> <div>300亩</div> </div> <div class="summary-item"> <div>租赁期限</div> <div>3年</div> </div> <div class="summary-item"> <div>租金标准</div> <div>¥2000/亩/年</div> </div> <div class="summary-item"> <div>年租金总额</div> <div>¥600,000</div> </div> <div class="summary-item"> <div>定金（20%）</div> <div class="summary-total">¥120,000</div> </div> </div> <!-- CA电子签章认证 --> <div class="step-container"> <h4 class="mb-4">CA电子签章认证</h4> <p>本合同使用国家认可的CA电子签章系统，具有法律效力。</p> <div class="d-flex align-items-center mb-3"> <i class="fas fa-shield-alt text-success me-3" style="font-size: 2rem;"></i> <div> <div class="fw-bold">安全可靠</div> <div class="text-muted">采用国家密码局认证的加密算法</div> </div> </div> <div class="d-flex align-items-center mb-3"> <i class="fas fa-gavel text-success me-3" style="font-size: 2rem;"></i> <div> <div class="fw-bold">法律效力</div> <div class="text-muted">符合《电子签名法》规定，具有法律效力</div> </div> </div> <div class="d-flex align-items-center"> <i class="fas fa-history text-success me-3" style="font-size: 2rem;"></i> <div> <div class="fw-bold">永久保存</div> <div class="text-muted">合同签署后将永久保存，随时可查</div> </div> </div> </div> </div> </div> </div> <!-- 页脚 -->
<div id="footer-container"></div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="js/login-config.js"></script>
<script src="js/components.js"></script>
<script src="https://cdn.jsdelivr.net/npm/signature_pad@4.0.0/dist/signature_pad.umd.min.js"></script> <script>
// 使用 common.js 加载导航栏

// 初始化签名板
var canvas = document.getElementById('signaturePad');
var signaturePad = new SignaturePad(canvas, {
backgroundColor: 'rgb(249, 249, 249)',
penColor: 'rgb(0, 0, 0)'
});
// 调整签名板大小
function resizeCanvas() {
var ratio = Math.max(window.devicePixelRatio || 1, 1);
canvas.width = canvas.offsetWidth * ratio;
canvas.height = canvas.offsetHeight * ratio;
canvas.getContext("2d").scale(ratio, ratio);
signaturePad.clear();
}
window.addEventListener("resize", resizeCanvas);
resizeCanvas();
// 清除签名
document.getElementById('clearSignature').addEventListener('click', function() {
signaturePad.clear();
});
// 确认签名
document.getElementById('confirmSignature').addEventListener('click', function() {
if (signaturePad.isEmpty()) {
alert('请先进行签名');
} else {
var signatureData = signaturePad.toDataURL();
console.log('签名已确认');
alert('签名已确认');
// 这里可以将签名数据保存到服务器
}
});
// 上一步
function prevStep() {
window.location.href = 'land-selection.html';
}
// 下一步
function nextStep() {
if (signaturePad.isEmpty()) {
alert('请先完成签名');
} else {
window.location.href = 'land-payment.html';
}
}

// 确保导航栏在页面加载完成后可见
window.onload = function() {
    // 确保导航栏可见
    const navbarContainer = document.getElementById('navbar-container');
    if (navbarContainer) {
        navbarContainer.style.display = '';
    }
};
</script> </body> </html>
