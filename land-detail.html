<!DOCTYPE html> <html lang="zh-CN"> <head> <meta charset="UTF-8"> <meta name="viewport" content="width=device-width, initial-scale=1.0"> <title>土地详情 - 智慧农业平台</title> <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet"> <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet"> <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" /> <link rel="stylesheet" href="css/common.css"> <link rel="stylesheet" href="css/navbar.css"> <style>
.land-gallery {
margin-bottom: 20px;
}
.land-gallery .main-image {
height: 300px;
object-fit: cover;
width: 100%;
background-color: #f8f9fa;
border-radius: 10px;
margin-bottom: 10px;
}
.land-gallery .thumbnails {
display: flex;
gap: 10px;
}
.land-gallery .thumbnail {
width: 100px;
height: 100px;
object-fit: cover;
border-radius: 5px;
cursor: pointer;
transition: opacity 0.3s;
}
.land-gallery .thumbnail:hover {
opacity: 0.8;
}
.land-info {
background: #fff;
padding: 20px;
border-radius: 10px;
box-shadow: 0 0 10px rgba(0,0,0,0.1);
margin-bottom: 20px;
}
.chat-window {
position: fixed;
bottom: 20px;
right: 20px;
width: 350px;
background: #fff;
border-radius: 10px;
box-shadow: 0 0 20px rgba(0,0,0,0.1);
z-index: 1000;
}
.chat-header {
background: var(--primary-color);
color: white;
padding: 10px 15px;
border-radius: 10px 10px 0 0;
cursor: pointer;
}
.chat-body {
height: 400px;
overflow-y: auto;
padding: 15px;
}
.chat-message {
margin-bottom: 15px;
}
.message-content {
padding: 10px;
border-radius: 10px;
max-width: 80%;
}
.message-sent {
background: var(--primary-color);
color: white;
margin-left: auto;
}
.message-received {
background: #f0f0f0;
}
.chat-input {
padding: 15px;
border-top: 1px solid #eee;
}
.chat-input input {
border-radius: 20px;
padding: 8px 15px;
}
.chat-input button {
border-radius: 20px;
padding: 8px 20px;
}
.feature-icon {
font-size: 1.5rem;
color: var(--primary-color);
margin-right: 10px;
}
#miniMap {
height: 200px;
border-radius: 10px;
margin-top: 10px;
}
.navbar-brand {
display: flex;
align-items: center;
gap: 10px;
}
.brand-logo {
width: 40px;
height: 40px;
}
.brand-logo img {
width: 100%;
min-height: auto;
object-fit: contain;
}
</style> </head> <body> <!-- 导航栏容器 --> <div id="navbar-container"></div> <!-- 导航栏 -->
<!-- 移除重复的导航栏容器 -->
<!-- 主要内容 --> <div class="container py-5"> <div class="row"> <!-- 土地图片 --> <div class="col-md-8"> <div class="land-gallery"> <img src="images/land1.jpg" alt="土地图片" class="main-image" id="mainImage"> <div class="thumbnails"> <img src="images/land1.jpg" alt="土地图片" class="thumbnail" onclick="changeImage(this.src)"> <img src="images/land2.jpg" alt="土地图片" class="thumbnail" onclick="changeImage(this.src)"> <img src="images/land3.jpg" alt="土地图片" class="thumbnail" onclick="changeImage(this.src)"> <img src="images/land4.jpg" alt="土地图片" class="thumbnail" onclick="changeImage(this.src)"> </div> </div> <!-- 土地信息 --> <div class="land-info"> <h3>浙江嘉兴水稻基地</h3> <div class="row mt-4"> <div class="col-md-6"> <p><i class="fas fa-map-marker-alt feature-icon"></i> 浙江省嘉兴市南湖区</p> <p><i class="fas fa-ruler-combined feature-icon"></i> 面积：300亩</p> <p><i class="fas fa-seedling feature-icon"></i> 适宜作物：水稻、小麦</p> </div> <div class="col-md-6"> <p><i class="fas fa-tint feature-icon"></i> 灌溉条件：完善</p> <p><i class="fas fa-road feature-icon"></i> 交通条件：便利</p> <p><i class="fas fa-sun feature-icon"></i> 光照条件：充足</p> </div> </div> </div> <!-- 土地详情 --> <div class="land-info"> <h4>土地详情</h4> <p>该土地位于浙江省嘉兴市南湖区，交通便利，灌溉设施完善。土壤肥沃，适合种植水稻、小麦等作物。周边配套设施齐全，有专业的农业技术指导团队。</p> <h5 class="mt-4">种植建议</h5> <ul> <li>建议种植优质水稻品种</li> <li>采用现代化种植技术</li> <li>定期进行土壤检测</li> <li>科学施肥，合理灌溉</li> </ul> </div> </div> <!-- 右侧信息 --> <div class="col-md-4"> <div class="land-info"> <h4>租赁信息</h4> <p class="text-success fw-bold fs-4">¥2000/亩/年</p> <div class="d-grid gap-2"> <button class="btn btn-success" onclick="showChat()">在线咨询</button> <a href="land-booking.html" class="btn btn-outline-success" onclick="bookLand()">预约看地</a> </div> </div> <div class="land-info"> <h4>联系方式</h4> <p><i class="fas fa-phone feature-icon"></i> 联系电话：13800138000</p> <p><i class="fas fa-envelope feature-icon"></i> 电子邮箱：<EMAIL></p> </div> <div class="land-info"> <h4>周边设施</h4> <div class="row mt-4"> <div class="col-md-6"> <p><i class="fas fa-road feature-icon"></i> 距离高速公路出口 5 公里</p> <p><i class="fas fa-store feature-icon"></i> 距离农资市场 3 公里</p> </div> <div class="col-md-6"> <p><i class="fas fa-flask feature-icon"></i> 距离农业技术站 2 公里</p> <p><i class="fas fa-industry feature-icon"></i> 距离农产品加工厂 4 公里</p> </div> </div> </div> <!-- 小地图 --> <div class="land-info"> <h4>位置信息</h4> <div id="miniMap"></div> </div> </div> </div> </div> <!-- 聊天窗口 --> <div class="chat-window" id="chatWindow" style="display: none;"> <div class="chat-header" onclick="toggleChat()"> <i class="fas fa-comments"></i> 在线咨询
<i class="fas fa-chevron-down float-end"></i> </div> <div class="chat-body" id="chatBody"> <div class="chat-message"> <div class="message-content message-received">
您好，欢迎咨询土地租赁信息，请问有什么可以帮您？
</div> </div> </div> <div class="chat-input"> <div class="input-group"> <input type="text" class="form-control" placeholder="请输入消息..." id="messageInput"> <button class="btn btn-success" onclick="sendMessage()">发送</button> </div> </div> </div> <!-- 页脚 -->
<div id="footer-container"></div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="js/login-config.js"></script>
<script src="js/components.js"></script>
<script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script> <script>
// 初始化小地图
var miniMap = L.map('miniMap').setView([30.2741, 120.1551], 13);
L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
attribution: '© OpenStreetMap contributors'
}).addTo(miniMap);
// 添加标记点
L.marker([30.2741, 120.1551])
.bindPopup('浙江嘉兴水稻基地')
.addTo(miniMap);
// 显示/隐藏聊天窗口
function showChat() {
document.getElementById('chatWindow').style.display = 'block';
}
function toggleChat() {
const chatWindow = document.getElementById('chatWindow');
const chatBody = document.querySelector('.chat-body');
const chatInput = document.querySelector('.chat-input');
if (chatBody.style.display === 'none') {
chatBody.style.display = 'block';
chatInput.style.display = 'block';
} else {
chatBody.style.display = 'none';
chatInput.style.display = 'none';
}
}
// 发送消息
function sendMessage() {
const input = document.getElementById('messageInput');
const message = input.value.trim();
if (message) {
const chatBody = document.getElementById('chatBody');
// 添加用户消息
const userMessage = document.createElement('div');
userMessage.className = 'chat-message';
userMessage.innerHTML = `
<div class="message-content message-sent">
${message}
</div>
`;
chatBody.appendChild(userMessage);
// 清空输入框
input.value = '';
// 滚动到底部
chatBody.scrollTop = chatBody.scrollHeight;
// 模拟客服回复
setTimeout(() => {
const replyMessage = document.createElement('div');
replyMessage.className = 'chat-message';
replyMessage.innerHTML = `
<div class="message-content message-received">
感谢您的咨询，我们的客服人员会尽快回复您。
</div>
`;
chatBody.appendChild(replyMessage);
chatBody.scrollTop = chatBody.scrollHeight;
}, 1000);
}
}
// 回车发送消息
document.getElementById('messageInput').addEventListener('keypress', function(e) {
if (e.key === 'Enter') {
sendMessage();
}
});
// 根据URL参数加载不同土地信息
function loadLandInfo() {
const urlParams = new URLSearchParams(window.location.search);
const landId = urlParams.get('id');
// 这里可以根据landId加载不同的土地信息
// 目前使用模拟数据
const landData = {
1: { name: '浙江嘉兴水稻基地', price: 2000, area: 300, location: [30.2741, 120.1551] },
2: { name: '江苏苏州蔬菜基地', price: 3000, area: 150, location: [31.2989, 120.5853] },
3: { name: '安徽合肥小麦基地', price: 1800, area: 500, location: [31.8206, 117.2272] },
4: { name: '浙江杭州蔬菜基地', price: 3500, area: 80, location: [30.2741, 120.1551] },
5: { name: '江苏南京水稻基地', price: 2200, area: 600, location: [31.2989, 120.5853] },
6: { name: '安徽芜湖蔬菜基地', price: 2800, area: 200, location: [31.8206, 117.2272] }
};
if (landId && landData[landId]) {
const data = landData[landId];
document.querySelector('h3').textContent = data.name;
document.querySelector('.text-success.fw-bold.fs-4').textContent = `¥${data.price}/亩/年`;
// 更新地图位置
miniMap.setView(data.location, 13);
L.marker(data.location)
.bindPopup(data.name)
.addTo(miniMap);
}
}
// 页面加载时执行
loadLandInfo();
// 预约看地
function bookLand() {
const urlParams = new URLSearchParams(window.location.search);
const landId = urlParams.get('id');
window.location.href = `land-booking.html?id=${landId}`;
}
// 切换主图
function changeImage(src) {
document.getElementById('mainImage').src = src;
}
// 设置当前页面的导航链接为激活状态
document.querySelectorAll('.nav-link').forEach(link => {
const page = link.getAttribute('data-page');
if (page === 'land') {
link.classList.add('active');
}
});

// 确保导航栏在页面加载完成后可见

// 页面加载完成后确保导航栏可见
window.onload = function() {
    // 确保导航栏可见
    const navbarContainer = document.getElementById('navbar-container');
    if (navbarContainer) {
        // 强制设置为可见
        navbarContainer.style.display = 'block';
        
        // 确保导航栏内容已加载
        if (!navbarContainer.innerHTML || navbarContainer.innerHTML.trim() === '') {
            // 如果导航栏内容为空，尝试重新加载
            if (typeof navbarHTML !== 'undefined') {
                navbarContainer.innerHTML = navbarHTML;
                
                // 重新应用登录状态和模块可见性
                if (typeof loadLoginState === 'function') loadLoginState();
                if (typeof updateNavbarDisplay === 'function') updateNavbarDisplay();
                if (typeof setActiveNavItem === 'function') setActiveNavItem();
                if (typeof setupLoginEvents === 'function') setupLoginEvents();
            }
        }
    }
};
</script> </body> </html> 