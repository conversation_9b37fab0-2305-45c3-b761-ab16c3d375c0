<!DOCTYPE html> <html lang="zh-CN"> <head> <meta charset="UTF-8"> <meta name="viewport" content="width=device-width, initial-scale=1.0"> <title>土地托管 - 智慧农业平台</title> <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet"> <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet"> <link rel="stylesheet" href="css/common.css"> <link rel="stylesheet" href="css/navbar.css"> <style>
:root {
--primary-color: #28a745;
--secondary-color: #6c757d;
--light-bg: #f8f9fa;
--card-shadow: 0 4px 6px rgba(0,0,0,0.1);
--transition: all 0.3s ease;
}
body {
background-color: var(--light-bg);
font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}
.management-header {
background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80');
background-size: cover;
background-position: center;
height: 300px;
color: white;
display: flex;
align-items: center;
margin-bottom: 30px;
border-radius: 10px;
}
.management-card {
background: white;
padding: 30px;
border-radius: 10px;
box-shadow: var(--card-shadow);
margin-bottom: 30px;
}
.service-card {
background: white;
border-radius: 10px;
box-shadow: var(--card-shadow);
margin-bottom: 30px;
overflow: hidden;
transition: var(--transition);
}
.service-card:hover {
transform: translateY(-10px);
}
.service-image {
height: 200px;
object-fit: cover;
width: 100%;
}
.service-content {
padding: 20px;
}
.service-title {
font-size: 1.2rem;
font-weight: bold;
margin-bottom: 10px;
}
.service-description {
color: var(--secondary-color);
margin-bottom: 15px;
}
.service-price {
font-weight: bold;
color: var(--primary-color);
font-size: 1.2rem;
margin-bottom: 15px;
}
.service-features {
margin-bottom: 15px;
}
.service-feature {
display: flex;
align-items: center;
margin-bottom: 5px;
}
.service-feature i {
color: var(--primary-color);
margin-right: 10px;
}
.step-indicator {
display: flex;
justify-content: space-between;
margin-bottom: 30px;
}
.step {
flex: 1;
text-align: center;
padding: 15px;
position: relative;
}
.step::after {
content: '';
position: absolute;
top: 50%;
right: 0;
width: 100%;
height: 2px;
background-color: #ddd;
transform: translateY(-50%);
z-index: -1;
}
.step:last-child::after {
display: none;
}
.step-number {
width: 40px;
height: 40px;
background-color: #ddd;
border-radius: 50%;
display: flex;
align-items: center;
justify-content: center;
margin: 0 auto 10px;
font-weight: bold;
color: white;
position: relative;
z-index: 1;
}
.step.active .step-number {
background-color: var(--primary-color);
}
.step.completed .step-number {
background-color: var(--primary-color);
}
.step-title {
font-size: 0.9rem;
color: var(--secondary-color);
}
.step.active .step-title {
color: var(--primary-color);
font-weight: bold;
}
.testimonial-card {
background: white;
padding: 20px;
border-radius: 10px;
box-shadow: var(--card-shadow);
margin-bottom: 20px;
}
.testimonial-content {
font-style: italic;
margin-bottom: 15px;
color: var(--secondary-color);
}
.testimonial-author {
display: flex;
align-items: center;
}
.testimonial-avatar {
width: 50px;
height: 50px;
border-radius: 50%;
object-fit: cover;
margin-right: 15px;
}
.testimonial-name {
font-weight: bold;
}
.testimonial-role {
font-size: 0.9rem;
color: var(--secondary-color);
}
.faq-item {
margin-bottom: 15px;
}
.faq-question {
font-weight: bold;
margin-bottom: 10px;
cursor: pointer;
display: flex;
justify-content: space-between;
align-items: center;
}
.faq-answer {
color: var(--secondary-color);
padding-left: 20px;
border-left: 2px solid var(--primary-color);
margin-top: 10px;
display: none;
}
.faq-question.active + .faq-answer {
display: block;
}
.form-check-input:checked {
background-color: var(--primary-color);
border-color: var(--primary-color);
}
.summary-item {
display: flex;
justify-content: space-between;
margin-bottom: 10px;
padding-bottom: 10px;
border-bottom: 1px solid #eee;
}
.summary-total {
font-weight: bold;
font-size: 1.2rem;
color: var(--primary-color);
}
</style> </head> <body> <!-- 导航栏 -->
<div id="navbar-container"></div>
<!-- 主要内容 --> <div class="container py-5"> <div class="management-header"> <div class="container"> <h1>土地托管服务</h1> <p class="lead">专业团队为您提供全方位的土地托管服务，让您的土地创造更高价值</p> <button class="btn btn-success btn-lg mt-3">立即申请托管</button> </div> </div> <!-- 服务介绍 --> <div class="row mb-5"> <div class="col-md-4"> <div class="service-card"> <img src="images/service1.jpg" alt="基础托管" class="service-image"> <div class="service-content"> <div class="service-title">基础托管</div> <div class="service-description">提供基本的土地管理和维护服务，确保土地保持良好状态</div> <div class="service-price">¥500/亩/年</div> <div class="service-features"> <div class="service-feature"> <i class="fas fa-check"></i> <span>基本土地维护</span> </div> <div class="service-feature"> <i class="fas fa-check"></i> <span>定期巡查</span> </div> <div class="service-feature"> <i class="fas fa-check"></i> <span>季节性整理</span> </div> <div class="service-feature"> <i class="fas fa-times text-secondary"></i> <span class="text-secondary">专业种植管理</span> </div> <div class="service-feature"> <i class="fas fa-times text-secondary"></i> <span class="text-secondary">收益分成</span> </div> </div> <button class="btn btn-outline-success w-100">选择此方案</button> </div> </div> </div> <div class="col-md-4"> <div class="service-card"> <img src="images/service2.jpg" alt="标准托管" class="service-image"> <div class="service-content"> <div class="service-title">标准托管</div> <div class="service-description">提供全面的土地管理和种植服务，包括作物选择和种植管理</div> <div class="service-price">¥1000/亩/年</div> <div class="service-features"> <div class="service-feature"> <i class="fas fa-check"></i> <span>基础托管所有服务</span> </div> <div class="service-feature"> <i class="fas fa-check"></i> <span>作物选择建议</span> </div> <div class="service-feature"> <i class="fas fa-check"></i> <span>种植全程管理</span> </div> <div class="service-feature"> <i class="fas fa-check"></i> <span>定期报告</span> </div> <div class="service-feature"> <i class="fas fa-times text-secondary"></i> <span class="text-secondary">收益分成</span> </div> </div> <button class="btn btn-success w-100">选择此方案</button> </div> </div> </div> <div class="col-md-4"> <div class="service-card"> <img src="images/service3.jpg" alt="高级托管" class="service-image"> <div class="service-content"> <div class="service-title">高级托管</div> <div class="service-description">提供最全面的土地托管服务，包括收益分成和市场营销</div> <div class="service-price">收益分成模式</div> <div class="service-features"> <div class="service-feature"> <i class="fas fa-check"></i> <span>标准托管所有服务</span> </div> <div class="service-feature"> <i class="fas fa-check"></i> <span>高端农产品定制</span> </div> <div class="service-feature"> <i class="fas fa-check"></i> <span>市场营销支持</span> </div> <div class="service-feature"> <i class="fas fa-check"></i> <span>产品品牌打造</span> </div> <div class="service-feature"> <i class="fas fa-check"></i> <span>收益分成（70/30）</span> </div> </div> <button class="btn btn-outline-success w-100">选择此方案</button> </div> </div> </div> </div> <!-- 托管流程 --> <div class="management-card"> <h3 class="mb-4 text-center">托管服务流程</h3> <div class="step-indicator"> <div class="step active"> <div class="step-number">1</div> <div class="step-title">提交申请</div> </div> <div class="step"> <div class="step-number">2</div> <div class="step-title">实地评估</div> </div> <div class="step"> <div class="step-number">3</div> <div class="step-title">签订合同</div> </div> <div class="step"> <div class="step-number">4</div> <div class="step-title">支付费用</div> </div> <div class="step"> <div class="step-number">5</div> <div class="step-title">开始托管</div> </div> </div> <div class="row text-center"> <div class="col-md-4 mb-4"> <div class="p-3"> <i class="fas fa-file-alt fa-3x text-success mb-3"></i> <h5>提交申请</h5> <p class="text-muted">填写托管申请表，提供土地基本信息和托管需求</p> </div> </div> <div class="col-md-4 mb-4"> <div class="p-3"> <i class="fas fa-search fa-3x text-success mb-3"></i> <h5>专业评估</h5> <p class="text-muted">我们的专业团队将实地考察，评估土地状况和潜力</p> </div> </div> <div class="col-md-4 mb-4"> <div class="p-3"> <i class="fas fa-handshake fa-3x text-success mb-3"></i> <h5>签约托管</h5> <p class="text-muted">双方签订托管合同，明确权责和服务内容</p> </div> </div> </div> </div> <!-- 申请表单 --> <div class="management-card"> <h3 class="mb-4">托管服务申请</h3> <form id="managementForm"> <div class="row mb-3"> <div class="col-md-6"> <label for="name" class="form-label">姓名</label> <input type="text" class="form-control" id="name" placeholder="请输入您的姓名" required> </div> <div class="col-md-6"> <label for="phone" class="form-label">联系电话</label> <input type="tel" class="form-control" id="phone" placeholder="请输入您的联系电话" required> </div> </div> <div class="mb-3"> <label for="email" class="form-label">电子邮箱</label> <input type="email" class="form-control" id="email" placeholder="请输入您的电子邮箱"> </div> <div class="row mb-3"> <div class="col-md-6"> <label for="landName" class="form-label">土地名称</label> <input type="text" class="form-control" id="landName" placeholder="请输入土地名称" required> </div> <div class="col-md-6"> <label for="landArea" class="form-label">土地面积（亩）</label> <input type="number" class="form-control" id="landArea" placeholder="请输入土地面积" required> </div> </div> <div class="row mb-3"> <div class="col-md-6"> <label for="landLocation" class="form-label">土地位置</label> <input type="text" class="form-control" id="landLocation" placeholder="请输入土地位置" required> </div> <div class="col-md-6"> <label for="landType" class="form-label">土地类型</label> <select class="form-select" id="landType" required> <option value="">请选择土地类型</option> <option value="paddy">水田</option> <option value="dry">旱地</option> <option value="orchard">果园</option> <option value="vegetable">菜地</option> <option value="tea">茶园</option> <option value="forest">林地</option> </select> </div> </div> <div class="mb-3"> <label class="form-label">托管服务类型</label> <div class="form-check mb-2"> <input class="form-check-input" type="radio" name="serviceType" id="basicService" value="basic"> <label class="form-check-label" for="basicService">
基础托管 (¥500/亩/年)
</label> </div> <div class="form-check mb-2"> <input class="form-check-input" type="radio" name="serviceType" id="standardService" value="standard" checked> <label class="form-check-label" for="standardService">
标准托管 (¥1000/亩/年)
</label> </div> <div class="form-check"> <input class="form-check-input" type="radio" name="serviceType" id="premiumService" value="premium"> <label class="form-check-label" for="premiumService">
高级托管 (收益分成模式)
</label> </div> </div> <div class="mb-3"> <label for="managementPeriod" class="form-label">托管期限</label> <select class="form-select" id="managementPeriod" required> <option value="">请选择托管期限</option> <option value="1">1年</option> <option value="3">3年</option> <option value="5">5年</option> <option value="10">10年</option> </select> </div> <div class="mb-3"> <label for="requirements" class="form-label">特殊需求或说明</label> <textarea class="form-control" id="requirements" rows="3" placeholder="请输入您的特殊需求或说明"></textarea> </div> <div class="mb-3 form-check"> <input type="checkbox" class="form-check-input" id="agreeTerms" required> <label class="form-check-label" for="agreeTerms">我已阅读并同意<a href="#">《托管服务协议》</a></label> </div> <div class="d-grid gap-2"> <button type="submit" class="btn btn-success">提交申请</button> </div> </form> </div> <div class="row"> <div class="col-md-6"> <!-- 客户评价 --> <div class="management-card"> <h3 class="mb-4">客户评价</h3> <div class="testimonial-card"> <div class="testimonial-content">
"我的土地托管给智慧农业平台已经两年了，他们的专业团队帮我管理得非常好，产量比以前提高了30%，而且省去了我很多麻烦。"
</div> <div class="testimonial-author"> <img src="images/avatar1.jpg" alt="用户头像" class="testimonial-avatar"> <div> <div class="testimonial-name">张先生</div> <div class="testimonial-role">浙江嘉兴 · 水稻种植户</div> </div> </div> </div> <div class="testimonial-card"> <div class="testimonial-content">
"选择高级托管服务是我做过的最明智的决定，不仅土地得到了专业管理，还帮我打造了自己的农产品品牌，收益比以前翻了一倍。"
</div> <div class="testimonial-author"> <img src="images/avatar2.jpg" alt="用户头像" class="testimonial-avatar"> <div> <div class="testimonial-name">李女士</div> <div class="testimonial-role">江苏苏州 · 果园主</div> </div> </div> </div> </div> </div> <div class="col-md-6"> <!-- 常见问题 --> <div class="management-card"> <h3 class="mb-4">常见问题</h3> <div class="faq-item"> <div class="faq-question"> <span>什么是土地托管服务？</span> <i class="fas fa-chevron-down"></i> </div> <div class="faq-answer">
土地托管服务是指土地所有者将土地委托给专业的农业服务机构进行管理和经营，包括土地维护、作物种植、病虫害防治等全方位服务，让土地创造更高的价值。
</div> </div> <div class="faq-item"> <div class="faq-question"> <span>托管期间我还能使用自己的土地吗？</span> <i class="fas fa-chevron-down"></i> </div> <div class="faq-answer">
托管期间，土地的所有权仍然属于您，但使用权暂时交由我们管理。您可以随时查看土地状况，但具体的农事活动将由我们的专业团队负责。
</div> </div> <div class="faq-item"> <div class="faq-question"> <span>如何计算收益分成？</span> <i class="fas fa-chevron-down"></i> </div> <div class="faq-answer">
高级托管服务采用收益分成模式，一般按照7:3的比例分配，即土地所有者获得70%的收益，我们获得30%的收益。具体比例可能会根据土地情况和投入成本进行调整。
</div> </div> <div class="faq-item"> <div class="faq-question"> <span>托管期限到期后怎么办？</span> <i class="fas fa-chevron-down"></i> </div> <div class="faq-answer">
托管期限到期后，您可以选择续签托管合同，也可以选择收回土地自行管理。如果您决定不再续签，我们会按照合同约定将土地恢复到约定状态后归还给您。
</div> </div> <div class="faq-item"> <div class="faq-question"> <span>托管服务费用如何支付？</span> <i class="fas fa-chevron-down"></i> </div> <div class="faq-answer">
基础托管和标准托管服务费用一般按年支付，可以选择一次性支付或分期支付。高级托管采用收益分成模式，不需要预先支付服务费用，而是在收获后按比例分配收益。
</div> </div> </div> </div> </div> </div> <!-- 页脚 -->
<div id="footer-container"></div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="js/login-config.js"></script>
<script src="js/components.js"></script>
<script>
// 使用 common.js 加载导航栏

// FAQ 问答切换
document.querySelectorAll('.faq-question').forEach(question => {
question.addEventListener('click', function() {
this.classList.toggle('active');
const icon = this.querySelector('i');
if (this.classList.contains('active')) {
icon.classList.remove('fa-chevron-down');
icon.classList.add('fa-chevron-up');
} else {
icon.classList.remove('fa-chevron-up');
icon.classList.add('fa-chevron-down');
}
});
});
// 表单提交
document.getElementById('managementForm').addEventListener('submit', function(e) {
e.preventDefault();
// 获取表单数据
const name = document.getElementById('name').value;
const phone = document.getElementById('phone').value;
const email = document.getElementById('email').value;
const landName = document.getElementById('landName').value;
const landArea = document.getElementById('landArea').value;
const landLocation = document.getElementById('landLocation').value;
const landType = document.getElementById('landType').value;
const serviceType = document.querySelector('input[name="serviceType"]:checked').value;
const managementPeriod = document.getElementById('managementPeriod').value;
const requirements = document.getElementById('requirements').value;
// 这里应该有实际的表单提交逻辑
console.log('表单提交:', {
name,
phone,
email,
landName,
landArea,
landLocation,
landType,
serviceType,
managementPeriod,
requirements
});
alert('您的托管申请已提交，我们的工作人员将尽快与您联系！');
this.reset();
});
</script> </body> </html>
