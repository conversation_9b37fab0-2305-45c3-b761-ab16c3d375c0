<!DOCTYPE html> <html lang="zh-CN"> <head> <meta charset="UTF-8"> <meta name="viewport" content="width=device-width, initial-scale=1.0"> <title>地图分区浏览 - 智慧农业平台</title> <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet"> <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet"> <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" /> <link rel="stylesheet" href="css/common.css"> <link rel="stylesheet" href="css/navbar.css">
<!-- 添加内联样式确保导航栏始终可见 -->
<style>
#navbar-container {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}
</style>
<style>
        :root {
            --primary-color: #28a745;
            --secondary-color: #6c757d;
            --light-bg: #f8f9fa;
            --card-shadow: 0 4px 6px rgba(0,0,0,0.1);
            --transition: all 0.3s ease;
        }
        body {
            background-color: var(--light-bg);
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }
        .map-container {
            height: 600px;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: var(--card-shadow);
            margin-bottom: 30px;
        }
        #map {
            height: 100%;
            width: 100%;
        }
        .region-selector {
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: var(--card-shadow);
            margin-bottom: 30px;
        }
        .land-stats {
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: var(--card-shadow);
            margin-bottom: 30px;
        }
        .stat-card {
            text-align: center;
            padding: 15px;
            border-radius: 8px;
            background-color: var(--light-bg);
            margin-bottom: 15px;
            transition: var(--transition);
        }
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--card-shadow);
        }
        .stat-icon {
            font-size: 2rem;
            color: var(--primary-color);
            margin-bottom: 10px;
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 5px;
        }
        .stat-label {
            font-size: 0.9rem;
            color: var(--secondary-color);
        }
        .land-type-filter {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;
        }
        .land-type-btn {
            padding: 8px 15px;
            border-radius: 20px;
            background-color: white;
            border: 1px solid #ddd;
            transition: var(--transition);
            cursor: pointer;
        }
        .land-type-btn.active {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
        .land-type-btn:hover:not(.active) {
            background-color: #f0f0f0;
        }
        .map-legend {
            background-color: white;
            padding: 15px;
            border-radius: 10px;
            box-shadow: var(--card-shadow);
            margin-top: 20px;
        }
        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 4px;
            margin-right: 10px;
        }

/* 确保Leaflet地图正确显示 */
.leaflet-container {
    height: 100%;
    width: 100%;
}</style> </head> <body>
<!-- 导航栏 - 使用内联方式直接加载，避免异步加载问题 -->
<div id="navbar-container">
<!-- 导航栏内容将在页面加载时由JavaScript填充，但即使JavaScript失败，这个容器也会保持可见 -->
</div>
<!-- 主要内容 --> <div class="container py-5"> <div class="row mb-4"> <div class="col-md-8"> <h2 class="mb-3">基地土地分布地图</h2> <p class="text-muted">通过地图查看全国各地的农业基地分布，点击标记查看详细信息</p> </div> <div class="col-md-4 text-md-end"> <button class="btn btn-success me-2"><i class="fas fa-heart me-1"></i> 收藏的地块</button> <button class="btn btn-outline-success"><i class="fas fa-filter me-1"></i> 筛选</button> </div> </div> <div class="row"> <div class="col-md-9"> <!-- 地图容器 --> <div class="map-container"> <div id="map"></div> </div> <!-- 土地类型筛选 --> <div class="land-type-filter"> <button class="land-type-btn active">全部</button> <button class="land-type-btn">水田</button> <button class="land-type-btn">旱地</button> <button class="land-type-btn">果园</button> <button class="land-type-btn">菜地</button> <button class="land-type-btn">茶园</button> <button class="land-type-btn">林地</button> </div> <!-- 地图图例 --> <div class="map-legend"> <h5 class="mb-3">地图图例</h5> <div class="row"> <div class="col-md-4"> <div class="legend-item"> <div class="legend-color" style="background-color: #4CAF50;"></div> <span>可租用</span> </div> </div> <div class="col-md-4"> <div class="legend-item"> <div class="legend-color" style="background-color: #FFC107;"></div> <span>预约中</span> </div> </div> <div class="col-md-4"> <div class="legend-item"> <div class="legend-color" style="background-color: #F44336;"></div> <span>已租用</span> </div> </div> </div> </div> </div> <div class="col-md-3"> <!-- 区域选择器 --> <div class="region-selector"> <h5 class="mb-3">区域选择</h5> <div class="mb-3"> <label for="province" class="form-label">省份</label> <select class="form-select" id="province"> <option value="">全部省份</option> <option value="zhejiang">浙江省</option> <option value="jiangsu">江苏省</option> <option value="shandong">山东省</option> <option value="anhui">安徽省</option> <option value="fujian">福建省</option> </select> </div> <div class="mb-3"> <label for="city" class="form-label">城市</label> <select class="form-select" id="city"> <option value="">全部城市</option> <option value="hangzhou">杭州市</option> <option value="ningbo">宁波市</option> <option value="wenzhou">温州市</option> <option value="shaoxing">绍兴市</option> </select> </div> <div class="mb-3"> <label for="area" class="form-label">面积范围</label> <select class="form-select" id="area"> <option value="">不限</option> <option value="0-10">0-10亩</option> <option value="10-50">10-50亩</option> <option value="50-100">50-100亩</option> <option value="100+">100亩以上</option> </select> </div> <div class="mb-3"> <label for="crop" class="form-label">适宜作物</label> <select class="form-select" id="crop"> <option value="">不限</option> <option value="rice">水稻</option> <option value="vegetable">蔬菜</option> <option value="fruit">水果</option> <option value="tea">茶叶</option> <option value="other">其他</option> </select> </div> <button class="btn btn-success w-100" onclick="filterMap()">应用筛选</button> </div> <!-- 土地统计 --> <div class="land-stats"> <h5 class="mb-3">土地统计</h5> <div class="stat-card"> <i class="fas fa-map-marked-alt stat-icon"></i> <div class="stat-value">1,245</div> <div class="stat-label">可租用地块</div> </div> <div class="stat-card"> <i class="fas fa-ruler stat-icon"></i> <div class="stat-value">12,680</div> <div class="stat-label">总面积（亩）</div> </div> <div class="stat-card"> <i class="fas fa-seedling stat-icon"></i> <div class="stat-value">28</div> <div class="stat-label">适宜作物种类</div> </div> </div> </div> </div> </div> <!-- 页脚 -->
<div id="footer-container"></div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="js/login-config.js"></script>
<script src="js/components.js"></script>
<script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
<script>

// 添加调试信息
console.log('land-map.html页面脚本开始执行');
document.addEventListener('DOMContentLoaded', function() {
    console.log('land-map.html DOMContentLoaded事件触发');
});
// 不再使用自定义的DOMContentLoaded事件处理程序，避免与components.js冲突
// 页面加载完成后初始化地图

// 页面加载完成后确保导航栏可见

// 页面加载完成后确保导航栏可见
window.onload = function() {
    // 确保导航栏可见
    const navbarContainer = document.getElementById('navbar-container');
    if (navbarContainer) {
        // 强制设置为可见
        navbarContainer.style.display = 'block';

        // 确保导航栏内容已加载
        if (!navbarContainer.innerHTML || navbarContainer.innerHTML.trim() === '') {
            // 如果导航栏内容为空，尝试重新加载
            if (typeof navbarHTML !== 'undefined') {
                navbarContainer.innerHTML = navbarHTML;

                // 重新应用登录状态和模块可见性
                if (typeof loadLoginState === 'function') loadLoginState();
                if (typeof updateNavbarDisplay === 'function') updateNavbarDisplay();
                if (typeof setActiveNavItem === 'function') setActiveNavItem();
                if (typeof setupLoginEvents === 'function') setupLoginEvents();
            }
        }
    }
};

        // 初始化地图
        var map = L.map('map').setView([35.8617, 104.1954], 4); // 中国中心点
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors'
        }).addTo(map);
        // 模拟数据 - 各省农业基地
        const provinces = [
            { name: "浙江省", center: [29.1416, 120.1536], count: 156 },
            { name: "江苏省", center: [32.0584, 118.7965], count: 143 },
            { name: "山东省", center: [36.6512, 117.1201], count: 187 },
            { name: "安徽省", center: [31.8612, 117.2835], count: 112 },
            { name: "福建省", center: [26.0745, 119.2965], count: 98 },
            { name: "广东省", center: [23.1317, 113.2663], count: 124 },
            { name: "江西省", center: [28.6757, 115.9047], count: 87 },
            { name: "河南省", center: [34.7655, 113.7532], count: 165 },
            { name: "湖北省", center: [30.5454, 114.3418], count: 103 },
            { name: "湖南省", center: [28.1127, 112.9834], count: 92 }
        ];
        // 添加省份标记
        provinces.forEach(province => {
            const marker = L.marker(province.center)
                .bindPopup(`
<div class="text-center"> <h6>${province.name}</h6> <p class="mb-1">基地数量: ${province.count}</p> <button class="btn btn-sm btn-success mt-2" onclick="showProvinceLands('${province.name}')">查看详情</button> </div>
                `)
                .addTo(map);
        });
        // 筛选地图
        function filterMap() {
            const province = document.getElementById('province').value;
            const city = document.getElementById('city').value;
            const area = document.getElementById('area').value;
            const crop = document.getElementById('crop').value;
            console.log(`筛选条件: 省份=${province}, 城市=${city}, 面积=${area}, 作物=${crop}`);
            // 这里应该有实际的筛选逻辑
            alert('筛选条件已应用，地图已更新');
        }
        // 显示省份土地
        function showProvinceLands(provinceName) {
            console.log(`显示${provinceName}的土地`);
            // 模拟点击后的行为 - 实际应用中应该是导航到详细页面或加载详细数据
            const province = provinces.find(p => p.name === provinceName);
            if (province) {
                map.setView(province.center, 7);
                // 模拟加载该省的详细地块
                setTimeout(() => {
                    alert(`已加载${provinceName}的${province.count}个地块数据`);
                }, 500);
            }
        }
        // 土地类型按钮点击事件
        document.querySelectorAll('.land-type-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.land-type-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                const landType = this.textContent;
                console.log(`选择的土地类型: ${landType}`);
                // 这里应该有实际的筛选逻辑
            });
        });
</script> </body> </html>
