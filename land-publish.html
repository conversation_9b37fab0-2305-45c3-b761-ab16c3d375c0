<!DOCTYPE html> <html lang="zh-CN"> <head> <meta charset="UTF-8"> <meta name="viewport" content="width=device-width, initial-scale=1.0"> <title>土地招租发布 - 智慧农业平台</title> <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet"> <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet"> <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" /> <link rel="stylesheet" href="css/common.css"> <link rel="stylesheet" href="css/navbar.css"> <style>
:root {
--primary-color: #28a745;
--secondary-color: #6c757d;
--light-bg: #f8f9fa;
--card-shadow: 0 4px 6px rgba(0,0,0,0.1);
--transition: all 0.3s ease;
}
body {
background-color: var(--light-bg);
font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}
.publish-header {
background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80');
background-size: cover;
background-position: center;
height: 200px;
color: white;
display: flex;
align-items: center;
margin-bottom: 30px;
border-radius: 10px;
}
.form-container {
background: white;
padding: 30px;
border-radius: 10px;
box-shadow: var(--card-shadow);
margin-bottom: 30px;
}
.map-container {
height: 300px;
border-radius: 10px;
overflow: hidden;
margin-bottom: 20px;
}
#map {
height: 100%;
width: 100%;
}
.upload-area {
border: 2px dashed #ddd;
border-radius: 10px;
padding: 30px;
text-align: center;
margin-bottom: 20px;
cursor: pointer;
transition: var(--transition);
}
.upload-area:hover {
border-color: var(--primary-color);
}
.upload-icon {
font-size: 3rem;
color: var(--primary-color);
margin-bottom: 15px;
}
.preview-images {
display: flex;
flex-wrap: wrap;
gap: 10px;
margin-top: 20px;
}
.preview-image {
width: 100px;
height: 100px;
object-fit: cover;
border-radius: 5px;
position: relative;
}
.remove-image {
position: absolute;
top: -10px;
right: -10px;
background: white;
border-radius: 50%;
width: 25px;
height: 25px;
display: flex;
align-items: center;
justify-content: center;
cursor: pointer;
box-shadow: 0 0 5px rgba(0,0,0,0.2);
}
.step-indicator {
display: flex;
justify-content: space-between;
margin-bottom: 30px;
}
.step {
flex: 1;
text-align: center;
padding: 15px;
position: relative;
}
.step::after {
content: '';
position: absolute;
top: 50%;
right: 0;
width: 100%;
height: 2px;
background-color: #ddd;
transform: translateY(-50%);
z-index: -1;
}
.step:last-child::after {
display: none;
}
.step-number {
width: 40px;
height: 40px;
background-color: #ddd;
border-radius: 50%;
display: flex;
align-items: center;
justify-content: center;
margin: 0 auto 10px;
font-weight: bold;
color: white;
position: relative;
z-index: 1;
}
.step.active .step-number {
background-color: var(--primary-color);
}
.step.completed .step-number {
background-color: var(--primary-color);
}
.step-title {
font-size: 0.9rem;
color: var(--secondary-color);
}
.step.active .step-title {
color: var(--primary-color);
font-weight: bold;
}

/* 确保Leaflet地图正确显示 */
.leaflet-container {
    height: 100%;
    width: 100%;
}</style> </head> <body> <!-- 导航栏 -->
<div id="navbar-container"></div>
<!-- 主要内容 --> <div class="container py-5"> <div class="publish-header"> <div class="container"> <h1>发布土地招租信息</h1> <p class="lead">将您的土地资源发布到平台，连接更多种植需求</p> </div> </div> <!-- 步骤指示器 --> <div class="step-indicator"> <div class="step active"> <div class="step-number">1</div> <div class="step-title">基本信息</div> </div> <div class="step"> <div class="step-number">2</div> <div class="step-title">土地详情</div> </div> <div class="step"> <div class="step-number">3</div> <div class="step-title">图片上传</div> </div> <div class="step"> <div class="step-number">4</div> <div class="step-title">租赁条件</div> </div> <div class="step"> <div class="step-number">5</div> <div class="step-title">提交审核</div> </div> </div> <!-- 表单容器 --> <div class="form-container"> <h4 class="mb-4">基本信息</h4> <form id="landPublishForm"> <div class="row mb-3"> <div class="col-md-6"> <label for="landName" class="form-label">土地名称</label> <input type="text" class="form-control" id="landName" placeholder="例如：浙江嘉兴水稻基地" required> </div> <div class="col-md-6"> <label for="landArea" class="form-label">土地面积（亩）</label> <input type="number" class="form-control" id="landArea" placeholder="请输入土地面积" required> </div> </div> <div class="row mb-3"> <div class="col-md-4"> <label for="province" class="form-label">省份</label> <select class="form-select" id="province" required> <option value="">请选择省份</option> <option value="zhejiang">浙江省</option> <option value="jiangsu">江苏省</option> <option value="anhui">安徽省</option> <option value="shandong">山东省</option> <option value="fujian">福建省</option> </select> </div> <div class="col-md-4"> <label for="city" class="form-label">城市</label> <select class="form-select" id="city" required> <option value="">请选择城市</option> <option value="hangzhou">杭州市</option> <option value="ningbo">宁波市</option> <option value="wenzhou">温州市</option> <option value="jiaxing">嘉兴市</option> <option value="shaoxing">绍兴市</option> </select> </div> <div class="col-md-4"> <label for="district" class="form-label">区/县</label> <select class="form-select" id="district" required> <option value="">请选择区/县</option> <option value="xihu">西湖区</option> <option value="shangcheng">上城区</option> <option value="xiacheng">下城区</option> <option value="jianggan">江干区</option> <option value="binjiang">滨江区</option> </select> </div> </div> <div class="mb-3"> <label for="address" class="form-label">详细地址</label> <input type="text" class="form-control" id="address" placeholder="请输入详细地址" required> </div> <div class="mb-3"> <label class="form-label">在地图上标记位置</label> <div class="map-container"> <div id="map"></div> </div> <div class="row"> <div class="col-md-6"> <label for="latitude" class="form-label">纬度</label> <input type="text" class="form-control" id="latitude" readonly> </div> <div class="col-md-6"> <label for="longitude" class="form-label">经度</label> <input type="text" class="form-control" id="longitude" readonly> </div> </div> </div> <div class="mb-3"> <label for="landType" class="form-label">土地类型</label> <select class="form-select" id="landType" required> <option value="">请选择土地类型</option> <option value="paddy">水田</option> <option value="dry">旱地</option> <option value="orchard">果园</option> <option value="vegetable">菜地</option> <option value="tea">茶园</option> <option value="forest">林地</option> </select> </div> <div class="mb-3"> <label for="suitableCrops" class="form-label">适宜作物</label> <select class="form-select" id="suitableCrops" multiple required> <option value="rice">水稻</option> <option value="wheat">小麦</option> <option value="corn">玉米</option> <option value="vegetable">蔬菜</option> <option value="fruit">水果</option> <option value="tea">茶叶</option> <option value="other">其他</option> </select> <div class="form-text">按住Ctrl键可多选</div> </div> <div class="d-flex justify-content-between mt-4"> <button type="button" class="btn btn-outline-secondary" onclick="window.history.back()">返回</button> <button type="button" class="btn btn-success" onclick="nextStep()">下一步</button> </div> </form> </div> </div> <!-- 页脚 -->
<div id="footer-container"></div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="js/login-config.js"></script>
<script src="js/components.js"></script>
<script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script> <script>


// 初始化地图
var map = L.map('map').setView([30.2741, 120.1551], 10);
L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
    attribution: '© OpenStreetMap contributors'
}).addTo(map);

// 添加可拖动的标记
var marker = L.marker([30.2741, 120.1551], {
    draggable: true
}).addTo(map);
// 更新坐标
function updateCoordinates(latlng) {
document.getElementById('latitude').value = latlng.lat.toFixed(6);
document.getElementById('longitude').value = latlng.lng.toFixed(6);
}
// 初始化坐标
updateCoordinates(marker.getLatLng());
// 拖动标记时更新坐标
marker.on('dragend', function(event) {
updateCoordinates(event.target.getLatLng());
});
// 点击地图时更新标记位置
map.on('click', function(event) {
marker.setLatLng(event.latlng);
updateCoordinates(event.latlng);
});
// 下一步
function nextStep() {
// 表单验证
const form = document.getElementById('landPublishForm');
if (form.checkValidity()) {
// 这里应该保存当前步骤的数据，然后跳转到下一步
alert('基本信息已保存，即将进入下一步');
// 实际应用中应该是跳转到下一步或显示下一步的表单
// window.location.href = 'land-publish-step2.html';
} else {
form.reportValidity();
}
}
</script> </body> </html>
