<!DOCTYPE html> <html lang="zh-CN"> <head> <meta charset="UTF-8"> <meta name="viewport" content="width=device-width, initial-scale=1.0"> <title>AI地块推荐 - 智慧农业平台</title> <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet"> <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet"> <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" /> <link rel="stylesheet" href="css/common.css"> <link rel="stylesheet" href="css/navbar.css"> <style>
:root {
--primary-color: #28a745;
--secondary-color: #6c757d;
--light-bg: #f8f9fa;
--card-shadow: 0 4px 6px rgba(0,0,0,0.1);
--transition: all 0.3s ease;
}
body {
background-color: var(--light-bg);
font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}
.recommendation-header {
background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80');
background-size: cover;
background-position: center;
height: 300px;
color: white;
display: flex;
align-items: center;
margin-bottom: 30px;
border-radius: 10px;
}
.recommendation-card {
background: white;
padding: 30px;
border-radius: 10px;
box-shadow: var(--card-shadow);
margin-bottom: 30px;
}
.map-container {
height: 500px;
border-radius: 10px;
overflow: hidden;
box-shadow: var(--card-shadow);
margin-bottom: 30px;
}
#map {
height: 500px;
width: 100%;
}
.preference-card {
background: white;
padding: 20px;
border-radius: 10px;
box-shadow: var(--card-shadow);
margin-bottom: 20px;
}
.preference-title {
font-weight: bold;
margin-bottom: 15px;
display: flex;
align-items: center;
}
.preference-icon {
color: var(--primary-color);
margin-right: 10px;
font-size: 1.2rem;
}
.preference-options {
display: flex;
flex-wrap: wrap;
gap: 10px;
margin-bottom: 15px;
}
.preference-option {
padding: 8px 15px;
border-radius: 20px;
background-color: var(--light-bg);
border: 1px solid #ddd;
cursor: pointer;
transition: var(--transition);
}
.preference-option.active {
background-color: var(--primary-color);
color: white;
border-color: var(--primary-color);
}
.preference-option:hover:not(.active) {
background-color: #f0f0f0;
}
.land-card {
background: white;
border-radius: 10px;
box-shadow: var(--card-shadow);
margin-bottom: 20px;
overflow: hidden;
transition: var(--transition);
}
.land-card:hover {
transform: translateY(-5px);
}
.land-image {
height: 150px;
object-fit: cover;
width: 100%;
}
.land-info {
padding: 15px;
}
.land-title {
font-size: 1.1rem;
font-weight: bold;
margin-bottom: 10px;
}
.land-price {
color: var(--primary-color);
font-weight: bold;
font-size: 1.2rem;
margin-bottom: 10px;
}
.land-features {
display: flex;
flex-wrap: wrap;
gap: 10px;
margin-bottom: 15px;
}
.land-feature {
background: var(--light-bg);
padding: 5px 10px;
border-radius: 15px;
font-size: 0.8rem;
color: var(--secondary-color);
}
.land-actions {
display: flex;
justify-content: space-between;
}
.match-score {
display: inline-block;
padding: 5px 10px;
border-radius: 15px;
font-size: 0.9rem;
font-weight: bold;
color: white;
background-color: var(--primary-color);
margin-bottom: 10px;
}
.ai-suggestion {
background-color: rgba(40, 167, 69, 0.1);
border-left: 4px solid var(--primary-color);
padding: 15px;
margin-bottom: 15px;
border-radius: 0 5px 5px 0;
}
.ai-suggestion-title {
font-weight: bold;
margin-bottom: 10px;
display: flex;
align-items: center;
}
.ai-suggestion-icon {
color: var(--primary-color);
margin-right: 10px;
font-size: 1.2rem;
}
.ai-suggestion-content {
color: var(--secondary-color);
}
.filter-section {
margin-bottom: 20px;
}
.filter-title {
font-weight: bold;
margin-bottom: 10px;
}
.range-slider {
margin-bottom: 15px;
}
.range-values {
display: flex;
justify-content: space-between;
margin-top: 5px;
font-size: 0.9rem;
color: var(--secondary-color);
}
</style> </head> <body> <!-- 导航栏 -->
<div id="navbar-container"></div>
<!-- 主要内容 --> <div class="container py-5"> <div class="recommendation-header"> <div class="container"> <h1>AI地块推荐</h1> <p class="lead">基于您的偏好和行为，智能推荐最适合您的农业地块</p> </div> </div> <div class="row"> <!-- 左侧偏好设置 --> <div class="col-md-4"> <div class="recommendation-card"> <h4 class="mb-4">您的偏好设置</h4> <!-- 种植目的 --> <div class="preference-card"> <div class="preference-title"> <i class="fas fa-bullseye preference-icon"></i> <span>种植目的</span> </div> <div class="preference-options"> <div class="preference-option active">商业种植</div> <div class="preference-option">自给自足</div> <div class="preference-option">休闲农业</div> <div class="preference-option">科研教学</div> </div> </div> <!-- 种植作物 --> <div class="preference-card"> <div class="preference-title"> <i class="fas fa-seedling preference-icon"></i> <span>种植作物</span> </div> <div class="preference-options"> <div class="preference-option active">水稻</div> <div class="preference-option">小麦</div> <div class="preference-option">蔬菜</div> <div class="preference-option">水果</div> <div class="preference-option">茶叶</div> <div class="preference-option">其他</div> </div> </div> <!-- 地理位置 --> <div class="preference-card"> <div class="preference-title"> <i class="fas fa-map-marker-alt preference-icon"></i> <span>地理位置</span> </div> <div class="preference-options"> <div class="preference-option active">浙江省</div> <div class="preference-option">江苏省</div> <div class="preference-option">安徽省</div> <div class="preference-option">山东省</div> <div class="preference-option">福建省</div> </div> </div> <!-- 面积范围 --> <div class="preference-card"> <div class="preference-title"> <i class="fas fa-ruler-combined preference-icon"></i> <span>面积范围</span> </div> <div class="filter-section"> <input type="range" class="form-range" min="0" max="1000" step="10" value="300" id="areaRange"> <div class="range-values"> <span>0亩</span> <span id="areaValue">300亩</span> <span>1000亩</span> </div> </div> </div> <!-- 预算范围 --> <div class="preference-card"> <div class="preference-title"> <i class="fas fa-coins preference-icon"></i> <span>预算范围</span> </div> <div class="filter-section"> <input type="range" class="form-range" min="500" max="5000" step="100" value="2000" id="budgetRange"> <div class="range-values"> <span>¥500/亩</span> <span id="budgetValue">¥2000/亩</span> <span>¥5000/亩</span> </div> </div> </div> <!-- 其他要求 --> <div class="preference-card"> <div class="preference-title"> <i class="fas fa-list-ul preference-icon"></i> <span>其他要求</span> </div> <div class="form-check mb-2"> <input class="form-check-input" type="checkbox" id="waterSupply" checked> <label class="form-check-label" for="waterSupply">
良好的水源供应
</label> </div> <div class="form-check mb-2"> <input class="form-check-input" type="checkbox" id="transportation" checked> <label class="form-check-label" for="transportation">
便利的交通条件
</label> </div> <div class="form-check mb-2"> <input class="form-check-input" type="checkbox" id="facilities"> <label class="form-check-label" for="facilities">
配套设施完善
</label> </div> <div class="form-check"> <input class="form-check-input" type="checkbox" id="organicCertified"> <label class="form-check-label" for="organicCertified">
有机认证土地
</label> </div> </div> <button class="btn btn-success w-100 mt-3" id="updatePreferences">更新偏好设置</button> </div> </div> <!-- 右侧推荐结果 --> <div class="col-md-8"> <!-- AI建议 --> <div class="ai-suggestion mb-4"> <div class="ai-suggestion-title"> <i class="fas fa-robot ai-suggestion-icon"></i> <span>AI智能建议</span> </div> <div class="ai-suggestion-content">
根据您的偏好分析，我们推荐您选择浙江省嘉兴市的水稻种植地块。该地区水源充足，气候适宜，土壤肥沃，非常适合水稻种植。同时，该地区交通便利，有完善的农业服务体系，可以为您提供全方位的支持。
</div> </div> <!-- 地图展示 --> <div class="map-container"> <div id="map"></div> </div> <!-- 推荐地块列表 --> <h4 class="mb-4">为您推荐的地块</h4> <div class="row"> <!-- 地块1 --> <div class="col-md-6"> <div class="land-card"> <img src="images/land1.jpg" alt="土地图片" class="land-image"> <div class="land-info"> <div class="match-score">匹配度 95%</div> <div class="land-title">浙江嘉兴水稻基地</div> <div class="land-price">¥2000/亩/年</div> <div class="land-features"> <span class="land-feature">水田</span> <span class="land-feature">300亩</span> <span class="land-feature">水稻</span> </div> <div class="land-actions"> <a href="land-detail.html?id=1" class="btn btn-sm btn-outline-success">查看详情</a> <button class="btn btn-sm btn-success">收藏</button> </div> </div> </div> </div> <!-- 地块2 --> <div class="col-md-6"> <div class="land-card"> <img src="images/land2.jpg" alt="土地图片" class="land-image"> <div class="land-info"> <div class="match-score">匹配度 90%</div> <div class="land-title">浙江湖州水稻基地</div> <div class="land-price">¥1800/亩/年</div> <div class="land-features"> <span class="land-feature">水田</span> <span class="land-feature">250亩</span> <span class="land-feature">水稻</span> </div> <div class="land-actions"> <a href="land-detail.html?id=2" class="btn btn-sm btn-outline-success">查看详情</a> <button class="btn btn-sm btn-success">收藏</button> </div> </div> </div> </div> <!-- 地块3 --> <div class="col-md-6"> <div class="land-card"> <img src="images/land3.jpg" alt="土地图片" class="land-image"> <div class="land-info"> <div class="match-score">匹配度 85%</div> <div class="land-title">江苏苏州水稻基地</div> <div class="land-price">¥2200/亩/年</div> <div class="land-features"> <span class="land-feature">水田</span> <span class="land-feature">350亩</span> <span class="land-feature">水稻</span> </div> <div class="land-actions"> <a href="land-detail.html?id=3" class="btn btn-sm btn-outline-success">查看详情</a> <button class="btn btn-sm btn-success">收藏</button> </div> </div> </div> </div> <!-- 地块4 --> <div class="col-md-6"> <div class="land-card"> <img src="images/land4.jpg" alt="土地图片" class="land-image"> <div class="land-info"> <div class="match-score">匹配度 80%</div> <div class="land-title">浙江绍兴水稻基地</div> <div class="land-price">¥1900/亩/年</div> <div class="land-features"> <span class="land-feature">水田</span> <span class="land-feature">280亩</span> <span class="land-feature">水稻</span> </div> <div class="land-actions"> <a href="land-detail.html?id=4" class="btn btn-sm btn-outline-success">查看详情</a> <button class="btn btn-sm btn-success">收藏</button> </div> </div> </div> </div> </div> <!-- 查看更多按钮 --> <div class="text-center mt-4"> <button class="btn btn-outline-success">查看更多推荐</button> </div> </div> </div> </div> <!-- 页脚 -->
<div id="footer-container"></div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="js/login-config.js"></script>
<script src="js/components.js"></script>

 <script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
<script>
// 初始化地图
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(function() {
        var map = L.map('map').setView([30.5, 120.5], 8);
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors'
        }).addTo(map);
// 添加推荐地块标记
const recommendedLands = [
{ id: 1, name: "浙江嘉兴水稻基地", position: [30.7741, 120.7551], match: 95 },
{ id: 2, name: "浙江湖州水稻基地", position: [30.8962, 120.0851], match: 90 },
{ id: 3, name: "江苏苏州水稻基地", position: [31.2989, 120.5853], match: 85 },
{ id: 4, name: "浙江绍兴水稻基地", position: [30.0301, 120.5801], match: 80 }
];
recommendedLands.forEach(land => {
const marker = L.marker(land.position)
.bindPopup(`
<div class="text-center"> <h6>${land.name}</h6> <p class="mb-1">匹配度: ${land.match}%</p> <a href="land-detail.html?id=${land.id}" class="btn btn-sm btn-success mt-2">查看详情</a> </div>
`)
.addTo(map);
});
// 偏好选项点击事件
document.querySelectorAll('.preference-option').forEach(option => {
option.addEventListener('click', function() {
const parent = this.parentElement;
parent.querySelectorAll('.preference-option').forEach(opt => {
opt.classList.remove('active');
});
this.classList.add('active');
});
});
// 范围滑块事件
document.getElementById('areaRange').addEventListener('input', function() {
document.getElementById('areaValue').textContent = this.value + '亩';
});
document.getElementById('budgetRange').addEventListener('input', function() {
document.getElementById('budgetValue').textContent = '¥' + this.value + '/亩';
});
// 更新偏好设置按钮点击事件
document.getElementById('updatePreferences').addEventListener('click', function() {
// 收集偏好设置
const purpose = document.querySelector('.preference-card:nth-child(1) .preference-option.active').textContent;
const crop = document.querySelector('.preference-card:nth-child(2) .preference-option.active').textContent;
const location = document.querySelector('.preference-card:nth-child(3) .preference-option.active').textContent;
const area = document.getElementById('areaRange').value;
const budget = document.getElementById('budgetRange').value;
const waterSupply = document.getElementById('waterSupply').checked;
const transportation = document.getElementById('transportation').checked;
const facilities = document.getElementById('facilities').checked;
const organicCertified = document.getElementById('organicCertified').checked;
console.log('更新偏好设置:', {
purpose,
crop,
location,
area,
budget,
waterSupply,
transportation,
facilities,
organicCertified
});
// 这里应该有实际的更新逻辑和重新推荐的逻辑
alert('偏好设置已更新，推荐结果已刷新');
});
    }, 500); // 添加延时，确保地图容器已经渲染
});

// 确保导航栏在页面加载完成后可见

// 页面加载完成后确保导航栏可见
window.onload = function() {
    // 确保导航栏可见
    const navbarContainer = document.getElementById('navbar-container');
    if (navbarContainer) {
        // 强制设置为可见
        navbarContainer.style.display = 'block';
        
        // 确保导航栏内容已加载
        if (!navbarContainer.innerHTML || navbarContainer.innerHTML.trim() === '') {
            // 如果导航栏内容为空，尝试重新加载
            if (typeof navbarHTML !== 'undefined') {
                navbarContainer.innerHTML = navbarHTML;
                
                // 重新应用登录状态和模块可见性
                if (typeof loadLoginState === 'function') loadLoginState();
                if (typeof updateNavbarDisplay === 'function') updateNavbarDisplay();
                if (typeof setActiveNavItem === 'function') setActiveNavItem();
                if (typeof setupLoginEvents === 'function') setupLoginEvents();
            }
        }
    }
};
</script> </body> </html>
