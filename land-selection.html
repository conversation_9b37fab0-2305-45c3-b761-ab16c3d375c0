<!DOCTYPE html> <html lang="zh-CN"> <head> <meta charset="UTF-8"> <meta name="viewport" content="width=device-width, initial-scale=1.0"> <title>在线选地 - 智慧农业平台</title> <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet"> <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet"> <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" /> <link rel="stylesheet" href="css/common.css"> <link rel="stylesheet" href="css/navbar.css"> <style>
:root {
--primary-color: #28a745;
--secondary-color: #6c757d;
--light-bg: #f8f9fa;
--card-shadow: 0 4px 6px rgba(0,0,0,0.1);
--transition: all 0.3s ease;
}
body {
background-color: var(--light-bg);
font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}
.selection-header {
background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80');
background-size: cover;
background-position: center;
height: 200px;
color: white;
display: flex;
align-items: center;
margin-bottom: 30px;
border-radius: 10px;
}
.map-container {
height: 500px;
border-radius: 10px;
overflow: hidden;
box-shadow: var(--card-shadow);
margin-bottom: 30px;
}
#map {
height: 500px;
width: 100%;
}
.filter-container {
background: white;
padding: 20px;
border-radius: 10px;
box-shadow: var(--card-shadow);
margin-bottom: 30px;
}
.land-card {
background: white;
border-radius: 10px;
box-shadow: var(--card-shadow);
margin-bottom: 20px;
overflow: hidden;
transition: var(--transition);
}
.land-card:hover {
transform: translateY(-5px);
}
.land-image {
height: 150px;
object-fit: cover;
width: 100%;
}
.land-info {
padding: 15px;
}
.land-title {
font-size: 1.1rem;
font-weight: bold;
margin-bottom: 10px;
}
.land-price {
color: var(--primary-color);
font-weight: bold;
font-size: 1.2rem;
margin-bottom: 10px;
}
.land-features {
display: flex;
flex-wrap: wrap;
gap: 10px;
margin-bottom: 15px;
}
.land-feature {
background: var(--light-bg);
padding: 5px 10px;
border-radius: 15px;
font-size: 0.8rem;
color: var(--secondary-color);
}
.land-actions {
display: flex;
justify-content: space-between;
}
.favorite-btn {
background: none;
border: none;
color: #dc3545;
cursor: pointer;
transition: var(--transition);
}
.favorite-btn:hover {
transform: scale(1.2);
}
.favorite-btn.active {
color: #dc3545;
}
.pagination-container {
display: flex;
justify-content: center;
margin-top: 30px;
}
.pagination {
display: flex;
list-style: none;
padding: 0;
margin: 0;
}
.page-item {
margin: 0 5px;
}
.page-link {
display: block;
padding: 8px 12px;
border-radius: 5px;
background: white;
color: var(--secondary-color);
text-decoration: none;
transition: var(--transition);
}
.page-link:hover {
background: var(--light-bg);
}
.page-item.active .page-link {
background: var(--primary-color);
color: white;
}
.favorites-container {
background: white;
padding: 20px;
border-radius: 10px;
box-shadow: var(--card-shadow);
margin-bottom: 30px;
}
.favorite-item {
display: flex;
align-items: center;
padding: 10px;
border-bottom: 1px solid #eee;
transition: var(--transition);
}
.favorite-item:hover {
background: var(--light-bg);
}
.favorite-item-image {
width: 60px;
height: 60px;
object-fit: cover;
border-radius: 5px;
margin-right: 15px;
}
.favorite-item-info {
flex: 1;
}
.favorite-item-title {
font-weight: bold;
margin-bottom: 5px;
}
.favorite-item-price {
color: var(--primary-color);
font-size: 0.9rem;
}
.favorite-item-remove {
color: #dc3545;
cursor: pointer;
transition: var(--transition);
}
.favorite-item-remove:hover {
transform: scale(1.2);
}
.compare-btn {
position: fixed;
bottom: 20px;
right: 20px;
background: var(--primary-color);
color: white;
padding: 10px 20px;
border-radius: 30px;
box-shadow: 0 5px 15px rgba(0,0,0,0.2);
z-index: 1000;
cursor: pointer;
transition: var(--transition);
}
.compare-btn:hover {
transform: translateY(-5px);
box-shadow: 0 8px 20px rgba(0,0,0,0.3);
}
.compare-count {
display: inline-block;
background: white;
color: var(--primary-color);
width: 24px;
height: 24px;
border-radius: 50%;
text-align: center;
line-height: 24px;
margin-left: 10px;
}
</style> </head> <body> <!-- 导航栏 -->
<div id="navbar-container"></div>
<!-- 主要内容 --> <div class="container py-5"> <div class="selection-header"> <div class="container"> <h1>在线选地</h1> <p class="lead">浏览并收藏您感兴趣的土地，进行比较和选择</p> </div> </div> <div class="row"> <div class="col-md-3"> <!-- 筛选条件 --> <div class="filter-container"> <h5 class="mb-3">筛选条件</h5> <div class="mb-3"> <label for="province" class="form-label">省份</label> <select class="form-select" id="province"> <option value="">全部省份</option> <option value="zhejiang">浙江省</option> <option value="jiangsu">江苏省</option> <option value="anhui">安徽省</option> <option value="shandong">山东省</option> <option value="fujian">福建省</option> </select> </div> <div class="mb-3"> <label for="city" class="form-label">城市</label> <select class="form-select" id="city"> <option value="">全部城市</option> <option value="hangzhou">杭州市</option> <option value="ningbo">宁波市</option> <option value="wenzhou">温州市</option> <option value="jiaxing">嘉兴市</option> <option value="shaoxing">绍兴市</option> </select> </div> <div class="mb-3"> <label for="landType" class="form-label">土地类型</label> <select class="form-select" id="landType"> <option value="">全部类型</option> <option value="paddy">水田</option> <option value="dry">旱地</option> <option value="orchard">果园</option> <option value="vegetable">菜地</option> <option value="tea">茶园</option> <option value="forest">林地</option> </select> </div> <div class="mb-3"> <label for="areaRange" class="form-label">面积范围</label> <select class="form-select" id="areaRange"> <option value="">不限</option> <option value="0-10">0-10亩</option> <option value="10-50">10-50亩</option> <option value="50-100">50-100亩</option> <option value="100+">100亩以上</option> </select> </div> <div class="mb-3"> <label for="priceRange" class="form-label">价格范围</label> <select class="form-select" id="priceRange"> <option value="">不限</option> <option value="0-1000">0-1000元/亩/年</option> <option value="1000-2000">1000-2000元/亩/年</option> <option value="2000-3000">2000-3000元/亩/年</option> <option value="3000+">3000元以上/亩/年</option> </select> </div> <div class="mb-3"> <label for="suitableCrops" class="form-label">适宜作物</label> <select class="form-select" id="suitableCrops"> <option value="">不限</option> <option value="rice">水稻</option> <option value="wheat">小麦</option> <option value="corn">玉米</option> <option value="vegetable">蔬菜</option> <option value="fruit">水果</option> <option value="tea">茶叶</option> </select> </div> <button class="btn btn-success w-100" onclick="applyFilters()">应用筛选</button> </div> <!-- 收藏的土地 --> <div class="favorites-container"> <h5 class="mb-3">我的收藏</h5> <div id="favoritesList"> <div class="favorite-item"> <img src="images/land1.jpg" alt="土地图片" class="favorite-item-image"> <div class="favorite-item-info"> <div class="favorite-item-title">浙江嘉兴水稻基地</div> <div class="favorite-item-price">¥2000/亩/年</div> </div> <i class="fas fa-times favorite-item-remove" onclick="removeFavorite(1)"></i> </div> <div class="favorite-item"> <img src="images/land2.jpg" alt="土地图片" class="favorite-item-image"> <div class="favorite-item-info"> <div class="favorite-item-title">江苏苏州蔬菜基地</div> <div class="favorite-item-price">¥3000/亩/年</div> </div> <i class="fas fa-times favorite-item-remove" onclick="removeFavorite(2)"></i> </div> </div> <div class="text-center mt-3"> <button class="btn btn-outline-success btn-sm" onclick="clearFavorites()">清空收藏</button> </div> </div> </div> <div class="col-md-9"> <!-- 地图视图 --> <div class="map-container"> <div id="map"></div> </div> <!-- 土地列表 --> <div class="row" id="landList"> <!-- 土地卡片1 --> <div class="col-md-4"> <div class="land-card"> <img src="images/land1.jpg" alt="土地图片" class="land-image"> <div class="land-info"> <div class="land-title">浙江嘉兴水稻基地</div> <div class="land-price">¥2000/亩/年</div> <div class="land-features"> <span class="land-feature">水田</span> <span class="land-feature">300亩</span> <span class="land-feature">水稻</span> </div> <div class="land-actions"> <a href="land-detail.html?id=1" class="btn btn-sm btn-outline-success">查看详情</a> <button class="favorite-btn active" onclick="toggleFavorite(this, 1)"> <i class="fas fa-heart"></i> </button> </div> </div> </div> </div> <!-- 土地卡片2 --> <div class="col-md-4"> <div class="land-card"> <img src="images/land2.jpg" alt="土地图片" class="land-image"> <div class="land-info"> <div class="land-title">江苏苏州蔬菜基地</div> <div class="land-price">¥3000/亩/年</div> <div class="land-features"> <span class="land-feature">菜地</span> <span class="land-feature">150亩</span> <span class="land-feature">蔬菜</span> </div> <div class="land-actions"> <a href="land-detail.html?id=2" class="btn btn-sm btn-outline-success">查看详情</a> <button class="favorite-btn active" onclick="toggleFavorite(this, 2)"> <i class="fas fa-heart"></i> </button> </div> </div> </div> </div> <!-- 土地卡片3 --> <div class="col-md-4"> <div class="land-card"> <img src="images/land3.jpg" alt="土地图片" class="land-image"> <div class="land-info"> <div class="land-title">安徽合肥小麦基地</div> <div class="land-price">¥1800/亩/年</div> <div class="land-features"> <span class="land-feature">旱地</span> <span class="land-feature">500亩</span> <span class="land-feature">小麦</span> </div> <div class="land-actions"> <a href="land-detail.html?id=3" class="btn btn-sm btn-outline-success">查看详情</a> <button class="favorite-btn" onclick="toggleFavorite(this, 3)"> <i class="fas fa-heart"></i> </button> </div> </div> </div> </div> <!-- 土地卡片4 --> <div class="col-md-4"> <div class="land-card"> <img src="images/land4.jpg" alt="土地图片" class="land-image"> <div class="land-info"> <div class="land-title">浙江杭州蔬菜基地</div> <div class="land-price">¥3500/亩/年</div> <div class="land-features"> <span class="land-feature">菜地</span> <span class="land-feature">80亩</span> <span class="land-feature">蔬菜</span> </div> <div class="land-actions"> <a href="land-detail.html?id=4" class="btn btn-sm btn-outline-success">查看详情</a> <button class="favorite-btn" onclick="toggleFavorite(this, 4)"> <i class="fas fa-heart"></i> </button> </div> </div> </div> </div> <!-- 土地卡片5 --> <div class="col-md-4"> <div class="land-card"> <img src="images/land5.jpg" alt="土地图片" class="land-image"> <div class="land-info"> <div class="land-title">江苏南京水稻基地</div> <div class="land-price">¥2200/亩/年</div> <div class="land-features"> <span class="land-feature">水田</span> <span class="land-feature">600亩</span> <span class="land-feature">水稻</span> </div> <div class="land-actions"> <a href="land-detail.html?id=5" class="btn btn-sm btn-outline-success">查看详情</a> <button class="favorite-btn" onclick="toggleFavorite(this, 5)"> <i class="fas fa-heart"></i> </button> </div> </div> </div> </div> <!-- 土地卡片6 --> <div class="col-md-4"> <div class="land-card"> <img src="images/land6.jpg" alt="土地图片" class="land-image"> <div class="land-info"> <div class="land-title">安徽芜湖蔬菜基地</div> <div class="land-price">¥2800/亩/年</div> <div class="land-features"> <span class="land-feature">菜地</span> <span class="land-feature">200亩</span> <span class="land-feature">蔬菜</span> </div> <div class="land-actions"> <a href="land-detail.html?id=6" class="btn btn-sm btn-outline-success">查看详情</a> <button class="favorite-btn" onclick="toggleFavorite(this, 6)"> <i class="fas fa-heart"></i> </button> </div> </div> </div> </div> </div> <!-- 分页 --> <div class="pagination-container"> <ul class="pagination"> <li class="page-item"><a class="page-link" href="#"><i class="fas fa-chevron-left"></i></a></li> <li class="page-item active"><a class="page-link" href="#">1</a></li> <li class="page-item"><a class="page-link" href="#">2</a></li> <li class="page-item"><a class="page-link" href="#">3</a></li> <li class="page-item"><a class="page-link" href="#"><i class="fas fa-chevron-right"></i></a></li> </ul> </div> </div> </div> </div> <!-- 比较按钮 --> <div class="compare-btn" onclick="compareLands()"> <i class="fas fa-exchange-alt"></i> 比较选中土地 <span class="compare-count">2</span> </div> <!-- 页脚 -->
<div id="footer-container"></div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="js/login-config.js"></script>
<script src="js/components.js"></script>
<script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script> <script>
// 初始化页面
document.addEventListener('DOMContentLoaded', function() {
    // 设置当前页面的导航链接为激活状态
    setActiveNavItem();
});

// 初始化地图
var map = L.map('map').setView([31.5, 120], 7);
L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
attribution: '© OpenStreetMap contributors'
}).addTo(map);
// 模拟数据 - 土地位置
const lands = [
{ id: 1, name: "浙江嘉兴水稻基地", position: [30.7741, 120.7551], price: 2000, area: 300, type: "水田", crop: "水稻" },
{ id: 2, name: "江苏苏州蔬菜基地", position: [31.2989, 120.5853], price: 3000, area: 150, type: "菜地", crop: "蔬菜" },
{ id: 3, name: "安徽合肥小麦基地", position: [31.8206, 117.2272], price: 1800, area: 500, type: "旱地", crop: "小麦" },
{ id: 4, name: "浙江杭州蔬菜基地", position: [30.2741, 120.1551], price: 3500, area: 80, type: "菜地", crop: "蔬菜" },
{ id: 5, name: "江苏南京水稻基地", position: [32.0584, 118.7965], price: 2200, area: 600, type: "水田", crop: "水稻" },
{ id: 6, name: "安徽芜湖蔬菜基地", position: [31.3339, 118.3752], price: 2800, area: 200, type: "菜地", crop: "蔬菜" }
];
// 添加标记点
lands.forEach(land => {
L.marker(land.position)
.bindPopup(`
<div class="text-center"> <h6>${land.name}</h6> <p class="mb-1">¥${land.price}/亩/年</p> <p class="mb-1">面积：${land.area}亩</p> <a href="land-detail.html?id=${land.id}" class="btn btn-sm btn-success mt-2">查看详情</a> </div>
`)
.addTo(map);
});
// 应用筛选
function applyFilters() {
const province = document.getElementById('province').value;
const city = document.getElementById('city').value;
const landType = document.getElementById('landType').value;
const areaRange = document.getElementById('areaRange').value;
const priceRange = document.getElementById('priceRange').value;
const suitableCrops = document.getElementById('suitableCrops').value;
console.log(`筛选条件: 省份=${province}, 城市=${city}, 土地类型=${landType}, 面积=${areaRange}, 价格=${priceRange}, 作物=${suitableCrops}`);
// 这里应该有实际的筛选逻辑
alert('筛选条件已应用，土地列表已更新');
}
// 切换收藏状态
function toggleFavorite(button, landId) {
button.classList.toggle('active');
if (button.classList.contains('active')) {
console.log(`添加收藏: 土地ID=${landId}`);
// 这里应该有实际的添加收藏逻辑
} else {
console.log(`取消收藏: 土地ID=${landId}`);
// 这里应该有实际的取消收藏逻辑
}
// 更新收藏计数
updateCompareCount();
}
// 移除收藏
function removeFavorite(landId) {
console.log(`移除收藏: 土地ID=${landId}`);
// 这里应该有实际的移除收藏逻辑
// 更新收藏计数
updateCompareCount();
}
// 清空收藏
function clearFavorites() {
console.log('清空所有收藏');
// 这里应该有实际的清空收藏逻辑
// 更新收藏计数
updateCompareCount();
}
// 更新比较计数
function updateCompareCount() {
const count = document.querySelectorAll('.favorite-btn.active').length;
document.querySelector('.compare-count').textContent = count;
}
// 比较土地
function compareLands() {
console.log('比较选中的土地');
// 这里应该有实际的比较逻辑，如跳转到比较页面
alert('即将跳转到土地比较页面');
}

// 确保导航栏在页面加载完成后可见

// 页面加载完成后确保导航栏可见
window.onload = function() {
    // 确保导航栏可见
    const navbarContainer = document.getElementById('navbar-container');
    if (navbarContainer) {
        // 强制设置为可见
        navbarContainer.style.display = 'block';
        
        // 确保导航栏内容已加载
        if (!navbarContainer.innerHTML || navbarContainer.innerHTML.trim() === '') {
            // 如果导航栏内容为空，尝试重新加载
            if (typeof navbarHTML !== 'undefined') {
                navbarContainer.innerHTML = navbarHTML;
                
                // 重新应用登录状态和模块可见性
                if (typeof loadLoginState === 'function') loadLoginState();
                if (typeof updateNavbarDisplay === 'function') updateNavbarDisplay();
                if (typeof setActiveNavItem === 'function') setActiveNavItem();
                if (typeof setupLoginEvents === 'function') setupLoginEvents();
            }
        }
    }
};
</script> </body> </html>
