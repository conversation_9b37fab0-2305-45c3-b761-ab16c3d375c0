<!DOCTYPE html> <html lang="zh-CN"> <head> <meta charset="UTF-8"> <meta name="viewport" content="width=device-width, initial-scale=1.0"> <title>地主专区 - 智慧农业平台</title> <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet"> <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet"> <link rel="stylesheet" href="css/common.css"> <link rel="stylesheet" href="css/navbar.css"> <style>
.landlord-header {
background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80');
background-size: cover;
background-position: center;
height: 300px;
color: white;
display: flex;
align-items: center;
justify-content: center;
text-align: center;
}
.feature-card {
transition: transform 0.3s;
margin-bottom: 20px;
box-shadow: 0 0 10px rgba(0,0,0,0.1);
}
.feature-card:hover {
transform: translateY(-5px);
}
.feature-icon {
font-size: 2.5rem;
color: var(--primary-color);
margin-bottom: 15px;
}
.benefit-item {
padding: 15px;
border-radius: 10px;
background: #f8f9fa;
margin-bottom: 15px;
}
.benefit-item i {
color: var(--primary-color);
margin-right: 10px;
}
.process-steps {
display: flex;
justify-content: space-between;
align-items: center;
margin: 40px 0;
position: relative;
}
.process-step {
flex: 1;
text-align: center;
position: relative;
z-index: 1;
}
.process-step:not(:last-child)::after {
content: '';
position: absolute;
top: 50%;
right: -50%;
width: 100%;
height: 2px;
background: var(--primary-color);
transform: translateY(-50%);
z-index: 0;
}
.process-step:not(:last-child)::before {
content: '→';
position: absolute;
top: 50%;
right: -50%;
transform: translateY(-50%);
color: var(--primary-color);
font-size: 24px;
z-index: 2;
}
.step-icon {
width: 80px;
height: 80px;
background: var(--primary-color);
border-radius: 50%;
display: flex;
align-items: center;
justify-content: center;
margin: 0 auto 20px;
color: white;
font-size: 2rem;
box-shadow: 0 4px 6px rgba(40, 167, 69, 0.2);
transition: var(--transition);
}
.process-step:hover .step-icon {
transform: translateY(-5px);
box-shadow: 0 8px 15px rgba(40, 167, 69, 0.3);
}
.step-title {
font-size: 1.2rem;
font-weight: 600;
margin-bottom: 10px;
color: var(--secondary-color);
}
.step-desc {
color: var(--secondary-color);
font-size: 0.9rem;
line-height: 1.5;
}
.navbar-brand {
display: flex;
align-items: center;
gap: 10px;
}
.brand-logo {
width: 40px;
height: 40px;
}
.brand-logo img {
width: 100%;
min-height: auto;
object-fit: contain;
}
</style> </head> <body> <!-- 导航栏容器 --> <div id="navbar-container"></div> <!-- 导航栏 -->
<div id="navbar-container"></div>
<!-- 主要内容 --> <div class="container py-5"> <!-- 特色功能 --> <div class="row mb-5"> <div class="col-md-4"> <div class="card feature-card"> <div class="card-body text-center"> <i class="fas fa-chart-line feature-icon"></i> <h4>收益分析</h4> <p>专业的收益分析工具，帮助您了解土地价值</p> </div> </div> </div> <div class="col-md-4"> <div class="card feature-card"> <div class="card-body text-center"> <i class="fas fa-tractor feature-icon"></i> <h4>种植管理</h4> <p>科学的种植方案，提高土地利用率</p> </div> </div> </div> <div class="col-md-4"> <div class="card feature-card"> <div class="card-body text-center"> <i class="fas fa-handshake feature-icon"></i> <h4>合作对接</h4> <p>快速对接优质农户，实现双赢</p> </div> </div> </div> </div> <!-- 收益分析 --> <div class="row mb-5"> <div class="col-md-6"> <h3 class="mb-4">收益分析</h3> <div class="benefit-item"> <i class="fas fa-calculator"></i> <span>年化收益率：8-12%</span> </div> <div class="benefit-item"> <i class="fas fa-chart-pie"></i> <span>土地增值率：5-8%</span> </div> <div class="benefit-item"> <i class="fas fa-coins"></i> <span>额外收益：种植补贴、政策优惠</span> </div> </div> <div class="col-md-6"> <div class="card"> <div class="card-body"> <h5 class="card-title">收益趋势图</h5> <canvas id="revenueChart"></canvas> </div> </div> </div> </div> <!-- 合作流程 --> <div class="row mb-5"> <div class="col-12"> <h3 class="mb-4">合作流程</h3> <div class="process-steps"> <div class="process-step"> <div class="step-icon"> <i class="fas fa-handshake"></i> </div> <h4 class="step-title">初步洽谈</h4> <p class="step-desc">了解土地情况，确定合作意向</p> </div> <div class="process-step"> <div class="step-icon"> <i class="fas fa-file-contract"></i> </div> <h4 class="step-title">签订协议</h4> <p class="step-desc">明确双方权利义务，签订合作协议</p> </div> <div class="process-step"> <div class="step-icon"> <i class="fas fa-tractor"></i> </div> <h4 class="step-title">土地开发</h4> <p class="step-desc">进行土地整理，开展农业生产</p> </div> <div class="process-step"> <div class="step-icon"> <i class="fas fa-chart-line"></i> </div> <h4 class="step-title">收益分配</h4> <p class="step-desc">按协议约定，进行收益分配</p> </div> </div> </div> </div> <!-- 常见问题 --> <div class="row"> <div class="col-12"> <h3 class="mb-4">常见问题</h3> <div class="accordion" id="faqAccordion"> <div class="accordion-item"> <h2 class="accordion-header"> <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
如何成为地主？
</button> </h2> <div id="faq1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion"> <div class="accordion-body">
完成注册后，提交土地证明文件，通过审核即可成为地主。
</div> </div> </div> <div class="accordion-item"> <h2 class="accordion-header"> <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
收益如何计算？
</button> </h2> <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion"> <div class="accordion-body">
收益包括土地租金、种植收益分成、政策补贴等。
</div> </div> </div> <div class="accordion-item"> <h2 class="accordion-header"> <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
如何保障权益？
</button> </h2> <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion"> <div class="accordion-body">
平台提供法律保障，签订正式合同，全程监控种植过程。
</div> </div> </div> </div> </div> </div> </div> <!-- 页脚 -->
<div id="footer-container"></div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="js/login-config.js"></script>
<script src="js/components.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script> <script>
// 收益趋势图
const ctx = document.getElementById('revenueChart').getContext('2d');
const revenueChart = new Chart(ctx, {
type: 'line',
data: {
labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
datasets: [{
label: '月收益',
data: [5000, 6000, 5500, 7000, 6500, 8000],
borderColor: 'rgb(75, 192, 192)',
tension: 0.1
}]
},
options: {
responsive: true,
scales: {
y: {
beginAtZero: true
}
}
}
});
// 设置当前页面的导航链接为激活状态
document.querySelectorAll('.nav-link').forEach(link => {
const page = link.getAttribute('data-page');
if (page === 'distribution') {
link.classList.add('active');
}
});
</script> </body> </html> 