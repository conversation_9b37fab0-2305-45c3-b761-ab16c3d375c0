<!DOCTYPE html> <html lang="zh-CN"> <head> <meta charset="UTF-8"> <meta name="viewport" content="width=device-width, initial-scale=1.0"> <title>直播带货 - 智慧农业平台</title> <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet"> <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet"> <link rel="stylesheet" href="css/common.css"> <link rel="stylesheet" href="css/navbar.css"> <style>
:root {
--primary-color: #28a745;
--secondary-color: #6c757d;
--light-bg: #f8f9fa;
--card-shadow: 0 4px 6px rgba(0,0,0,0.1);
--transition: all 0.3s ease;
}
body {
background-color: var(--light-bg);
font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}
.streaming-header {
background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80');
background-size: cover;
background-position: center;
height: 300px;
color: white;
display: flex;
align-items: center;
margin-bottom: 30px;
border-radius: 10px;
}
.streaming-card {
background: white;
padding: 30px;
border-radius: 10px;
box-shadow: var(--card-shadow);
margin-bottom: 30px;
}
.live-container {
background: #000;
border-radius: 10px;
overflow: hidden;
position: relative;
margin-bottom: 20px;
}
.live-video {
width: 100%;
aspect-ratio: 16 / 9;
object-fit: cover;
}
.live-badge {
position: absolute;
top: 15px;
left: 15px;
background-color: #dc3545;
color: white;
padding: 5px 10px;
border-radius: 5px;
font-weight: bold;
display: flex;
align-items: center;
}
.live-badge i {
margin-right: 5px;
}
.live-viewers {
position: absolute;
top: 15px;
right: 15px;
background-color: rgba(0, 0, 0, 0.5);
color: white;
padding: 5px 10px;
border-radius: 5px;
font-size: 0.9rem;
}
.live-info {
padding: 20px;
background: #f8f9fa;
border-radius: 0 0 10px 10px;
}
.live-title {
font-size: 1.2rem;
font-weight: bold;
margin-bottom: 10px;
}
.live-host {
display: flex;
align-items: center;
margin-bottom: 15px;
}
.host-avatar {
width: 40px;
height: 40px;
border-radius: 50%;
object-fit: cover;
margin-right: 10px;
}
.host-name {
font-weight: bold;
}
.host-followers {
font-size: 0.9rem;
color: var(--secondary-color);
margin-left: 10px;
}
.follow-btn {
margin-left: auto;
background-color: var(--primary-color);
color: white;
border: none;
padding: 5px 15px;
border-radius: 20px;
cursor: pointer;
transition: var(--transition);
}
.follow-btn:hover {
background-color: #218838;
}
.follow-btn.followed {
background-color: var(--secondary-color);
}
.live-description {
margin-bottom: 15px;
color: var(--secondary-color);
}
.live-tags {
display: flex;
flex-wrap: wrap;
gap: 10px;
margin-bottom: 15px;
}
.live-tag {
background: var(--light-bg);
padding: 5px 10px;
border-radius: 15px;
font-size: 0.8rem;
color: var(--secondary-color);
}
.chat-container {
background: white;
border-radius: 10px;
box-shadow: var(--card-shadow);
height: 400px;
display: flex;
flex-direction: column;
}
.chat-header {
padding: 15px;
border-bottom: 1px solid #eee;
font-weight: bold;
}
.chat-messages {
flex: 1;
overflow-y: auto;
padding: 15px;
}
.chat-message {
margin-bottom: 15px;
}
.message-sender {
font-weight: bold;
margin-bottom: 5px;
}
.message-content {
background: var(--light-bg);
padding: 10px;
border-radius: 10px;
display: inline-block;
}
.chat-input {
padding: 15px;
border-top: 1px solid #eee;
display: flex;
}
.chat-input input {
flex: 1;
padding: 10px;
border: 1px solid #ddd;
border-radius: 20px;
margin-right: 10px;
}
.chat-input button {
background-color: var(--primary-color);
color: white;
border: none;
padding: 10px 15px;
border-radius: 20px;
cursor: pointer;
transition: var(--transition);
}
.chat-input button:hover {
background-color: #218838;
}
.product-card {
background: white;
border-radius: 10px;
box-shadow: var(--card-shadow);
margin-bottom: 20px;
overflow: hidden;
transition: var(--transition);
}
.product-card:hover {
transform: translateY(-5px);
}
.product-image {
height: 150px;
object-fit: cover;
width: 100%;
}
.product-info {
padding: 15px;
}
.product-title {
font-size: 1rem;
font-weight: bold;
margin-bottom: 5px;
}
.product-price {
color: #dc3545;
font-weight: bold;
font-size: 1.1rem;
margin-bottom: 10px;
}
.product-original-price {
text-decoration: line-through;
color: var(--secondary-color);
font-size: 0.9rem;
margin-left: 5px;
}
.product-sales {
font-size: 0.9rem;
color: var(--secondary-color);
margin-bottom: 10px;
}
.product-actions {
display: flex;
justify-content: space-between;
}
.schedule-card {
background: white;
border-radius: 10px;
box-shadow: var(--card-shadow);
margin-bottom: 20px;
overflow: hidden;
transition: var(--transition);
}
.schedule-card:hover {
transform: translateY(-5px);
}
.schedule-image {
height: 200px;
object-fit: cover;
width: 100%;
}
.schedule-info {
padding: 15px;
}
.schedule-title {
font-size: 1.1rem;
font-weight: bold;
margin-bottom: 10px;
}
.schedule-time {
color: var(--primary-color);
font-weight: bold;
margin-bottom: 10px;
display: flex;
align-items: center;
}
.schedule-time i {
margin-right: 5px;
}
.schedule-description {
color: var(--secondary-color);
margin-bottom: 15px;
}
.schedule-actions {
display: flex;
justify-content: space-between;
}
.reminder-btn {
background-color: var(--primary-color);
color: white;
border: none;
padding: 5px 15px;
border-radius: 20px;
cursor: pointer;
transition: var(--transition);
display: flex;
align-items: center;
}
.reminder-btn i {
margin-right: 5px;
}
.reminder-btn:hover {
background-color: #218838;
}
.reminder-btn.reminded {
background-color: var(--secondary-color);
}
.farm-view-card {
background: white;
border-radius: 10px;
box-shadow: var(--card-shadow);
margin-bottom: 20px;
overflow: hidden;
}
.farm-view-title {
padding: 15px;
font-weight: bold;
border-bottom: 1px solid #eee;
}
.farm-view-container {
position: relative;
}
.farm-view-image {
width: 100%;
height: 300px;
object-fit: cover;
}
.farm-view-controls {
position: absolute;
bottom: 15px;
left: 0;
right: 0;
display: flex;
justify-content: center;
gap: 10px;
}
.farm-view-control {
background-color: rgba(0, 0, 0, 0.5);
color: white;
border: none;
width: 40px;
height: 40px;
border-radius: 50%;
display: flex;
align-items: center;
justify-content: center;
cursor: pointer;
transition: var(--transition);
}
.farm-view-control:hover {
background-color: rgba(0, 0, 0, 0.7);
}
.farm-view-locations {
padding: 15px;
display: flex;
overflow-x: auto;
gap: 10px;
}
.farm-location {
min-width: 120px;
text-align: center;
cursor: pointer;
transition: var(--transition);
}
.farm-location:hover {
transform: translateY(-5px);
}
.farm-location-image {
width: 100px;
height: 70px;
object-fit: cover;
border-radius: 5px;
margin-bottom: 5px;
}
.farm-location-name {
font-size: 0.9rem;
}
.tab-container {
margin-bottom: 20px;
}
.nav-tabs {
border-bottom: 1px solid #dee2e6;
margin-bottom: 20px;
}
.nav-tabs .nav-link {
margin-bottom: -1px;
border: 1px solid transparent;
border-top-left-radius: 0.25rem;
border-top-right-radius: 0.25rem;
color: var(--secondary-color);
padding: 0.5rem 1rem;
}
.nav-tabs .nav-link:hover {
border-color: #e9ecef #e9ecef #dee2e6;
}
.nav-tabs .nav-link.active {
color: var(--primary-color);
background-color: #fff;
border-color: #dee2e6 #dee2e6 #fff;
font-weight: bold;
}
</style> </head> <body> <!-- 导航栏 -->
<div id="navbar-container"></div>
<!-- 主要内容 --> <div class="container py-5"> <div class="streaming-header"> <div class="container"> <h1>直播带货与基地实景直播</h1> <p class="lead">足不出户，体验农场实景，了解农产品生产过程，直接购买优质农产品</p> </div> </div> <!-- 标签选项卡 --> <div class="tab-container"> <ul class="nav nav-tabs" id="streamingTabs" role="tablist"> <li class="nav-item" role="presentation"> <button class="nav-link active" id="live-tab" data-bs-toggle="tab" data-bs-target="#live" type="button" role="tab" aria-controls="live" aria-selected="true">正在直播</button> </li> <li class="nav-item" role="presentation"> <button class="nav-link" id="schedule-tab" data-bs-toggle="tab" data-bs-target="#schedule" type="button" role="tab" aria-controls="schedule" aria-selected="false">直播预告</button> </li> <li class="nav-item" role="presentation"> <button class="nav-link" id="farm-view-tab" data-bs-toggle="tab" data-bs-target="#farm-view" type="button" role="tab" aria-controls="farm-view" aria-selected="false">基地实景</button> </li> <li class="nav-item" role="presentation"> <button class="nav-link" id="replay-tab" data-bs-toggle="tab" data-bs-target="#replay" type="button" role="tab" aria-controls="replay" aria-selected="false">直播回放</button> </li> </ul> <div class="tab-content" id="streamingTabsContent"> <!-- 正在直播选项卡 --> <div class="tab-pane fade show active" id="live" role="tabpanel" aria-labelledby="live-tab"> <div class="streaming-card"> <div class="row"> <div class="col-md-8"> <!-- 直播视频 --> <div class="live-container"> <video class="live-video" autoplay muted loop> <source src="videos/farm-live.mp4" type="video/mp4">
您的浏览器不支持视频标签。
</video> <div class="live-badge"> <i class="fas fa-circle"></i> 直播中
</div> <div class="live-viewers"> <i class="fas fa-eye"></i> 1,245 人正在观看
</div> </div> <!-- 直播信息 --> <div class="live-info"> <div class="live-title">浙江嘉兴水稻基地直播：新米上市特惠，品质保证</div> <div class="live-host"> <img src="images/host1.jpg" alt="主播头像" class="host-avatar"> <div class="host-name">小麦农场主</div> <div class="host-followers">12.5万粉丝</div> <button class="follow-btn">关注</button> </div> <div class="live-description">
今天我们将带大家参观浙江嘉兴水稻基地的收割过程，并且为大家带来新米上市特惠活动，限时抢购！
</div> <div class="live-tags"> <span class="live-tag">有机大米</span> <span class="live-tag">新米上市</span> <span class="live-tag">特惠活动</span> <span class="live-tag">农场直播</span> </div> </div> </div> <div class="col-md-4"> <!-- 聊天区 --> <div class="chat-container"> <div class="chat-header">
直播互动区
</div> <div class="chat-messages" id="chatMessages"> <div class="chat-message"> <div class="message-sender">小红花</div> <div class="message-content">主播好！这个大米看起来好新鲜啊！</div> </div> <div class="chat-message"> <div class="message-sender">农场爱好者</div> <div class="message-content">请问这个大米是什么品种的？</div> </div> <div class="chat-message"> <div class="message-sender">小麦农场主</div> <div class="message-content">这是浙江特产的稻花香品种，香糊可口！</div> </div> <div class="chat-message"> <div class="message-sender">天空之城</div> <div class="message-content">这个价格真实惠，我要买两袋！</div> </div> <div class="chat-message"> <div class="message-sender">小清新</div> <div class="message-content">请问可以展示一下包装吗？</div> </div> <div class="chat-message"> <div class="message-sender">小麦农场主</div> <div class="message-content">好的，马上为大家展示包装！</div> </div> </div> <div class="chat-input"> <input type="text" placeholder="发送消息..." id="chatInput"> <button id="sendMessage">发送</button> </div> </div> </div> </div> </div> <!-- 直播商品 --> <div class="streaming-card"> <h4 class="mb-4">直播商品</h4> <div class="row"> <!-- 商品1 --> <div class="col-md-3"> <div class="product-card"> <img src="images/product1.jpg" alt="商品图片" class="product-image"> <div class="product-info"> <div class="product-title">有机稻花香大米 5kg</div> <div class="product-price">¥99 <span class="product-original-price">¥129</span></div> <div class="product-sales">已售 256 份</div> <div class="product-actions"> <button class="btn btn-sm btn-outline-success">查看详情</button> <button class="btn btn-sm btn-danger">立即抢购</button> </div> </div> </div> </div> <!-- 商品2 --> <div class="col-md-3"> <div class="product-card"> <img src="images/product2.jpg" alt="商品图片" class="product-image"> <div class="product-info"> <div class="product-title">精选有机茶叶礼盒</div> <div class="product-price">¥188 <span class="product-original-price">¥288</span></div> <div class="product-sales">已售 128 份</div> <div class="product-actions"> <button class="btn btn-sm btn-outline-success">查看详情</button> <button class="btn btn-sm btn-danger">立即抢购</button> </div> </div> </div> </div> <!-- 商品3 --> <div class="col-md-3"> <div class="product-card"> <img src="images/product3.jpg" alt="商品图片" class="product-image"> <div class="product-info"> <div class="product-title">新鲜水果礼盒</div> <div class="product-price">¥158 <span class="product-original-price">¥258</span></div> <div class="product-sales">已售 89 份</div> <div class="product-actions"> <button class="btn btn-sm btn-outline-success">查看详情</button> <button class="btn btn-sm btn-danger">立即抢购</button> </div> </div> </div> </div> <!-- 商品4 --> <div class="col-md-3"> <div class="product-card"> <img src="images/product4.jpg" alt="商品图片" class="product-image"> <div class="product-info"> <div class="product-title">有机蔬菜礼盒</div> <div class="product-price">¥128 <span class="product-original-price">¥168</span></div> <div class="product-sales">已售 76 份</div> <div class="product-actions"> <button class="btn btn-sm btn-outline-success">查看详情</button> <button class="btn btn-sm btn-danger">立即抢购</button> </div> </div> </div> </div> </div> </div> </div> <!-- 直播预告选项卡 --> <div class="tab-pane fade" id="schedule" role="tabpanel" aria-labelledby="schedule-tab"> <div class="streaming-card"> <h4 class="mb-4">直播预告</h4> <div class="row"> <!-- 预告1 --> <div class="col-md-6"> <div class="schedule-card"> <img src="images/schedule1.jpg" alt="预告图片" class="schedule-image"> <div class="schedule-info"> <div class="schedule-title">江苏苏州茶叶基地直播：新茶采摘季</div> <div class="schedule-time"> <i class="fas fa-calendar-alt"></i> 2023年10月15日 15:00
</div> <div class="schedule-description">
我们将带大家参观江苏苏州茶叶基地的新茶采摘过程，并且为大家带来新茶上市特惠活动！
</div> <div class="schedule-actions"> <button class="reminder-btn"> <i class="fas fa-bell"></i> 设置提醒
</button> <button class="btn btn-outline-success">查看详情</button> </div> </div> </div> </div> <!-- 预告2 --> <div class="col-md-6"> <div class="schedule-card"> <img src="images/schedule2.jpg" alt="预告图片" class="schedule-image"> <div class="schedule-info"> <div class="schedule-title">安徽合肥水果基地直播：应季水果采摘</div> <div class="schedule-time"> <i class="fas fa-calendar-alt"></i> 2023年10月18日 14:00
</div> <div class="schedule-description">
我们将带大家参观安徽合肥水果基地的应季水果采摘过程，并且为大家带来新鲜水果特惠活动！
</div> <div class="schedule-actions"> <button class="reminder-btn"> <i class="fas fa-bell"></i> 设置提醒
</button> <button class="btn btn-outline-success">查看详情</button> </div> </div> </div> </div> <!-- 预告3 --> <div class="col-md-6"> <div class="schedule-card"> <img src="images/schedule3.jpg" alt="预告图片" class="schedule-image"> <div class="schedule-info"> <div class="schedule-title">浙江杭州蔬菜基地直播：有机种植技术</div> <div class="schedule-time"> <i class="fas fa-calendar-alt"></i> 2023年10月20日 10:00
</div> <div class="schedule-description">
我们将带大家参观浙江杭州蔬菜基地的有机种植技术，并且为大家带来有机蔬菜特惠活动！
</div> <div class="schedule-actions"> <button class="reminder-btn"> <i class="fas fa-bell"></i> 设置提醒
</button> <button class="btn btn-outline-success">查看详情</button> </div> </div> </div> </div> <!-- 预告4 --> <div class="col-md-6"> <div class="schedule-card"> <img src="images/schedule4.jpg" alt="预告图片" class="schedule-image"> <div class="schedule-info"> <div class="schedule-title">山东烟台海鲜基地直播：海鲜捕捉与加工</div> <div class="schedule-time"> <i class="fas fa-calendar-alt"></i> 2023年10月25日 09:00
</div> <div class="schedule-description">
我们将带大家参观山东烟台海鲜基地的海鲜捕捉与加工过程，并且为大家带来新鲜海鲜特惠活动！
</div> <div class="schedule-actions"> <button class="reminder-btn"> <i class="fas fa-bell"></i> 设置提醒
</button> <button class="btn btn-outline-success">查看详情</button> </div> </div> </div> </div> </div> </div> </div> <!-- 基地实景选项卡 --> <div class="tab-pane fade" id="farm-view" role="tabpanel" aria-labelledby="farm-view-tab"> <div class="streaming-card"> <h4 class="mb-4">基地实景直播</h4> <div class="farm-view-card"> <div class="farm-view-title">浙江嘉兴水稻基地 - 实时监控</div> <div class="farm-view-container"> <img src="images/farm-view1.jpg" alt="农场实景" class="farm-view-image" id="farmViewImage"> <div class="farm-view-controls"> <button class="farm-view-control" onclick="rotateFarmView('left')"><i class="fas fa-arrow-left"></i></button> <button class="farm-view-control" onclick="zoomFarmView('in')"><i class="fas fa-search-plus"></i></button> <button class="farm-view-control" onclick="zoomFarmView('out')"><i class="fas fa-search-minus"></i></button> <button class="farm-view-control" onclick="rotateFarmView('right')"><i class="fas fa-arrow-right"></i></button> </div> </div> <div class="farm-view-locations"> <div class="farm-location" onclick="changeFarmView('images/farm-view1.jpg')"> <img src="images/farm-view1.jpg" alt="稻田区" class="farm-location-image"> <div class="farm-location-name">稻田区</div> </div> <div class="farm-location" onclick="changeFarmView('images/farm-view2.jpg')"> <img src="images/farm-view2.jpg" alt="加工区" class="farm-location-image"> <div class="farm-location-name">加工区</div> </div> <div class="farm-location" onclick="changeFarmView('images/farm-view3.jpg')"> <img src="images/farm-view3.jpg" alt="仓储区" class="farm-location-image"> <div class="farm-location-name">仓储区</div> </div> <div class="farm-location" onclick="changeFarmView('images/farm-view4.jpg')"> <img src="images/farm-view4.jpg" alt="管理区" class="farm-location-image"> <div class="farm-location-name">管理区</div> </div> <div class="farm-location" onclick="changeFarmView('images/farm-view5.jpg')"> <img src="images/farm-view5.jpg" alt="生态区" class="farm-location-image"> <div class="farm-location-name">生态区</div> </div> </div> </div> <div class="row mt-4"> <div class="col-md-6"> <div class="farm-view-card"> <div class="farm-view-title">江苏苏州茶叶基地 - 实时监控</div> <div class="farm-view-container"> <img src="images/farm-view6.jpg" alt="茶叶基地" class="farm-view-image"> </div> <div class="text-center p-3"> <button class="btn btn-success">进入实景直播</button> </div> </div> </div> <div class="col-md-6"> <div class="farm-view-card"> <div class="farm-view-title">安徽合肥水果基地 - 实时监控</div> <div class="farm-view-container"> <img src="images/farm-view7.jpg" alt="水果基地" class="farm-view-image"> </div> <div class="text-center p-3"> <button class="btn btn-success">进入实景直播</button> </div> </div> </div> </div> <div class="alert alert-info mt-4"> <i class="fas fa-info-circle me-2"></i>
实景直播每日更新，您可以实时查看农场生产情况。部分基地提供360度全景观看功能，请在PC端使用以获得最佳体验。
</div> </div> </div> <!-- 直播回放选项卡 --> <div class="tab-pane fade" id="replay" role="tabpanel" aria-labelledby="replay-tab"> <div class="streaming-card"> <h4 class="mb-4">直播回放</h4> <div class="row"> <!-- 回放1 --> <div class="col-md-6 mb-4"> <div class="live-container"> <video class="live-video" controls> <source src="videos/replay1.mp4" type="video/mp4">
您的浏览器不支持视频标签。
</video> </div> <div class="live-info"> <div class="live-title">浙江嘉兴水稻基地：水稻种植全过程</div> <div class="live-host"> <img src="images/host1.jpg" alt="主播头像" class="host-avatar"> <div class="host-name">小麦农场主</div> <div class="host-followers">12.5万粉丝</div> </div> <div class="live-description">
本期直播带大家了解水稻种植的全过程，从能种到种植，再到田间管理的全方位解析。
</div> <div class="d-flex justify-content-between align-items-center mt-3"> <div> <i class="fas fa-calendar-alt me-2"></i> 2023年6月10日
<i class="fas fa-eye ms-3 me-2"></i> 2.3万次播放
</div> <button class="btn btn-sm btn-outline-success">查看商品</button> </div> </div> </div> <!-- 回放2 --> <div class="col-md-6 mb-4"> <div class="live-container"> <video class="live-video" controls> <source src="videos/replay2.mp4" type="video/mp4">
您的浏览器不支持视频标签。
</video> </div> <div class="live-info"> <div class="live-title">江苏苏州茶叶基地：高山茶采摘与制作</div> <div class="live-host"> <img src="images/host2.jpg" alt="主播头像" class="host-avatar"> <div class="host-name">茶叶小主播</div> <div class="host-followers">8.6万粉丝</div> </div> <div class="live-description">
本期直播带大家了解高山茶的采摘与制作过程，从采摘到加工，再到包装的全方位解析。
</div> <div class="d-flex justify-content-between align-items-center mt-3"> <div> <i class="fas fa-calendar-alt me-2"></i> 2023年5月15日
<i class="fas fa-eye ms-3 me-2"></i> 1.8万次播放
</div> <button class="btn btn-sm btn-outline-success">查看商品</button> </div> </div> </div> <!-- 回放3 --> <div class="col-md-6 mb-4"> <div class="live-container"> <video class="live-video" controls> <source src="videos/replay3.mp4" type="video/mp4">
您的浏览器不支持视频标签。
</video> </div> <div class="live-info"> <div class="live-title">安徽合肥水果基地：有机水果种植技术</div> <div class="live-host"> <img src="images/host3.jpg" alt="主播头像" class="host-avatar"> <div class="host-name">水果达人</div> <div class="host-followers">5.2万粉丝</div> </div> <div class="live-description">
本期直播带大家了解有机水果的种植技术，从土壤准备到病虫害防治，再到收获的全方位解析。
</div> <div class="d-flex justify-content-between align-items-center mt-3"> <div> <i class="fas fa-calendar-alt me-2"></i> 2023年4月20日
<i class="fas fa-eye ms-3 me-2"></i> 1.5万次播放
</div> <button class="btn btn-sm btn-outline-success">查看商品</button> </div> </div> </div> <!-- 回放4 --> <div class="col-md-6 mb-4"> <div class="live-container"> <video class="live-video" controls> <source src="videos/replay4.mp4" type="video/mp4">
您的浏览器不支持视频标签。
</video> </div> <div class="live-info"> <div class="live-title">浙江杭州蔬菜基地：有机蔬菜种植与管理</div> <div class="live-host"> <img src="images/host4.jpg" alt="主播头像" class="host-avatar"> <div class="host-name">蔬菜小王</div> <div class="host-followers">3.8万粉丝</div> </div> <div class="live-description">
本期直播带大家了解有机蔬菜的种植与管理技术，从种子选择到病虫害防治，再到收获的全方位解析。
</div> <div class="d-flex justify-content-between align-items-center mt-3"> <div> <i class="fas fa-calendar-alt me-2"></i> 2023年3月25日
<i class="fas fa-eye ms-3 me-2"></i> 1.2万次播放
</div> <button class="btn btn-sm btn-outline-success">查看商品</button> </div> </div> </div> </div> <div class="text-center mt-3"> <button class="btn btn-outline-success">加载更多</button> </div> </div> </div> </div> </div> </div> <!-- 页脚 -->
<div id="footer-container"></div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="js/login-config.js"></script>
<script src="js/components.js"></script>
<script>
// 使用 common.js 加载导航栏

// 初始化页面
document.addEventListener('DOMContentLoaded', function() {
// 关注按钮点击事件
const followBtn = document.querySelector('.follow-btn');
if (followBtn) {
followBtn.addEventListener('click', function() {
this.classList.toggle('followed');
if (this.classList.contains('followed')) {
this.textContent = '已关注';
} else {
this.textContent = '关注';
}
});
}
// 发送消息按钮点击事件
const sendMessageBtn = document.getElementById('sendMessage');
const chatInput = document.getElementById('chatInput');
const chatMessages = document.getElementById('chatMessages');
if (sendMessageBtn && chatInput && chatMessages) {
sendMessageBtn.addEventListener('click', function() {
const message = chatInput.value.trim();
if (message) {
const messageHTML = `
<div class="chat-message"> <div class="message-sender">我</div> <div class="message-content">${message}</div> </div>
`;
chatMessages.insertAdjacentHTML('beforeend', messageHTML);
chatInput.value = '';
chatMessages.scrollTop = chatMessages.scrollHeight;
}
});
chatInput.addEventListener('keypress', function(e) {
if (e.key === 'Enter') {
sendMessageBtn.click();
}
});
}
// 设置提醒按钮点击事件
document.querySelectorAll('.reminder-btn').forEach(btn => {
btn.addEventListener('click', function() {
this.classList.toggle('reminded');
if (this.classList.contains('reminded')) {
this.innerHTML = '<i class="fas fa-bell-slash"></i> 取消提醒';
} else {
this.innerHTML = '<i class="fas fa-bell"></i> 设置提醒';
}
});
});
// 商品抢购按钮点击事件
document.querySelectorAll('.btn-danger').forEach(btn => {
btn.addEventListener('click', function() {
const productTitle = this.closest('.product-info').querySelector('.product-title').textContent;
alert(`已将商品「${productTitle}」加入购物车！`);
});
});
});
// 农场实景相关函数
function changeFarmView(imageSrc) {
document.getElementById('farmViewImage').src = imageSrc;
}
function rotateFarmView(direction) {
// 实际应用中应该有旋转图像的逻辑
alert(`旋转视图到${direction === 'left' ? '左侧' : '右侧'}`);
}
function zoomFarmView(type) {
// 实际应用中应该有缩放图像的逻辑
alert(`${type === 'in' ? '放大' : '缩小'}视图`);
}
</script>