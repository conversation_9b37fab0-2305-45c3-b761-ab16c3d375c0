<!DOCTYPE html> <html lang="zh-CN"> <head> <meta charset="UTF-8"> <meta name="viewport" content="width=device-width, initial-scale=1.0"> <title>接入顺丰/京东冷链物流 - 智慧农业平台</title> <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet"> <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet"> <link rel="stylesheet" href="css/common.css"> <link rel="stylesheet" href="css/navbar.css"> <style>
:root {
--primary-color: #28a745;
--secondary-color: #6c757d;
--light-bg: #f8f9fa;
--card-shadow: 0 4px 6px rgba(0,0,0,0.1);
--transition: all 0.3s ease;
--logistics-color: #8e44ad;
}
body {
background-color: var(--light-bg);
font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}
.logistics-header {
background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80');
background-size: cover;
background-position: center;
height: 300px;
color: white;
display: flex;
align-items: center;
margin-bottom: 30px;
border-radius: 10px;
}
.logistics-card {
background: white;
padding: 20px;
border-radius: 10px;
box-shadow: var(--card-shadow);
margin-bottom: 30px;
min-height: auto;
}
.stat-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 20px;
transition: var(--transition);
}
.stat-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.stat-icon {
font-size: 2rem;
color: var(--logistics-color);
margin-bottom: 10px;
}
.stat-value {
font-size: 1.5rem;
font-weight: bold;
color: var(--logistics-color);
margin-bottom: 5px;
}
.stat-label {
font-size: 0.9rem;
color: var(--secondary-color);
}
.partner-card {
background: var(--light-bg);
border-radius: 10px;
overflow: hidden;
margin-bottom: 20px;
transition: var(--transition);
min-height: auto;
border: 1px solid #eee;
position: relative;
}
.partner-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.partner-badge {
position: absolute;
top: 10px;
right: 10px;
background-color: var(--logistics-color);
color: white;
padding: 5px 10px;
border-radius: 20px;
font-size: 0.8rem;
z-index: 1;
}
.partner-logo {
width: 100%;
height: 120px;
object-fit: contain;
padding: 20px;
background-color: white;
}
.partner-info {
padding: 15px;
}
.partner-title {
font-weight: bold;
font-size: 1.1rem;
margin-bottom: 5px;
display: flex;
justify-content: space-between;
align-items: center;
}
.partner-subtitle {
color: var(--logistics-color);
font-size: 0.9rem;
margin-bottom: 10px;
}
.partner-desc {
color: var(--secondary-color);
margin-bottom: 10px;
font-size: 0.9rem;
height: 60px;
overflow: hidden;
}
.partner-features {
display: flex;
flex-wrap: wrap;
gap: 5px;
margin-bottom: 10px;
}
.partner-feature {
display: inline-block;
padding: 2px 8px;
border-radius: 15px;
font-size: 0.7rem;
background-color: #f0f0f0;
color: var(--secondary-color);
}
.partner-actions {
display: flex;
gap: 10px;
}
.service-card {
background: var(--light-bg);
border-radius: 10px;
overflow: hidden;
margin-bottom: 20px;
transition: var(--transition);
min-height: auto;
border: 1px solid #eee;
}
.service-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.service-img {
width: 100%;
height: 150px;
object-fit: cover;
}
.service-info {
padding: 15px;
}
.service-title {
font-weight: bold;
font-size: 1.1rem;
margin-bottom: 5px;
}
.service-desc {
color: var(--secondary-color);
margin-bottom: 10px;
font-size: 0.9rem;
height: 60px;
overflow: hidden;
}
.service-meta {
display: flex;
justify-content: space-between;
margin-bottom: 10px;
font-size: 0.9rem;
}
.service-price {
font-weight: bold;
color: var(--logistics-color);
}
.service-time {
color: var(--secondary-color);
display: flex;
align-items: center;
}
.service-time i {
margin-right: 5px;
}
.service-features {
display: flex;
flex-wrap: wrap;
gap: 5px;
margin-bottom: 10px;
}
.service-feature {
display: inline-block;
padding: 2px 8px;
border-radius: 15px;
font-size: 0.7rem;
background-color: #f0f0f0;
color: var(--secondary-color);
}
.service-actions {
display: flex;
gap: 10px;
}
.benefit-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 15px;
transition: var(--transition);
min-height: auto;
}
.benefit-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.benefit-header {
display: flex;
align-items: center;
margin-bottom: 15px;
}
.benefit-icon {
width: 50px;
height: 50px;
border-radius: 10px;
background-color: var(--logistics-color);
color: white;
display: flex;
align-items: center;
justify-content: center;
font-size: 1.5rem;
margin-right: 15px;
flex-shrink: 0;
}
.benefit-title {
font-weight: bold;
font-size: 1.1rem;
margin-bottom: 5px;
}
.benefit-subtitle {
font-size: 0.9rem;
color: var(--secondary-color);
}
.benefit-body {
margin-bottom: 15px;
}
.benefit-description {
color: var(--secondary-color);
margin-bottom: 15px;
}
.step-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 15px;
transition: var(--transition);
position: relative;
}
.step-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.step-header {
display: flex;
align-items: center;
margin-bottom: 15px;
}
.step-number {
width: 40px;
height: 40px;
border-radius: 50%;
background-color: var(--logistics-color);
color: white;
display: flex;
align-items: center;
justify-content: center;
font-weight: bold;
margin-right: 15px;
flex-shrink: 0;
}
.step-title {
font-weight: bold;
font-size: 1.1rem;
}
.step-body {
margin-bottom: 15px;
}
.step-description {
color: var(--secondary-color);
margin-bottom: 15px;
}
.step-connector {
position: absolute;
left: 20px;
top: 55px;
width: 2px;
height: calc(100% - 40px);
background-color: var(--logistics-color);
z-index: 0;
}
.faq-item {
margin-bottom: 15px;
}
.faq-question {
font-weight: bold;
margin-bottom: 5px;
cursor: pointer;
display: flex;
justify-content: space-between;
align-items: center;
}
.faq-answer {
color: var(--secondary-color);
display: none;
padding: 10px 0;
}
.faq-answer.active {
display: block;
}
.success-story {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 15px;
transition: var(--transition);
}
.success-story:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.success-header {
display: flex;
align-items: center;
margin-bottom: 10px;
}
.success-logo {
width: 60px;
height: 60px;
border-radius: 10px;
object-fit: cover;
margin-right: 15px;
background-color: white;
padding: 5px;
}
.success-info {
flex: 1;
}
.success-name {
font-weight: bold;
margin-bottom: 0;
}
.success-location {
font-size: 0.9rem;
color: var(--secondary-color);
}
.success-content {
color: var(--secondary-color);
margin-bottom: 10px;
font-size: 0.9rem;
}
.success-stats {
display: flex;
gap: 15px;
font-size: 0.9rem;
}
.success-stat {
display: flex;
align-items: center;
}
.success-stat i {
color: var(--logistics-color);
margin-right: 5px;
}
.application-form {
background: var(--light-bg);
padding: 20px;
border-radius: 10px;
}
.map-container {
height: 300px;
width: 100%;
border-radius: 10px;
overflow: hidden;
margin-bottom: 20px;
}
.chart-container {
position: relative;
height: 300px;
width: 100%;
margin-bottom: 20px;
}
.nav-tabs .nav-link {
color: var(--secondary-color);
}
.nav-tabs .nav-link.active {
color: var(--logistics-color);
font-weight: bold;
}
.tab-content {
padding: 20px 0;
}
.price-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 15px;
transition: var(--transition);
}
.price-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.price-header {
text-align: center;
margin-bottom: 15px;
padding-bottom: 15px;
border-bottom: 1px solid #ddd;
}
.price-title {
font-weight: bold;
font-size: 1.2rem;
margin-bottom: 5px;
}
.price-subtitle {
font-size: 0.9rem;
color: var(--secondary-color);
}
.price-value {
font-size: 2rem;
font-weight: bold;
color: var(--logistics-color);
margin-bottom: 5px;
}
.price-unit {
font-size: 0.9rem;
color: var(--secondary-color);
}
.price-features {
margin-bottom: 15px;
}
.price-feature {
display: flex;
align-items: center;
margin-bottom: 5px;
font-size: 0.9rem;
}
.price-feature i {
color: var(--logistics-color);
margin-right: 5px;
}
.price-actions {
text-align: center;
}
.tracking-form {
background: var(--light-bg);
padding: 20px;
border-radius: 10px;
margin-bottom: 20px;
}
.tracking-result {
background: var(--light-bg);
padding: 20px;
border-radius: 10px;
margin-top: 20px;
display: none;
}
.tracking-timeline {
position: relative;
padding-left: 30px;
margin-bottom: 20px;
}
.tracking-line {
position: absolute;
left: 15px;
top: 0;
bottom: 0;
width: 2px;
background-color: var(--logistics-color);
}
.tracking-item {
position: relative;
margin-bottom: 20px;
}
.tracking-dot {
position: absolute;
left: -30px;
top: 0;
width: 20px;
height: 20px;
border-radius: 50%;
background-color: var(--logistics-color);
border: 3px solid white;
}
.tracking-date {
font-weight: bold;
margin-bottom: 5px;
color: var(--logistics-color);
}
.tracking-content {
background-color: white;
padding: 15px;
border-radius: 10px;
box-shadow: var(--card-shadow);
}
.tracking-title {
font-weight: bold;
margin-bottom: 10px;
}
.tracking-desc {
color: var(--secondary-color);
font-size: 0.9rem;
}
</style> </head> <body> <!-- 导航栏 -->
<div id="navbar-container"></div>
<!-- 主要内容 --> <div class="container py-5"> <div class="logistics-header"> <div class="container"> <h1>接入顺丰/京东冷链物流</h1> <p class="lead">与顺丰、京东等知名物流企业深度合作，提供专业冷链物流服务，确保农产品新鲜送达，提升用户体验和满意度</p> <div class="mt-4"> <button class="btn btn-primary btn-lg me-2" style="background-color: #8e44ad; border-color: #8e44ad;"><i class="fas fa-truck me-2"></i>申请物流服务</button> <button class="btn btn-outline-light btn-lg"><i class="fas fa-search me-2"></i>物流查询</button> </div> </div> </div> <!-- 顶部统计卡片 --> <div class="row mb-4"> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-truck stat-icon"></i> <div class="stat-value">2</div> <div class="stat-label">物流合作伙伴</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-map-marked-alt stat-icon"></i> <div class="stat-value">328</div> <div class="stat-label">覆盖城市数量</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-box-open stat-icon"></i> <div class="stat-value">12.6万</div> <div class="stat-label">月均配送单量</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-thumbs-up stat-icon"></i> <div class="stat-value">98.5%</div> <div class="stat-label">用户满意度</div> </div> </div> </div> <!-- 主要内容区域 --> <div class="row"> <!-- 左侧内容 --> <div class="col-md-8"> <!-- 物流合作伙伴 --> <div class="logistics-card mb-4"> <h4 class="mb-4">物流合作伙伴</h4> <div class="row"> <div class="col-md-6 mb-4"> <div class="partner-card"> <div class="partner-badge">战略合作</div> <img src="images/sf-logo.png" alt="顺丰快递" class="partner-logo"> <div class="partner-info"> <div class="partner-title">顺丰快递</div> <div class="partner-subtitle">专业冷链物流服务提供商</div> <div class="partner-desc">与顺丰快递建立战略合作关系，提供专业的农产品冷链物流服务，包括仓储、运输、配送等全链路服务</div> <div class="partner-features"> <span class="partner-feature">冷链运输</span> <span class="partner-feature">温控仓储</span> <span class="partner-feature">全程监控</span> </div> <div class="partner-actions"> <button class="btn btn-primary w-100" style="background-color: #8e44ad; border-color: #8e44ad;"><i class="fas fa-info-circle me-2"></i>查看服务详情</button> </div> </div> </div> </div> <div class="col-md-6 mb-4"> <div class="partner-card"> <div class="partner-badge">战略合作</div> <img src="images/jd-logo.png" alt="京东物流" class="partner-logo"> <div class="partner-info"> <div class="partner-title">京东物流</div> <div class="partner-subtitle">专业冷链物流服务提供商</div> <div class="partner-desc">与京东物流建立战略合作关系，提供专业的农产品冷链物流服务，包括仓储、运输、配送等全链路服务</div> <div class="partner-features"> <span class="partner-feature">冷链运输</span> <span class="partner-feature">智能仓储</span> <span class="partner-feature">数字物流</span> </div> <div class="partner-actions"> <button class="btn btn-primary w-100" style="background-color: #8e44ad; border-color: #8e44ad;"><i class="fas fa-info-circle me-2"></i>查看服务详情</button> </div> </div> </div> </div> </div> </div> <!-- 物流服务 --> <div class="logistics-card mb-4"> <h4 class="mb-4">物流服务</h4> <div class="row"> <div class="col-md-6 mb-4"> <div class="service-card"> <img src="images/cold-chain1.jpg" alt="标准冷链配送" class="service-img"> <div class="service-info"> <div class="service-title">标准冷链配送</div> <div class="service-desc">适用于一般新鲜农产品的冷链配送服务，全程温控，确保农产品新鲜送达</div> <div class="service-meta"> <div class="service-price">15元/公斤起</div> <div class="service-time"><i class="fas fa-clock"></i> 24-48小时送达</div> </div> <div class="service-features"> <span class="service-feature">温控运输</span> <span class="service-feature">全程监控</span> <span class="service-feature">保鲜包装</span> </div> <div class="service-actions"> <button class="btn btn-primary w-100" style="background-color: #8e44ad; border-color: #8e44ad;"><i class="fas fa-truck me-2"></i>立即选择</button> </div> </div> </div> </div> <div class="col-md-6 mb-4"> <div class="service-card"> <img src="images/cold-chain2.jpg" alt="加急冷链配送" class="service-img"> <div class="service-info"> <div class="service-title">加急冷链配送</div> <div class="service-desc">适用于需要快速送达的高端新鲜农产品，优先配送，确保最短时间送达</div> <div class="service-meta"> <div class="service-price">25元/公斤起</div> <div class="service-time"><i class="fas fa-clock"></i> 12-24小时送达</div> </div> <div class="service-features"> <span class="service-feature">优先配送</span> <span class="service-feature">精准温控</span> <span class="service-feature">保鲜包装</span> </div> <div class="service-actions"> <button class="btn btn-primary w-100" style="background-color: #8e44ad; border-color: #8e44ad;"><i class="fas fa-truck me-2"></i>立即选择</button> </div> </div> </div> </div> <div class="col-md-6 mb-4"> <div class="service-card"> <img src="images/cold-chain3.jpg" alt="仓储配送一体化" class="service-img"> <div class="service-info"> <div class="service-title">仓储配送一体化</div> <div class="service-desc">提供仓储、分拣、配送一体化服务，适用于需要仓储和分批配送的农产品</div> <div class="service-meta"> <div class="service-price">定制报价</div> <div class="service-time"><i class="fas fa-clock"></i> 灵活配送</div> </div> <div class="service-features"> <span class="service-feature">温控仓储</span> <span class="service-feature">分拣包装</span> <span class="service-feature">定制配送</span> </div> <div class="service-actions"> <button class="btn btn-primary w-100" style="background-color: #8e44ad; border-color: #8e44ad;"><i class="fas fa-truck me-2"></i>立即选择</button> </div> </div> </div> </div> <div class="col-md-6 mb-4"> <div class="service-card"> <img src="images/cold-chain4.jpg" alt="生鲜定制配送" class="service-img"> <div class="service-info"> <div class="service-title">生鲜定制配送</div> <div class="service-desc">针对特殊要求的生鲜农产品提供定制化的冷链配送服务，满足个性化需求</div> <div class="service-meta"> <div class="service-price">定制报价</div> <div class="service-time"><i class="fas fa-clock"></i> 定制配送</div> </div> <div class="service-features"> <span class="service-feature">定制方案</span> <span class="service-feature">专人对接</span> <span class="service-feature">特殊需求</span> </div> <div class="service-actions"> <button class="btn btn-primary w-100" style="background-color: #8e44ad; border-color: #8e44ad;"><i class="fas fa-truck me-2"></i>立即选择</button> </div> </div> </div> </div> </div> </div> <!-- 物流优势 --> <div class="logistics-card mb-4"> <h4 class="mb-4">物流优势</h4> <div class="row"> <div class="col-md-6 mb-4"> <div class="benefit-card"> <div class="benefit-header"> <div class="benefit-icon"> <i class="fas fa-snowflake"></i> </div> <div> <div class="benefit-title">全程冷链</div> <div class="benefit-subtitle">保障农产品新鲜度</div> </div> </div> <div class="benefit-body"> <div class="benefit-description">
采用全程冷链物流模式，从采摘、包装、仓储、运输到配送全过程温控，确保农产品始终处于最佳保鲜温度，最大限度保持农产品的新鲜度和营养价值。
</div> </div> </div> </div> <div class="col-md-6 mb-4"> <div class="benefit-card"> <div class="benefit-header"> <div class="benefit-icon"> <i class="fas fa-shipping-fast"></i> </div> <div> <div class="benefit-title">高效配送</div> <div class="benefit-subtitle">缩短配送时间</div> </div> </div> <div class="benefit-body"> <div class="benefit-description">
依托顺丰、京东等物流合作伙伴的全国物流网络，实现高效配送。标准冷链配送服务可实现24-48小时送达，加急冷链配送服务可实现12-24小时送达，最大限度缩短从农田到餐桨的时间。
</div> </div> </div> </div> <div class="col-md-6 mb-4"> <div class="benefit-card"> <div class="benefit-header"> <div class="benefit-icon"> <i class="fas fa-map-marked-alt"></i> </div> <div> <div class="benefit-title">广泛覆盖</div> <div class="benefit-subtitle">覆盖全国主要城市</div> </div> </div> <div class="benefit-body"> <div class="benefit-description">
物流服务覆盖全国328个城市，包括一线、二线和三线城市，实现全国范围内的农产品冷链配送。同时，与合作伙伴共同建立了覆盖全国的温控仓储网络，为农产品提供全方位的物流支持。
</div> </div> </div> </div> <div class="col-md-6 mb-4"> <div class="benefit-card"> <div class="benefit-header"> <div class="benefit-icon"> <i class="fas fa-chart-line"></i> </div> <div> <div class="benefit-title">全程可视化</div> <div class="benefit-subtitle">实时监控物流状态</div> </div> </div> <div class="benefit-body"> <div class="benefit-description">
采用先进的物流跟踪系统，实现全程物流状态可视化。用户可以通过手机APP或网页实时查看农产品的物流状态、位置和温度等信息，实现全程监控，提高物流透明度和用户体验。
</div> </div> </div> </div> </div> </div> <!-- 物流流程 --> <div class="logistics-card mb-4"> <h4 class="mb-4">物流流程</h4> <div class="step-card"> <div class="step-header"> <div class="step-number">1</div> <div class="step-title">下单选择物流服务</div> </div> <div class="step-body"> <div class="step-description">
在下单时选择适合的物流服务类型，如标准冷链配送、加急冷链配送等。系统将根据您的选择自动计算物流费用和预计送达时间。
</div> </div> <div class="step-connector"></div> </div> <div class="step-card"> <div class="step-header"> <div class="step-number">2</div> <div class="step-title">农产品采摘与包装</div> </div> <div class="step-body"> <div class="step-description">
收到订单后，基地将进行农产品的采摘和初步处理。采用专业的保鲜包装技术，对农产品进行包装，确保农产品的新鲜度和品质。
</div> </div> <div class="step-connector"></div> </div> <div class="step-card"> <div class="step-header"> <div class="step-number">3</div> <div class="step-title">冷链运输</div> </div> <div class="step-body"> <div class="step-description">
包装好的农产品将被放入温控箱中，由冷链运输车辆运送到目的地城市的物流中心。全程温控，确保农产品始终处于最佳保鲜温度。
</div> </div> <div class="step-connector"></div> </div> <div class="step-card"> <div class="step-header"> <div class="step-number">4</div> <div class="step-title">物流中心分拣</div> </div> <div class="step-body"> <div class="step-description">
农产品到达目的地城市的物流中心后，将进行分拣和二次包装，准备最后的配送。整个过程在温控环境中进行，确保农产品的新鲜度。
</div> </div> <div class="step-connector"></div> </div> <div class="step-card"> <div class="step-header"> <div class="step-number">5</div> <div class="step-title">最后一公里配送</div> </div> <div class="step-body"> <div class="step-description">
由配送员使用温控配送箱将农产品配送到用户手中。用户可以通过APP或网页实时查看配送员的位置和预计送达时间，并在收货时进行签收确认。
</div> </div> </div> </div> </div> <!-- 右侧内容 --> <div class="col-md-4"> <!-- 物流查询 --> <div class="logistics-card"> <h4 class="mb-4">物流查询</h4> <div class="tracking-form"> <div class="mb-3"> <label class="form-label">物流单号</label> <div class="input-group"> <input type="text" class="form-control" placeholder="请输入物流单号"> <button class="btn btn-primary" style="background-color: #8e44ad; border-color: #8e44ad;" id="tracking-btn"><i class="fas fa-search me-2"></i>查询</button> </div> </div> <div class="tracking-result" id="tracking-result"> <h5 class="mb-3">物流跟踪信息</h5> <div class="mb-3"> <div><strong>物流单号：</strong> SF1234567890</div> <div><strong>物流公司：</strong> 顺丰快递</div> <div><strong>配送服务：</strong> 标准冷链配送</div> <div><strong>当前状态：</strong> <span class="text-success">运输中</span></div> <div><strong>当前温度：</strong> 4°C</div> </div> <div class="tracking-timeline"> <div class="tracking-line"></div> <div class="tracking-item"> <div class="tracking-dot"></div> <div class="tracking-date">2023-10-15 08:30</div> <div class="tracking-content"> <div class="tracking-title">已发货</div> <div class="tracking-desc">商品已从浙江杭州基地发出</div> </div> </div> <div class="tracking-item"> <div class="tracking-dot"></div> <div class="tracking-date">2023-10-15 12:45</div> <div class="tracking-content"> <div class="tracking-title">已到达中转站</div> <div class="tracking-desc">商品已到达杭州转运中心</div> </div> </div> <div class="tracking-item"> <div class="tracking-dot"></div> <div class="tracking-date">2023-10-15 18:20</div> <div class="tracking-content"> <div class="tracking-title">运输中</div> <div class="tracking-desc">商品正在运往下一站点</div> </div> </div> </div> </div> </div> </div> <!-- 物流价格表 --> <div class="logistics-card mt-4"> <h4 class="mb-4">物流价格表</h4> <div class="row"> <div class="col-md-6 mb-4"> <div class="price-card"> <div class="price-header"> <div class="price-title">标准冷链配送</div> <div class="price-subtitle">适用于一般新鲜农产品</div> </div> <div class="text-center mb-3"> <div class="price-value">15元</div> <div class="price-unit">/公斤起</div> </div> <div class="price-features"> <div class="price-feature"><i class="fas fa-check-circle"></i> 24-48小时送达</div> <div class="price-feature"><i class="fas fa-check-circle"></i> 全程温控运输</div> <div class="price-feature"><i class="fas fa-check-circle"></i> 保鲜包装</div> <div class="price-feature"><i class="fas fa-check-circle"></i> 全程物流跟踪</div> </div> <div class="price-actions"> <button class="btn btn-primary" style="background-color: #8e44ad; border-color: #8e44ad;"><i class="fas fa-truck me-2"></i>立即选择</button> </div> </div> </div> <div class="col-md-6 mb-4"> <div class="price-card"> <div class="price-header"> <div class="price-title">加急冷链配送</div> <div class="price-subtitle">适用于高端新鲜农产品</div> </div> <div class="text-center mb-3"> <div class="price-value">25元</div> <div class="price-unit">/公斤起</div> </div> <div class="price-features"> <div class="price-feature"><i class="fas fa-check-circle"></i> 12-24小时送达</div> <div class="price-feature"><i class="fas fa-check-circle"></i> 优先配送</div> <div class="price-feature"><i class="fas fa-check-circle"></i> 精准温控</div> <div class="price-feature"><i class="fas fa-check-circle"></i> 全程物流跟踪</div> </div> <div class="price-actions"> <button class="btn btn-primary" style="background-color: #8e44ad; border-color: #8e44ad;"><i class="fas fa-truck me-2"></i>立即选择</button> </div> </div> </div> </div> <div class="alert alert-info"> <i class="fas fa-info-circle me-2"></i>
注意：仓储配送一体化和生鲜定制配送服务需要根据具体需求进行定制报价，请联系客服获取详细信息。
</div> </div> <!-- 常见问题 --> <div class="logistics-card mt-4"> <h4 class="mb-4">常见问题</h4> <div class="faq-item"> <div class="faq-question" onclick="toggleFaq(this)"> <span>冷链物流和普通物流有什么区别？</span> <i class="fas fa-chevron-down"></i> </div> <div class="faq-answer">
冷链物流和普通物流的主要区别在于温度控制。冷链物流全程保持低温环境，从采摘、包装、仓储、运输到配送全过程温控，确保农产品始终处于最佳保鲜温度，最大限度保持农产品的新鲜度和营养价值。而普通物流不进行温度控制，不适合运输新鲜农产品。
</div> </div> <div class="faq-item"> <div class="faq-question" onclick="toggleFaq(this)"> <span>冷链物流的配送范围有哪些？</span> <i class="fas fa-chevron-down"></i> </div> <div class="faq-answer">
我们的冷链物流服务覆盖全国328个城市，包括一线、二线和三线城市。其中，一线城市和大部分二线城市可以实现24小时内送达，三线城市和偏远地区可能需要更长的配送时间。具体的配送范围和时间可以在下单时查询。
</div> </div> <div class="faq-item"> <div class="faq-question" onclick="toggleFaq(this)"> <span>如何查询物流状态？</span> <i class="fas fa-chevron-down"></i> </div> <div class="faq-answer">
您可以通过以下方式查询物流状态：1) 在智慧农业平台的网站或APP中输入物流单号进行查询；2) 在“我的订单”中点击“物流跟踪”按钮；3) 直接访问顺丰或京东物流的官方网站或APP，输入物流单号进行查询。通过物流跟踪系统，您可以实时查看农产品的物流状态、位置和温度等信息。
</div> </div> <div class="faq-item"> <div class="faq-question" onclick="toggleFaq(this)"> <span>如果收到的农产品不新鲜怎么办？</span> <i class="fas fa-chevron-down"></i> </div> <div class="faq-answer">
我们对冷链配送的农产品新鲜度提供保障。如果您收到的农产品不新鲜，请在收货后24小时内联系客服，并提供相关照片和说明。经核实后，我们将为您提供退款或补发新鲜农产品的服务。我们的目标是确保每一位客户都能收到新鲜的农产品。
</div> </div> <div class="faq-item"> <div class="faq-question" onclick="toggleFaq(this)"> <span>如何申请物流服务？</span> <i class="fas fa-chevron-down"></i> </div> <div class="faq-answer">
申请物流服务非常简单，您可以通过以下方式申请：1) 在下单购买农产品时，选择适合的物流服务类型；2) 如果您是农业基地或企业用户，可以点击“申请物流服务”按钮，填写申请表格，我们的客服人员将与您联系，提供定制化的物流解决方案。
</div> </div> </div> <!-- 申请物流服务 --> <div class="logistics-card mt-4"> <h4 class="mb-4">申请物流服务</h4> <div class="application-form"> <div class="mb-3"> <label class="form-label">公司/基地名称</label> <input type="text" class="form-control" placeholder="请输入公司或基地名称"> </div> <div class="mb-3"> <label class="form-label">联系人</label> <input type="text" class="form-control" placeholder="请输入联系人姓名"> </div> <div class="mb-3"> <label class="form-label">联系电话</label> <input type="tel" class="form-control" placeholder="请输入联系电话"> </div> <div class="mb-3"> <label class="form-label">电子邮箱</label> <input type="email" class="form-control" placeholder="请输入电子邮箱"> </div> <div class="mb-3"> <label class="form-label">物流需求</label> <select class="form-select"> <option>请选择物流需求</option> <option>标准冷链配送</option> <option>加急冷链配送</option> <option>仓储配送一体化</option> <option>生鲜定制配送</option> <option>其他</option> </select> </div> <div class="mb-3"> <label class="form-label">需求描述</label> <textarea class="form-control" rows="4" placeholder="请详细描述您的物流需求，包括产品类型、数量、配送范围等"></textarea> </div> <div class="d-grid"> <button class="btn btn-primary" style="background-color: #8e44ad; border-color: #8e44ad;"><i class="fas fa-paper-plane me-2"></i>提交申请</button> </div> </div> </div> </div> </div> </div> <!-- 页脚 -->
<div id="footer-container"></div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="js/login-config.js"></script>
<script src="js/components.js"></script>
<script>
// 使用 common.js 加载导航栏

// 切换FAQ显示/隐藏
function toggleFaq(element) {
const answer = element.nextElementSibling;
const icon = element.querySelector('i');
if (answer.classList.contains('active')) {
answer.classList.remove('active');
icon.classList.remove('fa-chevron-up');
icon.classList.add('fa-chevron-down');
} else {
answer.classList.add('active');
icon.classList.remove('fa-chevron-down');
icon.classList.add('fa-chevron-up');
}
}
// 物流查询按钮点击事件
document.getElementById('tracking-btn').addEventListener('click', function() {
document.getElementById('tracking-result').style.display = 'block';
});
// 立即选择按钮点击事件
document.querySelectorAll('.service-actions .btn, .price-actions .btn').forEach(btn => {
btn.addEventListener('click', function() {
const serviceName = this.closest('.service-card, .price-card').querySelector('.service-title, .price-title').textContent;
alert(`您已选择 ${serviceName} 服务，请在下单时选择此服务。`);
});
});
// 查看服务详情按钮点击事件
document.querySelectorAll('.partner-actions .btn').forEach(btn => {
btn.addEventListener('click', function() {
const partnerName = this.closest('.partner-card').querySelector('.partner-title').textContent;
alert(`正在查看 ${partnerName} 的服务详情，请稍后...`);
});
});
// 提交申请按钮点击事件
document.querySelector('.application-form .btn-primary').addEventListener('click', function(e) {
e.preventDefault();
alert('您的申请已提交成功！我们将尽快审核并与您联系。');
});
// 申请物流服务按钮点击事件
document.querySelector('.btn-primary.btn-lg').addEventListener('click', function() {
document.querySelector('.logistics-card:nth-child(3)').scrollIntoView({ behavior: 'smooth' });
});
// 物流查询按钮点击事件
document.querySelector('.btn-outline-light.btn-lg').addEventListener('click', function() {
document.querySelector('.logistics-card:nth-child(1)').scrollIntoView({ behavior: 'smooth' });
});
</script> </body> </html>