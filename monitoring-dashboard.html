<!DOCTYPE html> <html lang="zh-CN"> <head> <meta charset="UTF-8"> <meta name="viewport" content="width=device-width, initial-scale=1.0"> <title>监测系统主面板 - 智慧农业平台</title> <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet"> <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet"> <link rel="stylesheet" href="css/common.css"> <link rel="stylesheet" href="css/navbar.css"> <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" /> <script src="https://cdn.jsdelivr.net/npm/chart.js"></script> <style>
:root {
--primary-color: #28a745;
--secondary-color: #6c757d;
--light-bg: #f8f9fa;
--card-shadow: 0 4px 6px rgba(0,0,0,0.1);
--transition: all 0.3s ease;
}
body {
background-color: var(--light-bg);
font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}
.dashboard-header {
background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80');
background-size: cover;
background-position: center;
height: 200px;
color: white;
display: flex;
align-items: center;
margin-bottom: 30px;
border-radius: 10px;
}
.dashboard-card {
background: white;
padding: 20px;
border-radius: 10px;
box-shadow: var(--card-shadow);
margin-bottom: 30px;
min-height: auto;
}
.stat-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 20px;
transition: var(--transition);
}
.stat-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.stat-icon {
font-size: 2rem;
color: var(--primary-color);
margin-bottom: 10px;
}
.stat-value {
font-size: 1.5rem;
font-weight: bold;
color: var(--primary-color);
margin-bottom: 5px;
}
.stat-label {
font-size: 0.9rem;
color: var(--secondary-color);
}
.chart-container {
position: relative;
height: 300px;
width: 100%;
}
.alert-item {
padding: 10px 15px;
border-radius: 5px;
margin-bottom: 10px;
display: flex;
align-items: center;
}
.alert-icon {
margin-right: 15px;
font-size: 1.2rem;
}
.alert-content {
flex: 1;
}
.alert-title {
font-weight: bold;
margin-bottom: 5px;
}
.alert-desc {
font-size: 0.9rem;
}
.alert-warning {
background-color: rgba(255, 193, 7, 0.2);
border-left: 4px solid #ffc107;
}
.alert-danger {
background-color: rgba(220, 53, 69, 0.2);
border-left: 4px solid #dc3545;
}
.alert-info {
background-color: rgba(13, 202, 240, 0.2);
border-left: 4px solid #0dcaf0;
}
.device-card {
display: flex;
align-items: center;
padding: 15px;
border-radius: 10px;
background: var(--light-bg);
margin-bottom: 15px;
transition: var(--transition);
}
.device-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.device-icon {
width: 50px;
height: 50px;
background-color: white;
border-radius: 50%;
display: flex;
align-items: center;
justify-content: center;
margin-right: 15px;
color: var(--primary-color);
font-size: 1.5rem;
}
.device-info {
flex: 1;
}
.device-name {
font-weight: bold;
margin-bottom: 5px;
}
.device-status {
font-size: 0.9rem;
color: var(--secondary-color);
}
.device-status.online {
color: var(--primary-color);
}
.device-status.offline {
color: #dc3545;
}
.device-actions {
margin-left: 15px;
}
.map-container {
height: 400px;
border-radius: 10px;
overflow: hidden;
margin-bottom: 20px;
}
#map {
height: 400px;
width: 100%;
}
.sensor-marker {
width: 30px;
height: 30px;
background-color: var(--primary-color);
border-radius: 50%;
display: flex;
align-items: center;
justify-content: center;
color: white;
font-weight: bold;
}
.sensor-marker.warning {
background-color: #ffc107;
}
.sensor-marker.danger {
background-color: #dc3545;
}
.task-item {
display: flex;
align-items: center;
padding: 10px;
border-bottom: 1px solid #eee;
}
.task-checkbox {
margin-right: 15px;
}
.task-name {
flex: 1;
}
.task-date {
margin-right: 15px;
color: var(--secondary-color);
font-size: 0.9rem;
}
.task-status {
display: inline-block;
padding: 3px 8px;
border-radius: 15px;
font-size: 0.8rem;
}
.status-pending {
background-color: #ffc107;
color: #212529;
}
.status-completed {
background-color: #28a745;
color: white;
}
.status-overdue {
background-color: #dc3545;
color: white;
}
.weather-item {
display: flex;
align-items: center;
margin-bottom: 15px;
}
.weather-icon {
font-size: 1.5rem;
margin-right: 15px;
width: 40px;
text-align: center;
}
.weather-info {
flex: 1;
}
.weather-date {
font-weight: bold;
margin-bottom: 5px;
}
.weather-desc {
color: var(--secondary-color);
font-size: 0.9rem;
}
.weather-temp {
font-weight: bold;
min-width: 60px;
text-align: right;
}
.filter-btn {
padding: 5px 15px;
border-radius: 20px;
background-color: white;
border: 1px solid #ddd;
margin-right: 10px;
margin-bottom: 10px;
cursor: pointer;
transition: var(--transition);
}
.filter-btn.active {
background-color: var(--primary-color);
color: white;
border-color: var(--primary-color);
}
.filter-btn:hover:not(.active) {
background-color: #f0f0f0;
}
.quick-action {
display: flex;
flex-direction: column;
align-items: center;
padding: 15px;
border-radius: 10px;
background: var(--light-bg);
margin-bottom: 15px;
transition: var(--transition);
cursor: pointer;
}
.quick-action:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.quick-action-icon {
font-size: 2rem;
color: var(--primary-color);
margin-bottom: 10px;
}
.quick-action-label {
font-size: 0.9rem;
text-align: center;
}
</style> </head> <body> <!-- 导航栏 -->
<div id="navbar-container"></div>
<!-- 主要内容 --> <div class="container py-5"> <div class="dashboard-header"> <div class="container"> <h1>监测系统主面板</h1> <p class="lead">实时监控农田环境数据，智能分析和预警，提高农业生产效率</p> </div> </div> <!-- 顶部统计卡片 --> <div class="row mb-4"> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-thermometer-half stat-icon"></i> <div class="stat-value">26.5°C</div> <div class="stat-label">平均温度</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-tint stat-icon"></i> <div class="stat-value">68%</div> <div class="stat-label">平均湿度</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-sun stat-icon"></i> <div class="stat-value">856 lux</div> <div class="stat-label">光照强度</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-cloud-rain stat-icon"></i> <div class="stat-value">0.0 mm</div> <div class="stat-label">今日降雨量</div> </div> </div> </div> <!-- 主要内容区域 --> <div class="row"> <!-- 左侧内容 --> <div class="col-md-8"> <!-- 传感器地图 --> <div class="dashboard-card"> <div class="d-flex justify-content-between align-items-center mb-4"> <h4>传感器分布地图</h4> <div> <button class="filter-btn active">全部</button> <button class="filter-btn">土壤传感器</button> <button class="filter-btn">气象站</button> <button class="filter-btn">灌溉设备</button> </div> </div> <div class="map-container"> <div id="map"></div> </div> </div> <!-- 环境数据图表 --> <div class="dashboard-card"> <div class="d-flex justify-content-between align-items-center mb-4"> <h4>环境数据趋势</h4> <div> <button class="btn btn-sm btn-outline-success me-2">日</button> <button class="btn btn-sm btn-success me-2">周</button> <button class="btn btn-sm btn-outline-success">月</button> </div> </div> <div class="chart-container"> <canvas id="environmentChart"></canvas> </div> </div> <!-- 设备状态 --> <div class="dashboard-card"> <div class="d-flex justify-content-between align-items-center mb-4"> <h4>设备状态</h4> <a href="#" class="btn btn-sm btn-outline-success">查看全部</a> </div> <div class="device-card"> <div class="device-icon"> <i class="fas fa-thermometer-half"></i> </div> <div class="device-info"> <div class="device-name">土壤传感器 #001</div> <div class="device-status online">在线 - 电池电量: 85%</div> </div> <div class="device-actions"> <button class="btn btn-sm btn-outline-success">查看数据</button> </div> </div> <div class="device-card"> <div class="device-icon"> <i class="fas fa-wind"></i> </div> <div class="device-info"> <div class="device-name">气象站 #002</div> <div class="device-status online">在线 - 电池电量: 92%</div> </div> <div class="device-actions"> <button class="btn btn-sm btn-outline-success">查看数据</button> </div> </div> <div class="device-card"> <div class="device-icon"> <i class="fas fa-tint"></i> </div> <div class="device-info"> <div class="device-name">灌溉控制器 #003</div> <div class="device-status online">在线 - 电池电量: 78%</div> </div> <div class="device-actions"> <button class="btn btn-sm btn-outline-success">查看数据</button> </div> </div> <div class="device-card"> <div class="device-icon"> <i class="fas fa-camera"></i> </div> <div class="device-info"> <div class="device-name">监控摄像头 #004</div> <div class="device-status offline">离线 - 最后在线时间: 2小时前</div> </div> <div class="device-actions"> <button class="btn btn-sm btn-outline-danger">检查设备</button> </div> </div> </div> </div> <!-- 右侧内容 --> <div class="col-md-4"> <!-- 快速操作 --> <div class="dashboard-card"> <h4 class="mb-4">快速操作</h4> <div class="row"> <div class="col-6"> <div class="quick-action" onclick="location.href='soil-monitoring.html'"> <i class="fas fa-seedling quick-action-icon"></i> <div class="quick-action-label">土壤监测</div> </div> </div> <div class="col-6"> <div class="quick-action" onclick="location.href='weather-monitoring.html'"> <i class="fas fa-cloud-sun quick-action-icon"></i> <div class="quick-action-label">气象监测</div> </div> </div> <div class="col-6"> <div class="quick-action" onclick="location.href='pest-monitoring.html'"> <i class="fas fa-bug quick-action-icon"></i> <div class="quick-action-label">病虫害监测</div> </div> </div> <div class="col-6"> <div class="quick-action" onclick="location.href='irrigation-control.html'"> <i class="fas fa-faucet quick-action-icon"></i> <div class="quick-action-label">灌溉控制</div> </div> </div> </div> </div> <!-- 预警信息 --> <div class="dashboard-card"> <h4 class="mb-4">预警信息</h4> <div class="alert-item alert-danger"> <div class="alert-icon"> <i class="fas fa-exclamation-circle"></i> </div> <div class="alert-content"> <div class="alert-title">土壤湿度过低</div> <div class="alert-desc">A区块土壤湿度低于30%，建议及时灌溉</div> </div> </div> <div class="alert-item alert-warning"> <div class="alert-icon"> <i class="fas fa-exclamation-triangle"></i> </div> <div class="alert-content"> <div class="alert-title">病虫害风险</div> <div class="alert-desc">B区块检测到稻飞虱发生风险增加</div> </div> </div> <div class="alert-item alert-info"> <div class="alert-icon"> <i class="fas fa-info-circle"></i> </div> <div class="alert-content"> <div class="alert-title">天气预警</div> <div class="alert-desc">未来48小时可能有强降雨，请做好防涝准备</div> </div> </div> </div> <!-- 天气预报 --> <div class="dashboard-card"> <h4 class="mb-4">天气预报</h4> <div class="weather-item"> <div class="weather-icon"> <i class="fas fa-sun text-warning"></i> </div> <div class="weather-info"> <div class="weather-date">今天</div> <div class="weather-desc">晴朗</div> </div> <div class="weather-temp">28°C</div> </div> <div class="weather-item"> <div class="weather-icon"> <i class="fas fa-cloud-sun text-secondary"></i> </div> <div class="weather-info"> <div class="weather-date">明天</div> <div class="weather-desc">多云</div> </div> <div class="weather-temp">26°C</div> </div> <div class="weather-item"> <div class="weather-icon"> <i class="fas fa-cloud-rain text-info"></i> </div> <div class="weather-info"> <div class="weather-date">后天</div> <div class="weather-desc">小雨</div> </div> <div class="weather-temp">24°C</div> </div> </div> <!-- 待办任务 --> <div class="dashboard-card"> <div class="d-flex justify-content-between align-items-center mb-4"> <h4>待办任务</h4> <a href="#" class="btn btn-sm btn-outline-success">添加任务</a> </div> <div class="task-item"> <input type="checkbox" class="task-checkbox"> <div class="task-name">检查A区块灌溉系统</div> <div class="task-date">今天</div> </div> <div class="task-item"> <input type="checkbox" class="task-checkbox"> <div class="task-name">更换B区块传感器电池</div> <div class="task-date">明天</div> </div> <div class="task-item"> <input type="checkbox" class="task-checkbox"> <div class="task-name">病虫害防治</div> <div class="task-date">6月20日</div> </div> </div> </div> </div> </div> <!-- 页脚 -->
<div id="footer-container"></div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="js/login-config.js"></script>
<script src="js/components.js"></script>
<script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script> <script>
// 初始化地图
var map = L.map('map').setView([30.7741, 120.7551], 14);
L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
attribution: '© OpenStreetMap contributors'
}).addTo(map);
// 添加传感器标记
const sensors = [
{ id: 1, name: "土壤传感器 #001", position: [30.7741, 120.7551], type: "soil", status: "normal" },
{ id: 2, name: "气象站 #002", position: [30.7841, 120.7651], type: "weather", status: "normal" },
{ id: 3, name: "灌溉控制器 #003", position: [30.7641, 120.7451], type: "irrigation", status: "normal" },
{ id: 4, name: "土壤传感器 #005", position: [30.7541, 120.7351], type: "soil", status: "warning" },
{ id: 5, name: "监控摄像头 #004", position: [30.7941, 120.7751], type: "camera", status: "danger" }
];
sensors.forEach(sensor => {
const markerClass = sensor.status === 'normal' ? '' : sensor.status;
const marker = L.marker(sensor.position)
.bindPopup(`
<div class="text-center"> <h6>${sensor.name}</h6> <p class="mb-1">类型: ${sensor.type}</p> <p class="mb-1">状态: ${sensor.status}</p> <button class="btn btn-sm btn-success mt-2">查看详情</button> </div>
`)
.addTo(map);
});
// 初始化环境数据图表
var ctx = document.getElementById('environmentChart').getContext('2d');
var environmentChart = new Chart(ctx, {
type: 'line',
data: {
labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
datasets: [
{
label: '温度 (°C)',
data: [24, 25, 27, 26, 28, 27, 26],
borderColor: '#dc3545',
backgroundColor: 'rgba(220, 53, 69, 0.1)',
tension: 0.4,
fill: true
},
{
label: '湿度 (%)',
data: [65, 68, 62, 70, 65, 68, 68],
borderColor: '#0dcaf0',
backgroundColor: 'rgba(13, 202, 240, 0.1)',
tension: 0.4,
fill: true
},
{
label: '土壤湿度 (%)',
data: [45, 42, 40, 38, 35, 32, 30],
borderColor: '#28a745',
backgroundColor: 'rgba(40, 167, 69, 0.1)',
tension: 0.4,
fill: true
}
]
},
options: {
responsive: true,
maintainAspectRatio: false,
plugins: {
legend: {
position: 'top',
},
tooltip: {
mode: 'index',
intersect: false
}
},
scales: {
y: {
beginAtZero: false
}
}
}
});
// 筛选按钮点击事件
document.querySelectorAll('.filter-btn').forEach(btn => {
btn.addEventListener('click', function() {
document.querySelectorAll('.filter-btn').forEach(b => {
b.classList.remove('active');
});
this.classList.add('active');
});
});
// 任务复选框点击事件
document.querySelectorAll('.task-checkbox').forEach(checkbox => {
checkbox.addEventListener('change', function() {
const taskItem = this.closest('.task-item');
if (this.checked) {
taskItem.style.textDecoration = 'line-through';
taskItem.style.opacity = '0.6';
} else {
taskItem.style.textDecoration = 'none';
taskItem.style.opacity = '1';
}
});
});
</script> </body> </html>
