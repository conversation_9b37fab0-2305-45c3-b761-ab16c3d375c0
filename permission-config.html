<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>权限配置管理 - 智慧农业平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="css/navbar.css">
    
    <!-- 添加内联样式确保导航栏始终可见 -->
    <style>
    #navbar-container {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
        position: relative !important;
        z-index: 1000 !important;
    }
    
    .permission-card {
        background: white;
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }
    
    .role-badge {
        display: inline-block;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.8em;
        font-weight: bold;
        margin-left: 8px;
    }
    
    .role-user { background-color: #e3f2fd; color: #1976d2; }
    .role-admin { background-color: #f3e5f5; color: #7b1fa2; }
    .role-landlord { background-color: #e8f5e8; color: #388e3c; }
    .role-farmer { background-color: #fff3e0; color: #f57c00; }
    .role-guest { background-color: #f5f5f5; color: #616161; }
    
    .module-section {
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
    }
    
    .page-item {
        display: inline-block;
        background: #f8f9fa;
        padding: 4px 8px;
        margin: 2px;
        border-radius: 4px;
        font-size: 0.85em;
        border: 1px solid #dee2e6;
    }
    
    .page-item.allowed {
        background: #d4edda;
        border-color: #c3e6cb;
        color: #155724;
    }
    
    .page-item.denied {
        background: #f8d7da;
        border-color: #f5c6cb;
        color: #721c24;
    }
    
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 10px;
        margin-bottom: 20px;
    }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <div id="navbar-container"></div>

    <!-- 主要内容 -->
    <div class="container py-5">
        <div class="row">
            <div class="col-12">
                <h2 class="mb-4">
                    <i class="fas fa-shield-alt me-2"></i>权限配置管理
                </h2>
                <p class="text-muted mb-4">此页面用于查看和管理不同用户角色的权限配置。您可以查看每个用户角色可以访问的模块和页面。</p>
            </div>
        </div>
        
        <!-- 权限统计 -->
        <div class="row">
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <h3 id="totalUsers">4</h3>
                    <p class="mb-0">用户角色</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <h3 id="totalModules">6</h3>
                    <p class="mb-0">功能模块</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <h3 id="totalPages">29</h3>
                    <p class="mb-0">功能页面</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <h3 id="currentUserPages">0</h3>
                    <p class="mb-0">当前用户可访问</p>
                </div>
            </div>
        </div>
        
        <!-- 用户权限配置 -->
        <div class="row">
            <div class="col-12">
                <div class="permission-card">
                    <h5 class="mb-4">
                        <i class="fas fa-users me-2"></i>用户权限配置
                    </h5>
                    <div id="userPermissions"></div>
                </div>
            </div>
        </div>
        
        <!-- 模块页面详情 -->
        <div class="row">
            <div class="col-12">
                <div class="permission-card">
                    <h5 class="mb-4">
                        <i class="fas fa-sitemap me-2"></i>模块页面详情
                    </h5>
                    <div id="moduleDetails"></div>
                </div>
            </div>
        </div>
        
        <!-- 权限矩阵 -->
        <div class="row">
            <div class="col-12">
                <div class="permission-card">
                    <h5 class="mb-4">
                        <i class="fas fa-table me-2"></i>权限矩阵
                    </h5>
                    <div class="table-responsive">
                        <table class="table table-bordered" id="permissionMatrix">
                            <thead class="table-dark">
                                <tr>
                                    <th>页面</th>
                                    <th>未登录</th>
                                    <th>普通用户</th>
                                    <th>管理员</th>
                                    <th>地主</th>
                                    <th>农民</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <div id="footer-container"></div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/login-config.js"></script>
    <script src="js/components.js"></script>
    
    <script>
    // 立即执行函数，确保导航栏在页面加载过程中就开始处理
    (function() {
        console.log('权限配置页面脚本开始执行');
        
        // 确保导航栏可见
        function ensureNavbarVisible() {
            const navbarContainer = document.getElementById('navbar-container');
            if (navbarContainer) {
                navbarContainer.style.display = 'block';
                navbarContainer.style.visibility = 'visible';
                navbarContainer.style.opacity = '1';
                
                if (!navbarContainer.innerHTML || navbarContainer.innerHTML.trim() === '') {
                    if (typeof navbarHTML !== 'undefined') {
                        navbarContainer.innerHTML = navbarHTML;
                        
                        if (typeof loadLoginState === 'function') loadLoginState();
                        if (typeof updateNavbarDisplay === 'function') updateNavbarDisplay();
                        if (typeof setActiveNavItem === 'function') setActiveNavItem();
                        if (typeof setupLoginEvents === 'function') setupLoginEvents();
                    }
                }
            }
        }
        
        ensureNavbarVisible();
        
        document.addEventListener('DOMContentLoaded', function() {
            ensureNavbarVisible();
            initializePermissionConfig();
        });
        
        window.onload = function() {
            ensureNavbarVisible();
            initializePermissionConfig();
        };
    })();
    
    // 初始化权限配置页面
    function initializePermissionConfig() {
        updateStatistics();
        renderUserPermissions();
        renderModuleDetails();
        renderPermissionMatrix();
    }
    
    // 更新统计信息
    function updateStatistics() {
        const totalUsers = Object.keys(userAccounts).length;
        const totalModules = Object.keys(allPages).length;
        const totalPages = Object.values(allPages).reduce((sum, pages) => sum + pages.length, 0);
        
        document.getElementById('totalUsers').textContent = totalUsers;
        document.getElementById('totalModules').textContent = totalModules;
        document.getElementById('totalPages').textContent = totalPages;
        
        // 更新当前用户可访问页面数
        if (typeof currentUser !== 'undefined' && currentUser) {
            const summary = loginConfig.getUserPermissionsSummary(currentUser);
            document.getElementById('currentUserPages').textContent = summary.totalPages;
        } else {
            const guestPages = Object.values(loginConfig.guestPermissions).flat().length;
            document.getElementById('currentUserPages').textContent = guestPages;
        }
    }
    
    // 渲染用户权限配置
    function renderUserPermissions() {
        const container = document.getElementById('userPermissions');
        let html = '';
        
        // 未登录用户
        html += renderUserPermissionCard('guest', {
            displayName: '未登录用户',
            role: 'guest',
            permissions: loginConfig.guestPermissions
        });
        
        // 已注册用户
        Object.entries(userAccounts).forEach(([username, account]) => {
            html += renderUserPermissionCard(username, account);
        });
        
        container.innerHTML = html;
    }
    
    // 渲染单个用户权限卡片
    function renderUserPermissionCard(username, account) {
        const modules = account.permissions ? Object.keys(account.permissions) : [];
        const moduleNames = {
            'module1': '基地土地招租与订单种植',
            'module2': '农产品品牌化运营',
            'module3': '全过程监控系统',
            'module4': '二级分销体系',
            'module5': '农资农机交易系统',
            'module6': '运营支撑体系'
        };
        
        let totalPages = 0;
        if (username === 'guest') {
            totalPages = Object.values(account.permissions).flat().length;
        } else {
            modules.forEach(module => {
                const pages = loginConfig.getPagesForUserModule({username: username, role: account.role}, module);
                totalPages += pages.length;
            });
        }
        
        return `
            <div class="module-section">
                <h6>
                    ${account.displayName}
                    <span class="role-badge role-${account.role}">${account.role.toUpperCase()}</span>
                    <small class="text-muted ms-2">(${modules.length} 个模块, ${totalPages} 个页面)</small>
                </h6>
                <div class="mt-2">
                    ${modules.map(module => `
                        <span class="badge bg-primary me-1">${moduleNames[module]}</span>
                    `).join('')}
                </div>
            </div>
        `;
    }
    
    // 渲染模块详情
    function renderModuleDetails() {
        const container = document.getElementById('moduleDetails');
        let html = '';
        
        const moduleNames = {
            'module1': '基地土地招租与订单种植',
            'module2': '农产品品牌化运营',
            'module3': '全过程监控系统',
            'module4': '二级分销体系',
            'module5': '农资农机交易系统',
            'module6': '运营支撑体系'
        };
        
        Object.entries(allPages).forEach(([module, pages]) => {
            html += `
                <div class="module-section">
                    <h6><i class="fas fa-folder me-2"></i>${moduleNames[module]} (${pages.length} 个页面)</h6>
                    <div class="mt-2">
                        ${pages.map(page => `
                            <span class="page-item" title="${page.file}">${page.name}</span>
                        `).join('')}
                    </div>
                </div>
            `;
        });
        
        container.innerHTML = html;
    }
    
    // 渲染权限矩阵
    function renderPermissionMatrix() {
        const tbody = document.querySelector('#permissionMatrix tbody');
        let html = '';
        
        // 获取所有页面
        const allPagesList = [];
        Object.entries(allPages).forEach(([module, pages]) => {
            pages.forEach(page => {
                allPagesList.push({
                    module: module,
                    file: page.file,
                    name: page.name
                });
            });
        });
        
        // 为每个页面生成权限矩阵行
        allPagesList.forEach(page => {
            const guestHasAccess = loginConfig.guestPermissions[page.module] && 
                                 loginConfig.guestPermissions[page.module].includes(page.file);
            
            html += `
                <tr>
                    <td><strong>${page.name}</strong><br><small class="text-muted">${page.file}</small></td>
                    <td class="text-center">${guestHasAccess ? '<i class="fas fa-check text-success"></i>' : '<i class="fas fa-times text-danger"></i>'}</td>
            `;
            
            // 检查每个用户角色的权限
            Object.entries(userAccounts).forEach(([username, account]) => {
                const hasAccess = loginConfig.hasPagePermission({username: username, role: account.role}, page.file);
                html += `<td class="text-center">${hasAccess ? '<i class="fas fa-check text-success"></i>' : '<i class="fas fa-times text-danger"></i>'}</td>`;
            });
            
            html += '</tr>';
        });
        
        tbody.innerHTML = html;
    }
    
    // 监听登录状态变化
    setInterval(function() {
        updateStatistics();
    }, 1000);
    </script>
</body>
</html>
