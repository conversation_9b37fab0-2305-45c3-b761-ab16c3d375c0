<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>权限配置编辑器 - 智慧农业平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="css/navbar.css">

    <!-- 添加内联样式确保导航栏始终可见 -->
    <style>
    #navbar-container {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
        position: relative !important;
        z-index: 1000 !important;
    }

    .config-card {
        background: white;
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }

    .role-badge {
        display: inline-block;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.8em;
        font-weight: bold;
        margin-left: 8px;
    }

    .role-user { background-color: #e3f2fd; color: #1976d2; }
    .role-admin { background-color: #f3e5f5; color: #7b1fa2; }
    .role-landlord { background-color: #e8f5e8; color: #388e3c; }
    .role-farmer { background-color: #fff3e0; color: #f57c00; }
    .role-guest { background-color: #f5f5f5; color: #616161; }

    .module-config {
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        background: #f8f9fa;
    }

    .page-checkbox {
        margin: 5px 0;
    }

    .page-checkbox label {
        font-weight: normal;
        margin-left: 8px;
    }

    .config-actions {
        position: sticky;
        top: 20px;
        background: white;
        padding: 15px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .user-tab {
        cursor: pointer;
        padding: 10px 15px;
        margin: 2px;
        border-radius: 6px;
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        transition: all 0.3s;
    }

    .user-tab.active {
        background: #007bff;
        color: white;
        border-color: #007bff;
    }

    .user-tab:hover {
        background: #e9ecef;
    }

    .user-tab.active:hover {
        background: #0056b3;
    }

    .save-indicator {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
    }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <div id="navbar-container"></div>

    <!-- 保存指示器 -->
    <div id="saveIndicator" class="save-indicator"></div>

    <!-- 主要内容 -->
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <h2 class="mb-4">
                    <i class="fas fa-edit me-2"></i>权限配置编辑器
                </h2>
                <p class="text-muted mb-4">在此页面可以可视化地编辑用户权限配置。修改后点击"保存配置"按钮应用更改。</p>
            </div>
        </div>

        <div class="row">
            <!-- 左侧：用户选择和配置操作 -->
            <div class="col-md-3">
                <div class="config-actions">
                    <h5 class="mb-3">
                        <i class="fas fa-users me-2"></i>选择用户角色
                    </h5>

                    <!-- 用户角色选择 -->
                    <div id="userTabs" class="mb-4">
                        <div class="user-tab" data-user="guest">
                            <i class="fas fa-user-slash me-2"></i>未登录用户
                            <span class="role-badge role-guest">GUEST</span>
                        </div>
                        <div class="user-tab" data-user="user">
                            <i class="fas fa-user me-2"></i>普通用户
                            <span class="role-badge role-user">USER</span>
                        </div>
                        <div class="user-tab" data-user="admin">
                            <i class="fas fa-user-shield me-2"></i>管理员
                            <span class="role-badge role-admin">ADMIN</span>
                        </div>
                        <div class="user-tab" data-user="landlord">
                            <i class="fas fa-home me-2"></i>地主
                            <span class="role-badge role-landlord">LANDLORD</span>
                        </div>
                        <div class="user-tab" data-user="farmer">
                            <i class="fas fa-seedling me-2"></i>农民
                            <span class="role-badge role-farmer">FARMER</span>
                        </div>
                    </div>

                    <!-- 快速操作 -->
                    <h6 class="mb-3">快速操作</h6>
                    <div class="d-grid gap-2 mb-3">
                        <button class="btn btn-outline-success btn-sm" onclick="selectAllPages()">
                            <i class="fas fa-check-double me-1"></i>全选页面
                        </button>
                        <button class="btn btn-outline-warning btn-sm" onclick="clearAllPages()">
                            <i class="fas fa-times me-1"></i>清空页面
                        </button>
                        <button class="btn btn-outline-info btn-sm" onclick="resetToDefault()">
                            <i class="fas fa-undo me-1"></i>重置为默认
                        </button>
                    </div>

                    <!-- 保存配置 -->
                    <div class="d-grid gap-2">
                        <button class="btn btn-primary" onclick="saveConfiguration()">
                            <i class="fas fa-download me-1"></i>生成配置文件
                        </button>
                        <button class="btn btn-secondary" onclick="exportConfiguration()">
                            <i class="fas fa-file-export me-1"></i>导出备份
                        </button>
                        <button class="btn btn-outline-secondary" onclick="previewChanges()">
                            <i class="fas fa-eye me-1"></i>预览更改
                        </button>
                    </div>

                    <!-- 使用说明 -->
                    <div class="mt-3 p-2 bg-info bg-opacity-10 rounded">
                        <small class="text-info">
                            <i class="fas fa-info-circle me-1"></i>
                            <strong>使用说明：</strong><br>
                            1. 点击"生成配置文件"下载新的配置文件<br>
                            2. 将下载的文件替换 js/login-config.js<br>
                            3. 刷新页面即可看到新的权限配置
                        </small>
                    </div>

                    <!-- 当前用户信息 -->
                    <div class="mt-4 p-3 bg-light rounded">
                        <h6>当前编辑用户</h6>
                        <div id="currentUserInfo">
                            <p class="text-muted mb-0">请选择一个用户角色</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧：权限配置区域 -->
            <div class="col-md-9">
                <div class="config-card">
                    <h5 class="mb-4">
                        <i class="fas fa-cogs me-2"></i>权限配置
                        <span id="configTitle" class="text-muted"></span>
                    </h5>

                    <!-- 权限配置内容 -->
                    <div id="permissionConfig">
                        <div class="text-center text-muted py-5">
                            <i class="fas fa-arrow-left fa-2x mb-3"></i>
                            <p>请从左侧选择一个用户角色开始配置权限</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 预览模态框 -->
    <div class="modal fade" id="previewModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">配置预览</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <pre id="configPreview" class="bg-light p-3 rounded"></pre>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" onclick="applyPreviewedConfig()">应用配置</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <div id="footer-container"></div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/login-config.js"></script>
    <script src="js/components.js"></script>

    <script>
    // 当前编辑的用户
    let currentEditUser = null;

    // 配置数据副本（用于编辑）
    let configCopy = {
        userAccounts: JSON.parse(JSON.stringify(userAccounts)),
        guestPermissions: JSON.parse(JSON.stringify(loginConfig.guestPermissions))
    };

    // 立即执行函数，确保导航栏在页面加载过程中就开始处理
    (function() {
        console.log('权限编辑器页面脚本开始执行');

        // 确保导航栏可见
        function ensureNavbarVisible() {
            const navbarContainer = document.getElementById('navbar-container');
            if (navbarContainer) {
                navbarContainer.style.display = 'block';
                navbarContainer.style.visibility = 'visible';
                navbarContainer.style.opacity = '1';

                if (!navbarContainer.innerHTML || navbarContainer.innerHTML.trim() === '') {
                    if (typeof navbarHTML !== 'undefined') {
                        navbarContainer.innerHTML = navbarHTML;

                        if (typeof loadLoginState === 'function') loadLoginState();
                        if (typeof updateNavbarDisplay === 'function') updateNavbarDisplay();
                        if (typeof setActiveNavItem === 'function') setActiveNavItem();
                        if (typeof setupLoginEvents === 'function') setupLoginEvents();
                    }
                }
            }
        }

        ensureNavbarVisible();

        document.addEventListener('DOMContentLoaded', function() {
            ensureNavbarVisible();
            initializeEditor();
        });

        window.onload = function() {
            ensureNavbarVisible();
            initializeEditor();
        };
    })();

    // 初始化编辑器
    function initializeEditor() {
        setupUserTabs();
        // 默认选择第一个用户
        selectUser('guest');
    }

    // 设置用户标签页
    function setupUserTabs() {
        const userTabs = document.querySelectorAll('.user-tab');
        userTabs.forEach(tab => {
            tab.addEventListener('click', function() {
                const userId = this.getAttribute('data-user');
                selectUser(userId);
            });
        });
    }

    // 选择用户
    function selectUser(userId) {
        currentEditUser = userId;

        // 更新标签页状态
        document.querySelectorAll('.user-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-user="${userId}"]`).classList.add('active');

        // 更新当前用户信息
        updateCurrentUserInfo(userId);

        // 渲染权限配置
        renderPermissionConfig(userId);
    }

    // 更新当前用户信息
    function updateCurrentUserInfo(userId) {
        const infoDiv = document.getElementById('currentUserInfo');

        if (userId === 'guest') {
            const guestPages = Object.values(configCopy.guestPermissions).flat().length;
            infoDiv.innerHTML = `
                <p class="mb-1"><strong>未登录用户</strong> <span class="role-badge role-guest">GUEST</span></p>
                <small class="text-muted">当前可访问 ${guestPages} 个页面</small>
            `;
        } else {
            const account = configCopy.userAccounts[userId];
            if (account) {
                const modules = Object.keys(account.permissions || {}).length;
                let totalPages = 0;
                Object.entries(account.permissions || {}).forEach(([module, pages]) => {
                    if (pages === 'all') {
                        totalPages += allPages[module] ? allPages[module].length : 0;
                    } else if (Array.isArray(pages)) {
                        totalPages += pages.length;
                    }
                });

                infoDiv.innerHTML = `
                    <p class="mb-1"><strong>${account.displayName}</strong> <span class="role-badge role-${account.role}">${account.role.toUpperCase()}</span></p>
                    <small class="text-muted">当前可访问 ${modules} 个模块, ${totalPages} 个页面</small>
                `;
            }
        }
    }

    // 渲染权限配置
    function renderPermissionConfig(userId) {
        const configDiv = document.getElementById('permissionConfig');
        const titleSpan = document.getElementById('configTitle');

        if (userId === 'guest') {
            titleSpan.textContent = '- 未登录用户权限';
            configDiv.innerHTML = renderGuestPermissionConfig();
        } else {
            const account = configCopy.userAccounts[userId];
            titleSpan.textContent = `- ${account.displayName}权限`;
            configDiv.innerHTML = renderUserPermissionConfig(userId, account);
        }
    }

    // 渲染未登录用户权限配置
    function renderGuestPermissionConfig() {
        const moduleNames = {
            'module1': '基地土地招租与订单种植',
            'module2': '农产品品牌化运营',
            'module3': '全过程监控系统',
            'module4': '二级分销体系',
            'module5': '农资农机交易系统',
            'module6': '运营支撑体系'
        };

        let html = '<div class="alert alert-info"><i class="fas fa-info-circle me-2"></i>配置未登录用户可以访问的页面</div>';

        Object.entries(allPages).forEach(([module, pages]) => {
            const currentPermissions = configCopy.guestPermissions[module] || [];

            html += `
                <div class="module-config">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6 class="mb-0">
                            <i class="fas fa-folder me-2"></i>${moduleNames[module]}
                        </h6>
                        <div>
                            <button class="btn btn-sm btn-outline-success" onclick="selectAllInModule('guest', '${module}')">
                                <i class="fas fa-check-double"></i> 全选
                            </button>
                            <button class="btn btn-sm btn-outline-warning" onclick="clearAllInModule('guest', '${module}')">
                                <i class="fas fa-times"></i> 清空
                            </button>
                        </div>
                    </div>
                    <div class="row">
            `;

            pages.forEach(page => {
                const isChecked = currentPermissions.includes(page.file);
                html += `
                    <div class="col-md-6">
                        <div class="page-checkbox">
                            <input type="checkbox" id="guest_${module}_${page.file}"
                                   ${isChecked ? 'checked' : ''}
                                   onchange="updateGuestPermission('${module}', '${page.file}', this.checked)">
                            <label for="guest_${module}_${page.file}" title="${page.file}">
                                ${page.name}
                            </label>
                        </div>
                    </div>
                `;
            });

            html += `
                    </div>
                </div>
            `;
        });

        return html;
    }

    // 渲染用户权限配置
    function renderUserPermissionConfig(userId, account) {
        const moduleNames = {
            'module1': '基地土地招租与订单种植',
            'module2': '农产品品牌化运营',
            'module3': '全过程监控系统',
            'module4': '二级分销体系',
            'module5': '农资农机交易系统',
            'module6': '运营支撑体系'
        };

        let html = `<div class="alert alert-info"><i class="fas fa-info-circle me-2"></i>配置 ${account.displayName} 可以访问的模块和页面</div>`;

        Object.entries(allPages).forEach(([module, pages]) => {
            const currentPermissions = account.permissions[module];
            const hasModule = currentPermissions !== undefined;
            const isAllPages = currentPermissions === 'all';
            const selectedPages = Array.isArray(currentPermissions) ? currentPermissions : [];

            html += `
                <div class="module-config">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <input type="checkbox" id="module_${userId}_${module}"
                                   ${hasModule ? 'checked' : ''}
                                   onchange="toggleModule('${userId}', '${module}', this.checked)">
                            <label for="module_${userId}_${module}" class="fw-bold ms-2">
                                <i class="fas fa-folder me-2"></i>${moduleNames[module]}
                            </label>
                        </div>
                        <div>
                            <button class="btn btn-sm btn-outline-primary"
                                    onclick="setModuleAllPages('${userId}', '${module}')"
                                    ${!hasModule ? 'disabled' : ''}>
                                <i class="fas fa-globe"></i> 全部页面
                            </button>
                            <button class="btn btn-sm btn-outline-success"
                                    onclick="selectAllInModule('${userId}', '${module}')"
                                    ${!hasModule ? 'disabled' : ''}>
                                <i class="fas fa-check-double"></i> 全选
                            </button>
                            <button class="btn btn-sm btn-outline-warning"
                                    onclick="clearAllInModule('${userId}', '${module}')"
                                    ${!hasModule ? 'disabled' : ''}>
                                <i class="fas fa-times"></i> 清空
                            </button>
                        </div>
                    </div>

                    ${isAllPages ? '<div class="alert alert-success mb-3"><i class="fas fa-check-circle me-2"></i>此用户可访问该模块下的所有页面</div>' : ''}

                    <div class="row" ${!hasModule ? 'style="opacity: 0.5; pointer-events: none;"' : ''}>
            `;

            pages.forEach(page => {
                const isChecked = isAllPages || selectedPages.includes(page.file);
                html += `
                    <div class="col-md-6">
                        <div class="page-checkbox">
                            <input type="checkbox" id="${userId}_${module}_${page.file}"
                                   ${isChecked ? 'checked' : ''}
                                   ${isAllPages ? 'disabled' : ''}
                                   onchange="updateUserPermission('${userId}', '${module}', '${page.file}', this.checked)">
                            <label for="${userId}_${module}_${page.file}" title="${page.file}">
                                ${page.name}
                            </label>
                        </div>
                    </div>
                `;
            });

            html += `
                    </div>
                </div>
            `;
        });

        return html;
    }

    // 更新未登录用户权限
    function updateGuestPermission(module, pageFile, isChecked) {
        if (!configCopy.guestPermissions[module]) {
            configCopy.guestPermissions[module] = [];
        }

        if (isChecked) {
            if (!configCopy.guestPermissions[module].includes(pageFile)) {
                configCopy.guestPermissions[module].push(pageFile);
            }
        } else {
            configCopy.guestPermissions[module] = configCopy.guestPermissions[module].filter(p => p !== pageFile);
        }

        updateCurrentUserInfo('guest');
        showSaveIndicator('配置已修改，请保存');
    }

    // 切换模块权限
    function toggleModule(userId, module, isChecked) {
        if (!configCopy.userAccounts[userId].permissions) {
            configCopy.userAccounts[userId].permissions = {};
        }

        if (isChecked) {
            // 启用模块，默认给予该模块下的第一个页面权限
            const firstPage = allPages[module] && allPages[module][0] ? allPages[module][0].file : null;
            configCopy.userAccounts[userId].permissions[module] = firstPage ? [firstPage] : [];
        } else {
            // 禁用模块
            delete configCopy.userAccounts[userId].permissions[module];
        }

        updateCurrentUserInfo(userId);
        renderPermissionConfig(userId);
        showSaveIndicator('配置已修改，请保存');
    }

    // 设置模块为所有页面
    function setModuleAllPages(userId, module) {
        if (!configCopy.userAccounts[userId].permissions) {
            configCopy.userAccounts[userId].permissions = {};
        }

        configCopy.userAccounts[userId].permissions[module] = 'all';
        updateCurrentUserInfo(userId);
        renderPermissionConfig(userId);
        showSaveIndicator('配置已修改，请保存');
    }

    // 更新用户页面权限
    function updateUserPermission(userId, module, pageFile, isChecked) {
        if (!configCopy.userAccounts[userId].permissions) {
            configCopy.userAccounts[userId].permissions = {};
        }

        if (!configCopy.userAccounts[userId].permissions[module]) {
            configCopy.userAccounts[userId].permissions[module] = [];
        }

        let permissions = configCopy.userAccounts[userId].permissions[module];
        if (permissions === 'all') {
            // 如果当前是'all'，转换为具体页面列表
            permissions = allPages[module] ? allPages[module].map(p => p.file) : [];
            configCopy.userAccounts[userId].permissions[module] = permissions;
        }

        if (isChecked) {
            if (!permissions.includes(pageFile)) {
                permissions.push(pageFile);
            }
        } else {
            configCopy.userAccounts[userId].permissions[module] = permissions.filter(p => p !== pageFile);
        }

        // 如果没有任何页面被选中，移除整个模块
        if (configCopy.userAccounts[userId].permissions[module].length === 0) {
            delete configCopy.userAccounts[userId].permissions[module];
        }

        updateCurrentUserInfo(userId);
        showSaveIndicator('配置已修改，请保存');
    }

    // 选择模块下的所有页面
    function selectAllInModule(userId, module) {
        if (userId === 'guest') {
            configCopy.guestPermissions[module] = allPages[module] ? allPages[module].map(p => p.file) : [];
            updateCurrentUserInfo('guest');
            renderPermissionConfig('guest');
        } else {
            if (!configCopy.userAccounts[userId].permissions) {
                configCopy.userAccounts[userId].permissions = {};
            }
            configCopy.userAccounts[userId].permissions[module] = allPages[module] ? allPages[module].map(p => p.file) : [];
            updateCurrentUserInfo(userId);
            renderPermissionConfig(userId);
        }
        showSaveIndicator('配置已修改，请保存');
    }

    // 清空模块下的所有页面
    function clearAllInModule(userId, module) {
        if (userId === 'guest') {
            configCopy.guestPermissions[module] = [];
            updateCurrentUserInfo('guest');
            renderPermissionConfig('guest');
        } else {
            if (configCopy.userAccounts[userId].permissions) {
                delete configCopy.userAccounts[userId].permissions[module];
            }
            updateCurrentUserInfo(userId);
            renderPermissionConfig(userId);
        }
        showSaveIndicator('配置已修改，请保存');
    }

    // 全选当前用户的所有页面
    function selectAllPages() {
        if (!currentEditUser) return;

        if (currentEditUser === 'guest') {
            Object.keys(allPages).forEach(module => {
                configCopy.guestPermissions[module] = allPages[module].map(p => p.file);
            });
            updateCurrentUserInfo('guest');
            renderPermissionConfig('guest');
        } else {
            if (!configCopy.userAccounts[currentEditUser].permissions) {
                configCopy.userAccounts[currentEditUser].permissions = {};
            }
            Object.keys(allPages).forEach(module => {
                configCopy.userAccounts[currentEditUser].permissions[module] = 'all';
            });
            updateCurrentUserInfo(currentEditUser);
            renderPermissionConfig(currentEditUser);
        }
        showSaveIndicator('配置已修改，请保存');
    }

    // 清空当前用户的所有页面
    function clearAllPages() {
        if (!currentEditUser) return;

        if (currentEditUser === 'guest') {
            Object.keys(allPages).forEach(module => {
                configCopy.guestPermissions[module] = [];
            });
            updateCurrentUserInfo('guest');
            renderPermissionConfig('guest');
        } else {
            configCopy.userAccounts[currentEditUser].permissions = {};
            updateCurrentUserInfo(currentEditUser);
            renderPermissionConfig(currentEditUser);
        }
        showSaveIndicator('配置已修改，请保存');
    }

    // 重置为默认配置
    function resetToDefault() {
        if (!currentEditUser) return;

        if (confirm('确定要重置为默认配置吗？这将清除所有自定义权限设置。')) {
            // 清除localStorage中的配置
            if (typeof resetConfigToDefault === 'function') {
                resetConfigToDefault();
            } else {
                localStorage.removeItem('smartAgri_userAccounts');
                localStorage.removeItem('smartAgri_guestPermissions');
                location.reload();
            }
        }
    }

    // 显示保存指示器
    function showSaveIndicator(message) {
        const indicator = document.getElementById('saveIndicator');
        indicator.innerHTML = `
            <div class="alert alert-warning alert-dismissible fade show">
                <i class="fas fa-exclamation-triangle me-2"></i>${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        // 3秒后自动隐藏
        setTimeout(() => {
            indicator.innerHTML = '';
        }, 3000);
    }

    // 保存配置
    function saveConfiguration() {
        try {
            // 更新全局配置
            Object.assign(userAccounts, configCopy.userAccounts);
            Object.assign(loginConfig.guestPermissions, configCopy.guestPermissions);

            // 生成新的配置文件内容
            const newConfigContent = generateConfigFileContent();

            // 下载新的配置文件
            downloadConfigFile(newConfigContent);

            // 如果当前有用户登录，重新应用权限
            if (typeof updateNavbarDisplay === 'function') {
                updateNavbarDisplay();
            }

            showSuccessMessage('配置文件已生成并下载！请将下载的文件替换 js/login-config.js，然后刷新页面。');

            // 清除保存指示器
            document.getElementById('saveIndicator').innerHTML = '';

        } catch (error) {
            showErrorMessage('保存配置失败：' + error.message);
        }
    }

    // 生成配置文件内容
    function generateConfigFileContent() {
        const configContent = `/**
 * 登录配置文件
 * 用于配置未登录和不同用户角色登录后显示的页面
 * 此文件由权限配置编辑器自动生成，生成时间: ${new Date().toLocaleString()}
 */

// 所有页面定义
const allPages = ${JSON.stringify(allPages, null, 4)};

// 用户账号配置
const userAccounts = ${JSON.stringify(configCopy.userAccounts, null, 4)};

// 从localStorage加载配置
function loadConfigFromStorage() {
    try {
        const savedUserAccounts = localStorage.getItem('smartAgri_userAccounts');
        const savedGuestPermissions = localStorage.getItem('smartAgri_guestPermissions');

        if (savedUserAccounts) {
            const parsedAccounts = JSON.parse(savedUserAccounts);
            Object.assign(userAccounts, parsedAccounts);
            console.log('已从localStorage加载用户账号配置');
        }

        if (savedGuestPermissions) {
            const parsedPermissions = JSON.parse(savedGuestPermissions);
            Object.assign(loginConfig.guestPermissions, parsedPermissions);
            console.log('已从localStorage加载访客权限配置');
        }
    } catch (error) {
        console.error('加载配置失败:', error);
    }
}

// 保存配置到localStorage
function saveConfigToStorage() {
    try {
        localStorage.setItem('smartAgri_userAccounts', JSON.stringify(userAccounts));
        localStorage.setItem('smartAgri_guestPermissions', JSON.stringify(loginConfig.guestPermissions));
        console.log('配置已保存到localStorage');
        return true;
    } catch (error) {
        console.error('保存配置失败:', error);
        return false;
    }
}

// 重置配置到默认值
function resetConfigToDefault() {
    try {
        localStorage.removeItem('smartAgri_userAccounts');
        localStorage.removeItem('smartAgri_guestPermissions');

        // 重新加载默认配置
        location.reload();
        return true;
    } catch (error) {
        console.error('重置配置失败:', error);
        return false;
    }
}

// 登录配置
const loginConfig = {
    // 未登录时显示的模块和页面（默认配置）
    guestPermissions: ${JSON.stringify(configCopy.guestPermissions, null, 8)},

    // 根据用户角色获取可见模块
    getModulesForUser: function(user) {
        if (!user || !user.role) {
            return Object.keys(this.guestPermissions);
        }

        const account = userAccounts[user.username];
        return account ? Object.keys(account.permissions) : Object.keys(this.guestPermissions);
    },

    // 根据用户角色获取模块下的可见页面
    getPagesForUserModule: function(user, module) {
        if (!user || !user.role) {
            // 未登录用户
            return this.guestPermissions[module] || [];
        }

        const account = userAccounts[user.username];
        if (!account || !account.permissions[module]) {
            return [];
        }

        const modulePermissions = account.permissions[module];
        if (modulePermissions === 'all') {
            // 返回该模块下的所有页面
            return allPages[module] ? allPages[module].map(page => page.file) : [];
        } else if (Array.isArray(modulePermissions)) {
            // 返回指定的页面列表
            return modulePermissions;
        }

        return [];
    },

    // 检查用户是否有权限访问特定页面
    hasPagePermission: function(user, pageFile) {
        // 根据页面文件名确定所属模块
        const module = this.getModuleByPageFile(pageFile);
        if (!module) {
            return false;
        }

        const allowedPages = this.getPagesForUserModule(user, module);
        return allowedPages.includes(pageFile);
    },

    // 根据页面文件名获取所属模块
    getModuleByPageFile: function(pageFile) {
        for (const [module, pages] of Object.entries(allPages)) {
            if (pages.some(page => page.file === pageFile)) {
                return module;
            }
        }
        return null;
    },

    // 获取页面的显示名称
    getPageDisplayName: function(pageFile) {
        for (const [module, pages] of Object.entries(allPages)) {
            const page = pages.find(p => p.file === pageFile);
            if (page) {
                return page.name;
            }
        }
        return pageFile;
    },

    // 验证用户登录
    validateUser: function(username, password) {
        const account = userAccounts[username];
        if (account && account.password === password) {
            return {
                username: username,
                role: account.role,
                displayName: account.displayName,
                permissions: account.permissions
            };
        }
        return null;
    },

    // 获取用户权限摘要
    getUserPermissionsSummary: function(user) {
        if (!user || !user.role) {
            return {
                modules: Object.keys(this.guestPermissions),
                totalPages: Object.values(this.guestPermissions).flat().length
            };
        }

        const modules = this.getModulesForUser(user);
        let totalPages = 0;

        modules.forEach(module => {
            const pages = this.getPagesForUserModule(user, module);
            totalPages += pages.length;
        });

        return {
            modules: modules,
            totalPages: totalPages
        };
    }
};

// 页面加载时自动从localStorage加载配置
document.addEventListener('DOMContentLoaded', function() {
    loadConfigFromStorage();
});

// 如果DOMContentLoaded已经触发，立即加载配置
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', loadConfigFromStorage);
} else {
    loadConfigFromStorage();
}
`;

        return configContent;
    }

    // 下载配置文件
    function downloadConfigFile(content) {
        const blob = new Blob([content], { type: 'text/javascript' });
        const url = URL.createObjectURL(blob);

        const link = document.createElement('a');
        link.href = url;
        link.download = 'login-config.js';
        link.style.display = 'none';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        URL.revokeObjectURL(url);
    }

    // 导出配置
    function exportConfiguration() {
        const config = {
            userAccounts: configCopy.userAccounts,
            guestPermissions: configCopy.guestPermissions,
            exportTime: new Date().toISOString()
        };

        const dataStr = JSON.stringify(config, null, 2);
        const dataBlob = new Blob([dataStr], {type: 'application/json'});

        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `permission-config-${new Date().toISOString().split('T')[0]}.json`;
        link.click();

        showSuccessMessage('配置已导出到文件');
    }

    // 预览更改
    function previewChanges() {
        const config = {
            userAccounts: configCopy.userAccounts,
            guestPermissions: configCopy.guestPermissions
        };

        document.getElementById('configPreview').textContent = JSON.stringify(config, null, 2);

        const modal = new bootstrap.Modal(document.getElementById('previewModal'));
        modal.show();
    }

    // 应用预览的配置
    function applyPreviewedConfig() {
        saveConfiguration();
        const modal = bootstrap.Modal.getInstance(document.getElementById('previewModal'));
        modal.hide();
    }

    // 显示成功消息
    function showSuccessMessage(message) {
        const alert = document.createElement('div');
        alert.className = 'alert alert-success alert-dismissible fade show position-fixed';
        alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alert.innerHTML = `
            <strong>成功！</strong> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.body.appendChild(alert);

        setTimeout(() => {
            if (alert.parentNode) alert.parentNode.removeChild(alert);
        }, 3000);
    }

    // 显示错误消息
    function showErrorMessage(message) {
        const alert = document.createElement('div');
        alert.className = 'alert alert-danger alert-dismissible fade show position-fixed';
        alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alert.innerHTML = `
            <strong>错误！</strong> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.body.appendChild(alert);

        setTimeout(() => {
            if (alert.parentNode) alert.parentNode.removeChild(alert);
        }, 5000);
    }
    </script>
</body>
</html>
