<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>权限配置编辑器 - 智慧农业平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="css/navbar.css">
    
    <!-- 添加内联样式确保导航栏始终可见 -->
    <style>
    #navbar-container {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
        position: relative !important;
        z-index: 1000 !important;
    }
    
    .config-card {
        background: white;
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }
    
    .role-badge {
        display: inline-block;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.8em;
        font-weight: bold;
        margin-left: 8px;
    }
    
    .role-user { background-color: #e3f2fd; color: #1976d2; }
    .role-admin { background-color: #f3e5f5; color: #7b1fa2; }
    .role-landlord { background-color: #e8f5e8; color: #388e3c; }
    .role-farmer { background-color: #fff3e0; color: #f57c00; }
    .role-guest { background-color: #f5f5f5; color: #616161; }
    
    .module-config {
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        background: #f8f9fa;
    }
    
    .page-checkbox {
        margin: 5px 0;
    }
    
    .page-checkbox label {
        font-weight: normal;
        margin-left: 8px;
    }
    
    .config-actions {
        position: sticky;
        top: 20px;
        background: white;
        padding: 15px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .user-tab {
        cursor: pointer;
        padding: 10px 15px;
        margin: 2px;
        border-radius: 6px;
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        transition: all 0.3s;
    }
    
    .user-tab.active {
        background: #007bff;
        color: white;
        border-color: #007bff;
    }
    
    .user-tab:hover {
        background: #e9ecef;
    }
    
    .user-tab.active:hover {
        background: #0056b3;
    }
    
    .save-indicator {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
    }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <div id="navbar-container"></div>

    <!-- 保存指示器 -->
    <div id="saveIndicator" class="save-indicator"></div>

    <!-- 主要内容 -->
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <h2 class="mb-4">
                    <i class="fas fa-edit me-2"></i>权限配置编辑器
                </h2>
                <p class="text-muted mb-4">在此页面可以可视化地编辑用户权限配置。修改后点击"保存配置"按钮应用更改。</p>
            </div>
        </div>
        
        <div class="row">
            <!-- 左侧：用户选择和配置操作 -->
            <div class="col-md-3">
                <div class="config-actions">
                    <h5 class="mb-3">
                        <i class="fas fa-users me-2"></i>选择用户角色
                    </h5>
                    
                    <!-- 用户角色选择 -->
                    <div id="userTabs" class="mb-4">
                        <div class="user-tab" data-user="guest">
                            <i class="fas fa-user-slash me-2"></i>未登录用户
                            <span class="role-badge role-guest">GUEST</span>
                        </div>
                        <div class="user-tab" data-user="user">
                            <i class="fas fa-user me-2"></i>普通用户
                            <span class="role-badge role-user">USER</span>
                        </div>
                        <div class="user-tab" data-user="admin">
                            <i class="fas fa-user-shield me-2"></i>管理员
                            <span class="role-badge role-admin">ADMIN</span>
                        </div>
                        <div class="user-tab" data-user="landlord">
                            <i class="fas fa-home me-2"></i>地主
                            <span class="role-badge role-landlord">LANDLORD</span>
                        </div>
                        <div class="user-tab" data-user="farmer">
                            <i class="fas fa-seedling me-2"></i>农民
                            <span class="role-badge role-farmer">FARMER</span>
                        </div>
                    </div>
                    
                    <!-- 快速操作 -->
                    <h6 class="mb-3">快速操作</h6>
                    <div class="d-grid gap-2 mb-3">
                        <button class="btn btn-outline-success btn-sm" onclick="selectAllPages()">
                            <i class="fas fa-check-double me-1"></i>全选页面
                        </button>
                        <button class="btn btn-outline-warning btn-sm" onclick="clearAllPages()">
                            <i class="fas fa-times me-1"></i>清空页面
                        </button>
                        <button class="btn btn-outline-info btn-sm" onclick="resetToDefault()">
                            <i class="fas fa-undo me-1"></i>重置为默认
                        </button>
                    </div>
                    
                    <!-- 保存配置 -->
                    <div class="d-grid gap-2">
                        <button class="btn btn-primary" onclick="saveConfiguration()">
                            <i class="fas fa-save me-1"></i>保存配置
                        </button>
                        <button class="btn btn-secondary" onclick="exportConfiguration()">
                            <i class="fas fa-download me-1"></i>导出配置
                        </button>
                        <button class="btn btn-outline-secondary" onclick="previewChanges()">
                            <i class="fas fa-eye me-1"></i>预览更改
                        </button>
                    </div>
                    
                    <!-- 当前用户信息 -->
                    <div class="mt-4 p-3 bg-light rounded">
                        <h6>当前编辑用户</h6>
                        <div id="currentUserInfo">
                            <p class="text-muted mb-0">请选择一个用户角色</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 右侧：权限配置区域 -->
            <div class="col-md-9">
                <div class="config-card">
                    <h5 class="mb-4">
                        <i class="fas fa-cogs me-2"></i>权限配置
                        <span id="configTitle" class="text-muted"></span>
                    </h5>
                    
                    <!-- 权限配置内容 -->
                    <div id="permissionConfig">
                        <div class="text-center text-muted py-5">
                            <i class="fas fa-arrow-left fa-2x mb-3"></i>
                            <p>请从左侧选择一个用户角色开始配置权限</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 预览模态框 -->
    <div class="modal fade" id="previewModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">配置预览</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <pre id="configPreview" class="bg-light p-3 rounded"></pre>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" onclick="applyPreviewedConfig()">应用配置</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <div id="footer-container"></div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/login-config.js"></script>
    <script src="js/components.js"></script>
    
    <script>
    // 当前编辑的用户
    let currentEditUser = null;
    
    // 配置数据副本（用于编辑）
    let configCopy = {
        userAccounts: JSON.parse(JSON.stringify(userAccounts)),
        guestPermissions: JSON.parse(JSON.stringify(loginConfig.guestPermissions))
    };
    
    // 立即执行函数，确保导航栏在页面加载过程中就开始处理
    (function() {
        console.log('权限编辑器页面脚本开始执行');
        
        // 确保导航栏可见
        function ensureNavbarVisible() {
            const navbarContainer = document.getElementById('navbar-container');
            if (navbarContainer) {
                navbarContainer.style.display = 'block';
                navbarContainer.style.visibility = 'visible';
                navbarContainer.style.opacity = '1';
                
                if (!navbarContainer.innerHTML || navbarContainer.innerHTML.trim() === '') {
                    if (typeof navbarHTML !== 'undefined') {
                        navbarContainer.innerHTML = navbarHTML;
                        
                        if (typeof loadLoginState === 'function') loadLoginState();
                        if (typeof updateNavbarDisplay === 'function') updateNavbarDisplay();
                        if (typeof setActiveNavItem === 'function') setActiveNavItem();
                        if (typeof setupLoginEvents === 'function') setupLoginEvents();
                    }
                }
            }
        }
        
        ensureNavbarVisible();
        
        document.addEventListener('DOMContentLoaded', function() {
            ensureNavbarVisible();
            initializeEditor();
        });
        
        window.onload = function() {
            ensureNavbarVisible();
            initializeEditor();
        };
    })();
    
    // 初始化编辑器
    function initializeEditor() {
        setupUserTabs();
        // 默认选择第一个用户
        selectUser('guest');
    }
    
    // 设置用户标签页
    function setupUserTabs() {
        const userTabs = document.querySelectorAll('.user-tab');
        userTabs.forEach(tab => {
            tab.addEventListener('click', function() {
                const userId = this.getAttribute('data-user');
                selectUser(userId);
            });
        });
    }
    
    // 选择用户
    function selectUser(userId) {
        currentEditUser = userId;
        
        // 更新标签页状态
        document.querySelectorAll('.user-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-user="${userId}"]`).classList.add('active');
        
        // 更新当前用户信息
        updateCurrentUserInfo(userId);
        
        // 渲染权限配置
        renderPermissionConfig(userId);
    }
    
    // 更新当前用户信息
    function updateCurrentUserInfo(userId) {
        const infoDiv = document.getElementById('currentUserInfo');
        
        if (userId === 'guest') {
            const guestPages = Object.values(configCopy.guestPermissions).flat().length;
            infoDiv.innerHTML = `
                <p class="mb-1"><strong>未登录用户</strong> <span class="role-badge role-guest">GUEST</span></p>
                <small class="text-muted">当前可访问 ${guestPages} 个页面</small>
            `;
        } else {
            const account = configCopy.userAccounts[userId];
            if (account) {
                const modules = Object.keys(account.permissions || {}).length;
                let totalPages = 0;
                Object.entries(account.permissions || {}).forEach(([module, pages]) => {
                    if (pages === 'all') {
                        totalPages += allPages[module] ? allPages[module].length : 0;
                    } else if (Array.isArray(pages)) {
                        totalPages += pages.length;
                    }
                });
                
                infoDiv.innerHTML = `
                    <p class="mb-1"><strong>${account.displayName}</strong> <span class="role-badge role-${account.role}">${account.role.toUpperCase()}</span></p>
                    <small class="text-muted">当前可访问 ${modules} 个模块, ${totalPages} 个页面</small>
                `;
            }
        }
    }
    </script>
</body>
</html>
