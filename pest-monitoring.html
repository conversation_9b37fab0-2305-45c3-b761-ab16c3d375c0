<!DOCTYPE html> <html lang="zh-CN"> <head> <meta charset="UTF-8"> <meta name="viewport" content="width=device-width, initial-scale=1.0"> <title>病虫害监测 - 智慧农业平台</title> <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet"> <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet"> <link rel="stylesheet" href="css/common.css"> <link rel="stylesheet" href="css/navbar.css"> <script src="https://cdn.jsdelivr.net/npm/chart.js"></script> <style>
:root {
--primary-color: #28a745;
--secondary-color: #6c757d;
--light-bg: #f8f9fa;
--card-shadow: 0 4px 6px rgba(0,0,0,0.1);
--transition: all 0.3s ease;
}
body {
background-color: var(--light-bg);
font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}
.monitoring-header {
background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80');
background-size: cover;
background-position: center;
height: 200px;
color: white;
display: flex;
align-items: center;
margin-bottom: 30px;
border-radius: 10px;
}
.monitoring-card {
background: white;
padding: 20px;
border-radius: 10px;
box-shadow: var(--card-shadow);
margin-bottom: 30px;
min-height: auto;
}
.stat-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 20px;
transition: var(--transition);
}
.stat-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.stat-icon {
font-size: 2rem;
color: var(--primary-color);
margin-bottom: 10px;
}
.stat-value {
font-size: 1.5rem;
font-weight: bold;
color: var(--primary-color);
margin-bottom: 5px;
}
.stat-label {
font-size: 0.9rem;
color: var(--secondary-color);
}
.chart-container {
position: relative;
height: 300px;
width: 100%;
}
.map-container {
height: 400px;
border-radius: 10px;
overflow: hidden;
margin-bottom: 20px;
}
#map {
min-height: auto;
width: 100%;
}
.filter-btn {
padding: 5px 15px;
border-radius: 20px;
background-color: white;
border: 1px solid #ddd;
margin-right: 10px;
margin-bottom: 10px;
cursor: pointer;
transition: var(--transition);
}
.filter-btn.active {
background-color: var(--primary-color);
color: white;
border-color: var(--primary-color);
}
.filter-btn:hover:not(.active) {
background-color: #f0f0f0;
}
.pest-card {
display: flex;
align-items: center;
padding: 15px;
border-radius: 10px;
background: var(--light-bg);
margin-bottom: 15px;
transition: var(--transition);
}
.pest-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.pest-icon {
width: 50px;
height: 50px;
background-color: white;
border-radius: 50%;
display: flex;
align-items: center;
justify-content: center;
margin-right: 15px;
color: var(--primary-color);
font-size: 1.5rem;
}
.pest-info {
flex: 1;
}
.pest-name {
font-weight: bold;
margin-bottom: 5px;
}
.pest-status {
font-size: 0.9rem;
color: var(--secondary-color);
}
.pest-status.low {
color: var(--primary-color);
}
.pest-status.medium {
color: #ffc107;
}
.pest-status.high {
color: #dc3545;
}
.pest-actions {
margin-left: 15px;
}
.pest-detail-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 15px;
transition: var(--transition);
}
.pest-detail-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.pest-detail-title {
font-weight: bold;
margin-bottom: 10px;
display: flex;
align-items: center;
}
.pest-detail-icon {
margin-right: 10px;
color: var(--primary-color);
}
.pest-detail-value {
font-size: 1.5rem;
font-weight: bold;
margin-bottom: 5px;
}
.pest-detail-desc {
font-size: 0.9rem;
color: var(--secondary-color);
}
.pest-detail-status {
display: inline-block;
padding: 3px 8px;
border-radius: 15px;
font-size: 0.8rem;
margin-left: 10px;
}
.status-low {
background-color: rgba(40, 167, 69, 0.2);
color: var(--primary-color);
}
.status-medium {
background-color: rgba(255, 193, 7, 0.2);
color: #ffc107;
}
.status-high {
background-color: rgba(220, 53, 69, 0.2);
color: #dc3545;
}
.recommendation-card {
background-color: rgba(40, 167, 69, 0.1);
border-left: 4px solid var(--primary-color);
padding: 15px;
margin-bottom: 15px;
border-radius: 0 5px 5px 0;
}
.recommendation-title {
font-weight: bold;
margin-bottom: 10px;
display: flex;
align-items: center;
}
.recommendation-icon {
color: var(--primary-color);
margin-right: 10px;
}
.recommendation-content {
color: var(--secondary-color);
}
.alert-item {
padding: 10px 15px;
border-radius: 5px;
margin-bottom: 10px;
display: flex;
align-items: center;
}
.alert-icon {
margin-right: 15px;
font-size: 1.2rem;
}
.alert-content {
flex: 1;
}
.alert-title {
font-weight: bold;
margin-bottom: 5px;
}
.alert-desc {
font-size: 0.9rem;
}
.alert-warning {
background-color: rgba(255, 193, 7, 0.2);
border-left: 4px solid #ffc107;
}
.alert-danger {
background-color: rgba(220, 53, 69, 0.2);
border-left: 4px solid #dc3545;
}
.alert-info {
background-color: rgba(13, 202, 240, 0.2);
border-left: 4px solid #0dcaf0;
}
.history-item {
display: flex;
margin-bottom: 15px;
padding-bottom: 15px;
border-bottom: 1px solid #eee;
}
.history-icon {
width: 40px;
height: 40px;
background-color: var(--light-bg);
border-radius: 50%;
display: flex;
align-items: center;
justify-content: center;
margin-right: 15px;
color: var(--primary-color);
}
.history-content {
flex: 1;
}
.history-title {
font-weight: bold;
margin-bottom: 5px;
}
.history-time {
font-size: 0.9rem;
color: var(--secondary-color);
margin-bottom: 5px;
}
.history-desc {
font-size: 0.9rem;
}
.image-gallery {
display: grid;
grid-template-columns: repeat(2, 1fr);
gap: 15px;
margin-bottom: 20px;
}
.gallery-item {
border-radius: 10px;
overflow: hidden;
position: relative;
height: 150px;
}
.gallery-image {
width: 100%;
min-height: auto;
object-fit: cover;
}
.gallery-overlay {
position: absolute;
bottom: 0;
left: 0;
right: 0;
background: rgba(0,0,0,0.7);
color: white;
padding: 10px;
font-size: 0.9rem;
}
.treatment-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 15px;
transition: var(--transition);
}
.treatment-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.treatment-title {
font-weight: bold;
margin-bottom: 10px;
}
.treatment-desc {
color: var(--secondary-color);
margin-bottom: 15px;
}
.treatment-steps {
margin-bottom: 15px;
}
.treatment-step {
display: flex;
margin-bottom: 10px;
}
.step-number {
width: 25px;
height: 25px;
background-color: var(--primary-color);
color: white;
border-radius: 50%;
display: flex;
align-items: center;
justify-content: center;
margin-right: 10px;
font-size: 0.8rem;
font-weight: bold;
}
.step-content {
flex: 1;
}
</style> </head> <body> <!-- 导航栏 -->
<div id="navbar-container"></div>
<!-- 主要内容 --> <div class="container py-5"> <div class="monitoring-header"> <div class="container"> <h1>病虫害监测</h1> <p class="lead">实时监控农田病虫害情况，提供智能预警和防治建议，保障农作物健康生长</p> </div> </div> <!-- 顶部统计卡片 --> <div class="row mb-4"> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-bug stat-icon"></i> <div class="stat-value">3</div> <div class="stat-label">当前病虫害类型</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-exclamation-triangle stat-icon"></i> <div class="stat-value">1</div> <div class="stat-label">高风险警报</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-chart-line stat-icon"></i> <div class="stat-value">15%</div> <div class="stat-label">平均发病率</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-spray-can stat-icon"></i> <div class="stat-value">2</div> <div class="stat-label">待执行防治任务</div> </div> </div> </div> <!-- 主要内容区域 --> <div class="row"> <!-- 左侧内容 --> <div class="col-md-8"> <!-- 病虫害分布地图 --> <div class="monitoring-card"> <div class="d-flex justify-content-between align-items-center mb-4"> <h4>病虫害分布地图</h4> <div> <button class="filter-btn active">全部</button> <button class="filter-btn">虫害</button> <button class="filter-btn">真菌病害</button> <button class="filter-btn">病毒病害</button> </div> </div> <div class="map-container"> <div id="map"></div> </div> </div> <!-- 病虫害趋势图表 --> <div class="monitoring-card"> <div class="d-flex justify-content-between align-items-center mb-4"> <h4>病虫害发生趋势</h4> <div> <button class="btn btn-sm btn-outline-success me-2">日</button> <button class="btn btn-sm btn-success me-2">周</button> <button class="btn btn-sm btn-outline-success">月</button> </div> </div> <div class="chart-container"> <canvas id="pestChart"></canvas> </div> </div> <!-- 病虫害监测记录 --> <div class="monitoring-card"> <div class="d-flex justify-content-between align-items-center mb-4"> <h4>病虫害监测记录</h4> <a href="#" class="btn btn-sm btn-outline-success">查看全部</a> </div> <div class="pest-card"> <div class="pest-icon"> <i class="fas fa-bug"></i> </div> <div class="pest-info"> <div class="pest-name">稻飞虫</div> <div class="pest-status high">高风险 - 发生率: 25% - 区域: A区块</div> </div> <div class="pest-actions"> <button class="btn btn-sm btn-outline-danger">查看详情</button> </div> </div> <div class="pest-card"> <div class="pest-icon"> <i class="fas fa-disease"></i> </div> <div class="pest-info"> <div class="pest-name">稻灰飞虫</div> <div class="pest-status medium">中等风险 - 发生率: 15% - 区域: B区块</div> </div> <div class="pest-actions"> <button class="btn btn-sm btn-outline-warning">查看详情</button> </div> </div> <div class="pest-card"> <div class="pest-icon"> <i class="fas fa-virus"></i> </div> <div class="pest-info"> <div class="pest-name">稻灰发病</div> <div class="pest-status low">低风险 - 发生率: 5% - 区域: C区块</div> </div> <div class="pest-actions"> <button class="btn btn-sm btn-outline-success">查看详情</button> </div> </div> </div> <!-- 病虫害图像识别 --> <div class="monitoring-card"> <div class="d-flex justify-content-between align-items-center mb-4"> <h4>病虫害图像识别</h4> <button class="btn btn-sm btn-success">上传新图像</button> </div> <div class="image-gallery"> <div class="gallery-item"> <img src="images/pest1.jpg" alt="病虫害图像" class="gallery-image"> <div class="gallery-overlay">稻飞虫 - 识别率: 95%</div> </div> <div class="gallery-item"> <img src="images/pest2.jpg" alt="病虫害图像" class="gallery-image"> <div class="gallery-overlay">稻灰飞虫 - 识别率: 92%</div> </div> <div class="gallery-item"> <img src="images/pest3.jpg" alt="病虫害图像" class="gallery-image"> <div class="gallery-overlay">稻灰发病 - 识别率: 88%</div> </div> <div class="gallery-item"> <img src="images/pest4.jpg" alt="病虫害图像" class="gallery-image"> <div class="gallery-overlay">健康水稻 - 识别率: 98%</div> </div> </div> </div> </div> <!-- 右侧内容 --> <div class="col-md-4"> <!-- 当前病虫害详情 --> <div class="monitoring-card"> <h4 class="mb-4">当前病虫害详情</h4> <div class="pest-detail-card"> <div class="pest-detail-title"> <i class="fas fa-bug pest-detail-icon"></i> <span>稻飞虫</span> <span class="pest-detail-status status-high">高风险</span> </div> <div class="pest-detail-value">25%</div> <div class="pest-detail-desc">发生率，主要分布在A区块</div> <div class="mt-3"> <button class="btn btn-sm btn-outline-success me-2">查看详情</button> <button class="btn btn-sm btn-danger">防治方案</button> </div> </div> <div class="pest-detail-card"> <div class="pest-detail-title"> <i class="fas fa-disease pest-detail-icon"></i> <span>稻灰飞虫</span> <span class="pest-detail-status status-medium">中等风险</span> </div> <div class="pest-detail-value">15%</div> <div class="pest-detail-desc">发生率，主要分布在B区块</div> <div class="mt-3"> <button class="btn btn-sm btn-outline-success me-2">查看详情</button> <button class="btn btn-sm btn-warning">防治方案</button> </div> </div> <div class="pest-detail-card"> <div class="pest-detail-title"> <i class="fas fa-virus pest-detail-icon"></i> <span>稻灰发病</span> <span class="pest-detail-status status-low">低风险</span> </div> <div class="pest-detail-value">5%</div> <div class="pest-detail-desc">发生率，主要分布在C区块</div> <div class="mt-3"> <button class="btn btn-sm btn-outline-success me-2">查看详情</button> <button class="btn btn-sm btn-success">防治方案</button> </div> </div> </div> <!-- 防治方案 --> <div class="monitoring-card"> <h4 class="mb-4">防治方案</h4> <div class="treatment-card"> <div class="treatment-title">稻飞虫防治方案</div> <div class="treatment-desc">针对A区块稻飞虫高发生率情况，建议采取以下防治措施：</div> <div class="treatment-steps"> <div class="treatment-step"> <div class="step-number">1</div> <div class="step-content">定期监测虫情，及时发现并控制</div> </div> <div class="treatment-step"> <div class="step-number">2</div> <div class="step-content">使用生物农药《苯菌素》，每些用量50ml，稍稍稀释后均匀喷洒</div> </div> <div class="treatment-step"> <div class="step-number">3</div> <div class="step-content">在田间设置诱捕灯，诱杀成虫</div> </div> </div> <button class="btn btn-sm btn-success">开始执行</button> </div> </div> <!-- 预警信息 --> <div class="monitoring-card"> <h4 class="mb-4">预警信息</h4> <div class="alert-item alert-danger"> <div class="alert-icon"> <i class="fas fa-exclamation-circle"></i> </div> <div class="alert-content"> <div class="alert-title">稻飞虫高风险警报</div> <div class="alert-desc">A区块稻飞虫发生率超过25%，建议立即采取防治措施</div> </div> </div> <div class="alert-item alert-warning"> <div class="alert-icon"> <i class="fas fa-exclamation-triangle"></i> </div> <div class="alert-content"> <div class="alert-title">稻灰飞虫中等风险</div> <div class="alert-desc">B区块稻灰飞虫发生率达到15%，建议密切监测</div> </div> </div> <div class="alert-item alert-info"> <div class="alert-icon"> <i class="fas fa-info-circle"></i> </div> <div class="alert-content"> <div class="alert-title">天气变化提醒</div> <div class="alert-desc">未来三天气温升高，湿度增大，可能导致虫害增加</div> </div> </div> </div> <!-- 防治历史 --> <div class="monitoring-card"> <h4 class="mb-4">防治历史</h4> <div class="history-item"> <div class="history-icon"> <i class="fas fa-spray-can"></i> </div> <div class="history-content"> <div class="history-title">稻飞虫防治</div> <div class="history-time">2023-06-10 09:30</div> <div class="history-desc">对A区块进行了生物农药喷洒，用量50ml/些</div> </div> </div> <div class="history-item"> <div class="history-icon"> <i class="fas fa-lightbulb"></i> </div> <div class="history-content"> <div class="history-title">诱捕灯安装</div> <div class="history-time">2023-06-05 15:45</div> <div class="history-desc">在B区块安装了10直诱捕灯，用于稻灰飞虫防治</div> </div> </div> <div class="history-item"> <div class="history-icon"> <i class="fas fa-seedling"></i> </div> <div class="history-content"> <div class="history-title">农事操作</div> <div class="history-time">2023-05-20 08:15</div> <div class="history-desc">对C区块进行了水位调节，防治稻灰发病</div> </div> </div> </div> </div> </div> </div> <!-- 页脚 -->
<div id="footer-container"></div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="js/login-config.js"></script>
<script src="js/components.js"></script>
<script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script> <script>
// 使用 common.js 加载导航栏

// 初始化地图
var map = L.map('map').setView([30.7741, 120.7551], 14);
L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
attribution: '© OpenStreetMap contributors'
}).addTo(map);
// 添加病虫害标记
const pestAreas = [
{ id: 1, name: "稻飞虫区域", position: [30.7741, 120.7551], type: "insect", status: "high", value: "25%" },
{ id: 2, name: "稻灰飞虫区域", position: [30.7841, 120.7651], type: "insect", status: "medium", value: "15%" },
{ id: 3, name: "稻灰发病区域", position: [30.7641, 120.7451], type: "fungus", status: "low", value: "5%" }
];
pestAreas.forEach(area => {
// 创建圆形区域标记
const color = area.status === 'high' ? '#dc3545' : area.status === 'medium' ? '#ffc107' : '#28a745';
const circle = L.circle(area.position, {
color: color,
fillColor: color,
fillOpacity: 0.3,
radius: 300
}).addTo(map);
// 添加点击事件
circle.bindPopup(`
<div class="text-center"> <h6>${area.name}</h6> <p class="mb-1">类型: ${area.type}</p> <p class="mb-1">风险等级: ${area.status}</p> <p class="mb-1">发生率: ${area.value}</p> <button class="btn btn-sm btn-success mt-2">查看详情</button> </div>
`);
});
// 初始化病虫害趋势图表
var ctx = document.getElementById('pestChart').getContext('2d');
var pestChart = new Chart(ctx, {
type: 'line',
data: {
labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
datasets: [
{
label: '稻飞虫发生率 (%)',
data: [10, 12, 15, 18, 20, 22, 25],
borderColor: '#dc3545',
backgroundColor: 'rgba(220, 53, 69, 0.1)',
tension: 0.4,
fill: true
},
{
label: '稻灰飞虫发生率 (%)',
data: [8, 10, 12, 13, 14, 15, 15],
borderColor: '#ffc107',
backgroundColor: 'rgba(255, 193, 7, 0.1)',
tension: 0.4,
fill: true
},
{
label: '稻灰发病发生率 (%)',
data: [3, 3, 4, 4, 5, 5, 5],
borderColor: '#28a745',
backgroundColor: 'rgba(40, 167, 69, 0.1)',
tension: 0.4,
fill: true
}
]
},
options: {
responsive: true,
maintainAspectRatio: false,
plugins: {
legend: {
position: 'top',
},
tooltip: {
mode: 'index',
intersect: false
}
},
scales: {
y: {
beginAtZero: true,
max: 30
}
}
}
});
// 筛选按钮点击事件
document.querySelectorAll('.filter-btn').forEach(btn => {
btn.addEventListener('click', function() {
document.querySelectorAll('.filter-btn').forEach(b => {
b.classList.remove('active');
});
this.classList.add('active');
});
});
// 防治方案按钮点击事件
document.querySelector('.treatment-card .btn-success').addEventListener('click', function() {
alert('已开始执行稻飞虫防治方案，请在任务列表中查看进度！');
});
// 上传新图像按钮点击事件
document.querySelector('.monitoring-card .btn-success').addEventListener('click', function() {
alert('请选择要上传的病虫害图像！');
});

// 确保导航栏在页面加载完成后可见
window.onload = function() {
    // 确保导航栏可见
    const navbarContainer = document.getElementById('navbar-container');
    if (navbarContainer) {
        navbarContainer.style.display = '';
    }
};
</script>