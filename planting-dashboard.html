<!DOCTYPE html> <html lang="zh-CN"> <head> <meta charset="UTF-8"> <meta name="viewport" content="width=device-width, initial-scale=1.0"> <title>种植看板 - 智慧农业平台</title> <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet"> <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet"> <link rel="stylesheet" href="css/common.css"> <link rel="stylesheet" href="css/navbar.css"> <script src="https://cdn.jsdelivr.net/npm/chart.js"></script> <style>
:root {
--primary-color: #28a745;
--secondary-color: #6c757d;
--light-bg: #f8f9fa;
--card-shadow: 0 4px 6px rgba(0,0,0,0.1);
--transition: all 0.3s ease;
}
body {
background-color: var(--light-bg);
font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}
.dashboard-header {
background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80');
background-size: cover;
background-position: center;
height: 200px;
color: white;
display: flex;
align-items: center;
margin-bottom: 30px;
border-radius: 10px;
}
.dashboard-card {
background: white;
padding: 20px;
border-radius: 10px;
box-shadow: var(--card-shadow);
margin-bottom: 30px;
min-height: auto;
}
.stat-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 20px;
transition: var(--transition);
}
.stat-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.stat-icon {
font-size: 2rem;
color: var(--primary-color);
margin-bottom: 10px;
}
.stat-value {
font-size: 1.5rem;
font-weight: bold;
color: var(--primary-color);
margin-bottom: 5px;
}
.stat-label {
font-size: 0.9rem;
color: var(--secondary-color);
}
.progress-container {
margin-bottom: 20px;
}
.progress-label {
display: flex;
justify-content: space-between;
margin-bottom: 5px;
}
.progress-bar {
height: 10px;
border-radius: 5px;
background-color: #ddd;
overflow: hidden;
}
.progress-value {
min-height: auto;
background-color: var(--primary-color);
border-radius: 5px;
}
.task-item {
display: flex;
align-items: center;
padding: 10px;
border-bottom: 1px solid #eee;
}
.task-checkbox {
margin-right: 15px;
}
.task-name {
flex: 1;
}
.task-date {
margin-right: 15px;
color: var(--secondary-color);
font-size: 0.9rem;
}
.task-status {
display: inline-block;
padding: 3px 8px;
border-radius: 15px;
font-size: 0.8rem;
}
.status-pending {
background-color: #ffc107;
color: #212529;
}
.status-completed {
background-color: #28a745;
color: white;
}
.status-overdue {
background-color: #dc3545;
color: white;
}
.chart-container {
position: relative;
height: 300px;
width: 100%;
}
.weather-item {
display: flex;
align-items: center;
margin-bottom: 15px;
}
.weather-icon {
font-size: 1.5rem;
margin-right: 15px;
width: 40px;
text-align: center;
}
.weather-info {
flex: 1;
}
.weather-date {
font-weight: bold;
margin-bottom: 5px;
}
.weather-desc {
color: var(--secondary-color);
font-size: 0.9rem;
}
.weather-temp {
font-weight: bold;
min-width: 60px;
text-align: right;
}
.activity-item {
display: flex;
margin-bottom: 15px;
padding-bottom: 15px;
border-bottom: 1px solid #eee;
}
.activity-icon {
width: 40px;
height: 40px;
background-color: var(--light-bg);
border-radius: 50%;
display: flex;
align-items: center;
justify-content: center;
margin-right: 15px;
color: var(--primary-color);
}
.activity-content {
flex: 1;
}
.activity-title {
font-weight: bold;
margin-bottom: 5px;
}
.activity-time {
color: var(--secondary-color);
font-size: 0.8rem;
}
.crop-image {
width: 100%;
height: 150px;
object-fit: cover;
border-radius: 10px;
margin-bottom: 15px;
}
.crop-info {
margin-bottom: 15px;
}
.crop-name {
font-weight: bold;
font-size: 1.2rem;
margin-bottom: 5px;
}
.crop-variety {
color: var(--secondary-color);
font-size: 0.9rem;
margin-bottom: 10px;
}
.crop-data {
display: flex;
justify-content: space-between;
margin-bottom: 10px;
}
.crop-data-item {
text-align: center;
}
.crop-data-value {
font-weight: bold;
color: var(--primary-color);
}
.crop-data-label {
font-size: 0.8rem;
color: var(--secondary-color);
}
.alert-item {
padding: 10px 15px;
border-radius: 5px;
margin-bottom: 10px;
display: flex;
align-items: center;
}
.alert-icon {
margin-right: 15px;
font-size: 1.2rem;
}
.alert-content {
flex: 1;
}
.alert-title {
font-weight: bold;
margin-bottom: 5px;
}
.alert-desc {
font-size: 0.9rem;
}
.alert-warning {
background-color: rgba(255, 193, 7, 0.2);
border-left: 4px solid #ffc107;
}
.alert-danger {
background-color: rgba(220, 53, 69, 0.2);
border-left: 4px solid #dc3545;
}
.alert-info {
background-color: rgba(13, 202, 240, 0.2);
border-left: 4px solid #0dcaf0;
}
.filter-btn {
padding: 5px 15px;
border-radius: 20px;
background-color: white;
border: 1px solid #ddd;
margin-right: 10px;
margin-bottom: 10px;
cursor: pointer;
transition: var(--transition);
}
.filter-btn.active {
background-color: var(--primary-color);
color: white;
border-color: var(--primary-color);
}
.filter-btn:hover:not(.active) {
background-color: #f0f0f0;
}
</style> </head> <body> <!-- 导航栏 -->
<div id="navbar-container"></div>
<!-- 主要内容 --> <div class="container py-5"> <div class="dashboard-header"> <div class="container"> <h1>种植看板</h1> <p class="lead">实时监控您的种植进度和关键农事记录</p> </div> </div> <!-- 顶部统计卡片 --> <div class="row mb-4"> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-seedling stat-icon"></i> <div class="stat-value">300亩</div> <div class="stat-label">种植面积</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-calendar-alt stat-icon"></i> <div class="stat-value">120天</div> <div class="stat-label">已种植天数</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-chart-line stat-icon"></i> <div class="stat-value">65%</div> <div class="stat-label">生长进度</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-balance-scale stat-icon"></i> <div class="stat-value">600吨</div> <div class="stat-label">预计产量</div> </div> </div> </div> <!-- 主要内容区域 --> <div class="row"> <!-- 左侧内容 --> <div class="col-md-8"> <!-- 种植进度 --> <div class="dashboard-card"> <div class="d-flex justify-content-between align-items-center mb-4"> <h4>种植进度</h4> <div> <button class="filter-btn active">全部</button> <button class="filter-btn">已完成</button> <button class="filter-btn">进行中</button> <button class="filter-btn">待开始</button> </div> </div> <div class="progress-container"> <div class="progress-label"> <span>总体进度</span> <span>65%</span> </div> <div class="progress-bar"> <div class="progress-value" style="width: 65%;"></div> </div> </div> <div class="row"> <div class="col-md-6"> <div class="progress-container"> <div class="progress-label"> <span>土地准备</span> <span>100%</span> </div> <div class="progress-bar"> <div class="progress-value" style="width: 100%;"></div> </div> </div> </div> <div class="col-md-6"> <div class="progress-container"> <div class="progress-label"> <span>播种</span> <span>100%</span> </div> <div class="progress-bar"> <div class="progress-value" style="width: 100%;"></div> </div> </div> </div> <div class="col-md-6"> <div class="progress-container"> <div class="progress-label"> <span>生长管理</span> <span>75%</span> </div> <div class="progress-bar"> <div class="progress-value" style="width: 75%;"></div> </div> </div> </div> <div class="col-md-6"> <div class="progress-container"> <div class="progress-label"> <span>病虫害防治</span> <span>60%</span> </div> <div class="progress-bar"> <div class="progress-value" style="width: 60%;"></div> </div> </div> </div> <div class="col-md-6"> <div class="progress-container"> <div class="progress-label"> <span>施肥</span> <span>50%</span> </div> <div class="progress-bar"> <div class="progress-value" style="width: 50%;"></div> </div> </div> </div> <div class="col-md-6"> <div class="progress-container"> <div class="progress-label"> <span>收获</span> <span>0%</span> </div> <div class="progress-bar"> <div class="progress-value" style="width: 0%;"></div> </div> </div> </div> </div> </div> <!-- 生长数据图表 --> <div class="dashboard-card"> <div class="d-flex justify-content-between align-items-center mb-4"> <h4>生长数据</h4> <div> <button class="btn btn-sm btn-outline-success me-2">周</button> <button class="btn btn-sm btn-success me-2">月</button> <button class="btn btn-sm btn-outline-success">季</button> </div> </div> <div class="chart-container"> <canvas id="growthChart"></canvas> </div> </div> <!-- 最近农事记录 --> <div class="dashboard-card"> <div class="d-flex justify-content-between align-items-center mb-4"> <h4>最近农事记录</h4> <a href="#" class="btn btn-sm btn-outline-success">查看全部</a> </div> <div class="activity-item"> <div class="activity-icon"> <i class="fas fa-tint"></i> </div> <div class="activity-content"> <div class="activity-title">灌溉</div> <div class="activity-desc">进行了水稻田间灌溉，水位保持在5cm</div> <div class="activity-time">2023-06-15 08:30</div> </div> </div> <div class="activity-item"> <div class="activity-icon"> <i class="fas fa-prescription-bottle"></i> </div> <div class="activity-content"> <div class="activity-title">施肥</div> <div class="activity-desc">追施氮肥，每亩用量15kg</div> <div class="activity-time">2023-06-10 09:15</div> </div> </div> <div class="activity-item"> <div class="activity-icon"> <i class="fas fa-bug"></i> </div> <div class="activity-content"> <div class="activity-title">病虫害防治</div> <div class="activity-desc">发现少量稻飞虱，进行了防治喷洒</div> <div class="activity-time">2023-06-05 10:30</div> </div> </div> <div class="activity-item"> <div class="activity-icon"> <i class="fas fa-clipboard-check"></i> </div> <div class="activity-content"> <div class="activity-title">田间检查</div> <div class="activity-desc">检查水稻生长情况，长势良好</div> <div class="activity-time">2023-06-01 14:00</div> </div> </div> </div> </div> <!-- 右侧内容 --> <div class="col-md-4"> <!-- 作物信息 --> <div class="dashboard-card"> <h4 class="mb-4">作物信息</h4> <img src="images/rice.jpg" alt="水稻" class="crop-image" /> <div class="crop-info"> <div class="crop-name">水稻</div> <div class="crop-variety">品种：中稻9号</div> <div class="crop-data"> <div class="crop-data-item"> <div class="crop-data-value">3月10日</div> <div class="crop-data-label">播种日期</div> </div> <div class="crop-data-item"> <div class="crop-data-value">9月15日</div> <div class="crop-data-label">预计收获</div> </div> <div class="crop-data-item"> <div class="crop-data-value">2000kg/亩</div> <div class="crop-data-label">目标产量</div> </div> </div> </div> <button class="btn btn-outline-success w-100">查看详细信息</button> </div> <!-- 天气预报 --> <div class="dashboard-card"> <h4 class="mb-4">天气预报</h4> <div class="weather-item"> <div class="weather-icon"> <i class="fas fa-sun text-warning"></i> </div> <div class="weather-info"> <div class="weather-date">今天</div> <div class="weather-desc">晴朗</div> </div> <div class="weather-temp">28°C</div> </div> <div class="weather-item"> <div class="weather-icon"> <i class="fas fa-cloud-sun text-secondary"></i> </div> <div class="weather-info"> <div class="weather-date">明天</div> <div class="weather-desc">多云</div> </div> <div class="weather-temp">26°C</div> </div> <div class="weather-item"> <div class="weather-icon"> <i class="fas fa-cloud-rain text-info"></i> </div> <div class="weather-info"> <div class="weather-date">后天</div> <div class="weather-desc">小雨</div> </div> <div class="weather-temp">24°C</div> </div> <div class="weather-item"> <div class="weather-icon"> <i class="fas fa-cloud text-secondary"></i> </div> <div class="weather-info"> <div class="weather-date">周五</div> <div class="weather-desc">阴天</div> </div> <div class="weather-temp">25°C</div> </div> </div> <!-- 预警信息 --> <div class="dashboard-card"> <h4 class="mb-4">预警信息</h4> <div class="alert-item alert-warning"> <div class="alert-icon"> <i class="fas fa-exclamation-triangle"></i> </div> <div class="alert-content"> <div class="alert-title">病虫害风险</div> <div class="alert-desc">近期气温升高，稻飞虱发生风险增加</div> </div> </div> <div class="alert-item alert-info"> <div class="alert-icon"> <i class="fas fa-info-circle"></i> </div> <div class="alert-content"> <div class="alert-title">施肥提醒</div> <div class="alert-desc">水稻进入拔节期，建议追施穗肥</div> </div> </div> <div class="alert-item alert-danger"> <div class="alert-icon"> <i class="fas fa-bolt"></i> </div> <div class="alert-content"> <div class="alert-title">极端天气</div> <div class="alert-desc">未来一周可能有强降雨，请做好防涝准备</div> </div> </div> </div> <!-- 待办任务 --> <div class="dashboard-card"> <div class="d-flex justify-content-between align-items-center mb-4"> <h4>待办任务</h4> <a href="#" class="btn btn-sm btn-outline-success">添加任务</a> </div> <div class="task-item"> <input type="checkbox" class="task-checkbox"> <div class="task-name">检查水稻生长情况</div> <div class="task-date">今天</div> </div> <div class="task-item"> <input type="checkbox" class="task-checkbox"> <div class="task-name">准备追施穗肥</div> <div class="task-date">明天</div> </div> <div class="task-item"> <input type="checkbox" class="task-checkbox"> <div class="task-name">病虫害防治</div> <div class="task-date">6月20日</div> </div> <div class="task-item"> <input type="checkbox" class="task-checkbox"> <div class="task-name">调整水位</div> <div class="task-date">6月22日</div> </div> </div> </div> </div> </div> <!-- 页脚 -->
<div id="footer-container"></div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="js/login-config.js"></script>
<script src="js/components.js"></script>
<script>
// 初始化生长数据图表
var ctx = document.getElementById('growthChart').getContext('2d');
var growthChart = new Chart(ctx, {
type: 'line',
data: {
labels: ['3月', '4月', '5月', '6月', '7月', '8月', '9月'],
datasets: [
{
label: '株高 (cm)',
data: [5, 15, 35, 60, 80, 95, 100],
borderColor: '#28a745',
backgroundColor: 'rgba(40, 167, 69, 0.1)',
tension: 0.4,
fill: true
},
{
label: '叶面积指数',
data: [0.5, 1.5, 3.0, 4.5, 5.2, 5.0, 4.8],
borderColor: '#17a2b8',
backgroundColor: 'rgba(23, 162, 184, 0.1)',
tension: 0.4,
fill: true
}
]
},
options: {
responsive: true,
maintainAspectRatio: false,
plugins: {
legend: {
position: 'top',
},
tooltip: {
mode: 'index',
intersect: false
}
},
scales: {
y: {
beginAtZero: true
}
}
}
});
// 筛选按钮点击事件
document.querySelectorAll('.filter-btn').forEach(btn => {
btn.addEventListener('click', function() {
document.querySelectorAll('.filter-btn').forEach(b => {
b.classList.remove('active');
});
this.classList.add('active');
});
});
// 任务复选框点击事件
document.querySelectorAll('.task-checkbox').forEach(checkbox => {
checkbox.addEventListener('change', function() {
const taskItem = this.closest('.task-item');
if (this.checked) {
taskItem.style.textDecoration = 'line-through';
taskItem.style.opacity = '0.6';
} else {
taskItem.style.textDecoration = 'none';
taskItem.style.opacity = '1';
}
});
});
</script> </body> </html>
