<!DOCTYPE html> <html lang="zh-CN"> <head> <meta charset="UTF-8"> <meta name="viewport" content="width=device-width, initial-scale=1.0"> <title>种植计划 - 智慧农业平台</title> <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet"> <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet"> <link rel="stylesheet" href="css/common.css"> <link rel="stylesheet" href="css/navbar.css"> <style>
:root {
--primary-color: #28a745;
--secondary-color: #6c757d;
--light-bg: #f8f9fa;
--card-shadow: 0 4px 6px rgba(0,0,0,0.1);
--transition: all 0.3s ease;
}
body {
background-color: var(--light-bg);
font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}
.plan-header {
background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80');
background-size: cover;
background-position: center;
height: 200px;
color: white;
display: flex;
align-items: center;
margin-bottom: 30px;
border-radius: 10px;
}
.plan-container {
background: white;
padding: 30px;
border-radius: 10px;
box-shadow: var(--card-shadow);
margin-bottom: 30px;
}
.timeline {
position: relative;
padding: 20px 0;
}
.timeline::before {
content: '';
position: absolute;
top: 0;
bottom: 0;
left: 50px;
width: 2px;
background-color: #ddd;
}
.timeline-item {
position: relative;
padding-left: 80px;
margin-bottom: 30px;
}
.timeline-date {
position: absolute;
left: 0;
width: 50px;
text-align: center;
background-color: var(--primary-color);
color: white;
padding: 5px;
border-radius: 5px;
font-size: 0.8rem;
}
.timeline-content {
background-color: var(--light-bg);
padding: 15px;
border-radius: 10px;
position: relative;
}
.timeline-content::before {
content: '';
position: absolute;
top: 15px;
left: -10px;
width: 0;
height: 0;
border-top: 10px solid transparent;
border-bottom: 10px solid transparent;
border-right: 10px solid var(--light-bg);
}
.timeline-title {
font-weight: bold;
margin-bottom: 10px;
}
.timeline-description {
color: var(--secondary-color);
margin-bottom: 10px;
}
.timeline-actions {
display: flex;
justify-content: flex-end;
gap: 10px;
}
.task-status {
display: inline-block;
padding: 3px 8px;
border-radius: 15px;
font-size: 0.8rem;
margin-left: 10px;
}
.status-pending {
background-color: #ffc107;
color: #212529;
}
.status-completed {
background-color: #28a745;
color: white;
}
.status-overdue {
background-color: #dc3545;
color: white;
}
.plan-summary {
background-color: var(--light-bg);
padding: 20px;
border-radius: 10px;
margin-bottom: 20px;
}
.summary-item {
display: flex;
justify-content: space-between;
margin-bottom: 10px;
padding-bottom: 10px;
border-bottom: 1px solid #ddd;
}
.summary-item:last-child {
border-bottom: none;
}
.crop-selector {
margin-bottom: 20px;
}
.crop-option {
display: inline-block;
padding: 10px 15px;
margin-right: 10px;
margin-bottom: 10px;
border: 1px solid #ddd;
border-radius: 5px;
cursor: pointer;
transition: var(--transition);
}
.crop-option:hover {
border-color: var(--primary-color);
}
.crop-option.active {
background-color: var(--primary-color);
color: white;
border-color: var(--primary-color);
}
.crop-icon {
margin-right: 5px;
}
.task-item {
display: flex;
align-items: center;
padding: 10px;
border-bottom: 1px solid #eee;
}
.task-checkbox {
margin-right: 15px;
}
.task-name {
flex: 1;
}
.task-date {
margin-right: 15px;
color: var(--secondary-color);
font-size: 0.9rem;
}
.task-edit {
color: var(--primary-color);
cursor: pointer;
margin-left: 10px;
}
.task-delete {
color: #dc3545;
cursor: pointer;
margin-left: 10px;
}
.add-task-btn {
display: block;
width: 100%;
padding: 10px;
text-align: center;
background-color: var(--light-bg);
border: 1px dashed #ddd;
border-radius: 5px;
margin-top: 15px;
cursor: pointer;
transition: var(--transition);
}
.add-task-btn:hover {
background-color: #eee;
}
.progress-container {
margin-bottom: 20px;
}
.progress-label {
display: flex;
justify-content: space-between;
margin-bottom: 5px;
}
.progress-bar {
height: 10px;
border-radius: 5px;
background-color: #ddd;
overflow: hidden;
}
.progress-value {
min-height: auto;
background-color: var(--primary-color);
border-radius: 5px;
}
</style> </head> <body> <!-- 导航栏 -->
<div id="navbar-container"></div>
<!-- 主要内容 --> <div class="container py-5"> <div class="plan-header"> <div class="container"> <h1>种植计划</h1> <p class="lead">制定和管理您的农作物种植计划，提高种植效率</p> </div> </div> <div class="row"> <div class="col-md-8"> <!-- 种植计划容器 --> <div class="plan-container"> <div class="d-flex justify-content-between align-items-center mb-4"> <h4>浙江嘉兴水稻基地种植计划</h4> <div> <button class="btn btn-outline-success me-2"><i class="fas fa-print me-1"></i> 打印</button> <button class="btn btn-success"><i class="fas fa-edit me-1"></i> 编辑</button> </div> </div> <!-- 作物选择器 --> <div class="crop-selector"> <div class="crop-option active"> <i class="fas fa-seedling crop-icon"></i> 水稻
</div> <div class="crop-option"> <i class="fas fa-wheat-awn crop-icon"></i> 小麦
</div> <div class="crop-option"> <i class="fas fa-carrot crop-icon"></i> 蔬菜
</div> <div class="crop-option"> <i class="fas fa-apple-alt crop-icon"></i> 水果
</div> </div> <!-- 进度条 --> <div class="progress-container"> <div class="progress-label"> <span>种植进度</span> <span>35%</span> </div> <div class="progress-bar"> <div class="progress-value" style="width: 35%;"></div> </div> </div> <!-- 时间线 --> <div class="timeline"> <!-- 时间线项目1 --> <div class="timeline-item"> <div class="timeline-date">3月</div> <div class="timeline-content"> <div class="timeline-title">
土地准备
<span class="task-status status-completed">已完成</span> </div> <div class="timeline-description">
清理土地，进行深耕，施加基肥，准备育秧。
</div> <div class="timeline-actions"> <button class="btn btn-sm btn-outline-secondary">查看详情</button> <button class="btn btn-sm btn-outline-success">添加记录</button> </div> </div> </div> <!-- 时间线项目2 --> <div class="timeline-item"> <div class="timeline-date">4月</div> <div class="timeline-content"> <div class="timeline-title">
育秧
<span class="task-status status-completed">已完成</span> </div> <div class="timeline-description">
选择优质种子，进行浸种、催芽，育秧。
</div> <div class="timeline-actions"> <button class="btn btn-sm btn-outline-secondary">查看详情</button> <button class="btn btn-sm btn-outline-success">添加记录</button> </div> </div> </div> <!-- 时间线项目3 --> <div class="timeline-item"> <div class="timeline-date">5月</div> <div class="timeline-content"> <div class="timeline-title">
插秧
<span class="task-status status-pending">进行中</span> </div> <div class="timeline-description">
将秧苗移栽到准备好的稻田中，确保合理的密度和深度。
</div> <div class="timeline-actions"> <button class="btn btn-sm btn-outline-secondary">查看详情</button> <button class="btn btn-sm btn-outline-success">添加记录</button> </div> </div> </div> <!-- 时间线项目4 --> <div class="timeline-item"> <div class="timeline-date">6月</div> <div class="timeline-content"> <div class="timeline-title">
田间管理
<span class="task-status status-pending">待开始</span> </div> <div class="timeline-description">
水位管理，施肥，病虫害防治。
</div> <div class="timeline-actions"> <button class="btn btn-sm btn-outline-secondary">查看详情</button> <button class="btn btn-sm btn-outline-success">添加记录</button> </div> </div> </div> <!-- 时间线项目5 --> <div class="timeline-item"> <div class="timeline-date">9月</div> <div class="timeline-content"> <div class="timeline-title">
收获
<span class="task-status status-pending">待开始</span> </div> <div class="timeline-description">
在水稻成熟时进行收割，晾晒，储存。
</div> <div class="timeline-actions"> <button class="btn btn-sm btn-outline-secondary">查看详情</button> <button class="btn btn-sm btn-outline-success">添加记录</button> </div> </div> </div> </div> </div> <!-- 任务列表 --> <div class="plan-container"> <h4 class="mb-4">当前任务</h4> <div class="task-item"> <input type="checkbox" class="task-checkbox" checked> <div class="task-name">准备插秧所需工具和设备</div> <div class="task-date">5月10日</div> <div class="task-status status-completed">已完成</div> <i class="fas fa-edit task-edit"></i> <i class="fas fa-trash task-delete"></i> </div> <div class="task-item"> <input type="checkbox" class="task-checkbox"> <div class="task-name">检查水稻秧苗生长情况</div> <div class="task-date">5月12日</div> <div class="task-status status-pending">进行中</div> <i class="fas fa-edit task-edit"></i> <i class="fas fa-trash task-delete"></i> </div> <div class="task-item"> <input type="checkbox" class="task-checkbox"> <div class="task-name">开始插秧作业</div> <div class="task-date">5月15日</div> <div class="task-status status-pending">待开始</div> <i class="fas fa-edit task-edit"></i> <i class="fas fa-trash task-delete"></i> </div> <div class="task-item"> <input type="checkbox" class="task-checkbox"> <div class="task-name">插秧后水位管理</div> <div class="task-date">5月20日</div> <div class="task-status status-pending">待开始</div> <i class="fas fa-edit task-edit"></i> <i class="fas fa-trash task-delete"></i> </div> <div class="add-task-btn"> <i class="fas fa-plus me-2"></i> 添加新任务
</div> </div> </div> <div class="col-md-4"> <!-- 计划摘要 --> <div class="plan-container"> <h4 class="mb-4">计划摘要</h4> <div class="plan-summary"> <div class="summary-item"> <div>土地名称</div> <div>浙江嘉兴水稻基地</div> </div> <div class="summary-item"> <div>种植作物</div> <div>水稻</div> </div> <div class="summary-item"> <div>种植面积</div> <div>300亩</div> </div> <div class="summary-item"> <div>种植周期</div> <div>3月 - 9月</div> </div> <div class="summary-item"> <div>预计产量</div> <div>600吨</div> </div> </div> <div class="d-grid gap-2"> <button class="btn btn-success">修改计划</button> </div> </div> <!-- 农事建议 --> <div class="plan-container"> <h4 class="mb-4">智能农事建议</h4> <div class="alert alert-success"> <i class="fas fa-lightbulb me-2"></i> <strong>建议：</strong> 根据当前天气预报，建议在5月15日前完成插秧作业，避开即将到来的降雨。
</div> <div class="alert alert-warning"> <i class="fas fa-exclamation-triangle me-2"></i> <strong>注意：</strong> 近期可能有病虫害风险，建议提前做好防治准备。
</div> <div class="alert alert-info"> <i class="fas fa-info-circle me-2"></i> <strong>提示：</strong> 插秧后7天是水稻生长的关键期，请注意水位管理。
</div> </div> <!-- 专家咨询 --> <div class="plan-container"> <h4 class="mb-4">专家咨询</h4> <p>遇到种植问题？咨询我们的农业专家获取专业建议。</p> <div class="d-grid gap-2"> <button class="btn btn-outline-success"><i class="fas fa-user-md me-2"></i> 在线咨询专家</button> <button class="btn btn-outline-secondary"><i class="fas fa-book me-2"></i> 查看种植知识库</button> </div> </div> </div> </div> </div> <!-- 页脚 -->
<div id="footer-container"></div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="js/login-config.js"></script>
<script src="js/components.js"></script>
<script>
// 使用 common.js 加载导航栏

// 作物选择
document.querySelectorAll('.crop-option').forEach(option => {
option.addEventListener('click', function() {
document.querySelectorAll('.crop-option').forEach(opt => {
opt.classList.remove('active');
});
this.classList.add('active');
// 这里可以根据选择的作物更新种植计划
const cropName = this.textContent.trim();
console.log(`选择的作物: ${cropName}`);
alert(`已切换到${cropName}的种植计划`);
});
});
// 任务复选框
document.querySelectorAll('.task-checkbox').forEach(checkbox => {
checkbox.addEventListener('change', function() {
const taskItem = this.closest('.task-item');
const taskStatus = taskItem.querySelector('.task-status');
if (this.checked) {
taskStatus.textContent = '已完成';
taskStatus.className = 'task-status status-completed';
} else {
taskStatus.textContent = '进行中';
taskStatus.className = 'task-status status-pending';
}
});
});
// 添加新任务
document.querySelector('.add-task-btn').addEventListener('click', function() {
// 这里应该弹出一个模态框来添加新任务
alert('添加新任务功能即将上线');
});
// 编辑任务
document.querySelectorAll('.task-edit').forEach(editBtn => {
editBtn.addEventListener('click', function() {
const taskName = this.closest('.task-item').querySelector('.task-name').textContent;
alert(`编辑任务: ${taskName}`);
});
});
// 删除任务
document.querySelectorAll('.task-delete').forEach(deleteBtn => {
deleteBtn.addEventListener('click', function() {
const taskItem = this.closest('.task-item');
const taskName = taskItem.querySelector('.task-name').textContent;
if (confirm(`确定要删除任务: ${taskName}?`)) {
taskItem.remove();
}
});
});
</script> </body> </html>
