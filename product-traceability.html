<!DOCTYPE html> <html lang="zh-CN"> <head> <meta charset="UTF-8"> <meta name="viewport" content="width=device-width, initial-scale=1.0"> <title>产品溯源 - 智慧农业平台</title> <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet"> <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet"> <link rel="stylesheet" href="css/common.css"> <link rel="stylesheet" href="css/navbar.css"> <style>
:root {
--primary-color: #28a745;
--secondary-color: #6c757d;
--light-bg: #f8f9fa;
--card-shadow: 0 4px 6px rgba(0,0,0,0.1);
--transition: all 0.3s ease;
}
body {
background-color: var(--light-bg);
font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}
.traceability-header {
background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80');
background-size: cover;
background-position: center;
height: 300px;
color: white;
display: flex;
align-items: center;
margin-bottom: 30px;
border-radius: 10px;
}
.traceability-card {
background: white;
padding: 30px;
border-radius: 10px;
box-shadow: var(--card-shadow);
margin-bottom: 30px;
}
.qr-scanner {
background: var(--light-bg);
padding: 20px;
border-radius: 10px;
text-align: center;
margin-bottom: 30px;
}
.qr-container {
width: 250px;
height: 250px;
margin: 0 auto;
border: 2px dashed #ddd;
display: flex;
align-items: center;
justify-content: center;
margin-bottom: 20px;
}
.qr-icon {
font-size: 3rem;
color: var(--secondary-color);
}
.code-input {
max-width: 300px;
margin: 0 auto;
}
.timeline {
position: relative;
padding: 20px 0;
}
.timeline::before {
content: '';
position: absolute;
top: 0;
bottom: 0;
left: 20px;
width: 2px;
background-color: var(--primary-color);
}
.timeline-item {
position: relative;
padding-left: 50px;
margin-bottom: 30px;
}
.timeline-icon {
position: absolute;
left: 0;
width: 40px;
height: 40px;
background-color: var(--primary-color);
border-radius: 50%;
display: flex;
align-items: center;
justify-content: center;
color: white;
}
.timeline-content {
background-color: var(--light-bg);
padding: 15px;
border-radius: 10px;
position: relative;
}
.timeline-content::before {
content: '';
position: absolute;
top: 15px;
left: -10px;
width: 0;
height: 0;
border-top: 10px solid transparent;
border-bottom: 10px solid transparent;
border-right: 10px solid var(--light-bg);
}
.timeline-title {
font-weight: bold;
margin-bottom: 5px;
}
.timeline-date {
color: var(--secondary-color);
font-size: 0.9rem;
margin-bottom: 10px;
}
.timeline-description {
color: #333;
}
.product-image {
width: 100%;
border-radius: 10px;
margin-bottom: 20px;
}
.product-info {
margin-bottom: 20px;
}
.product-name {
font-size: 1.5rem;
font-weight: bold;
margin-bottom: 10px;
}
.product-description {
color: var(--secondary-color);
margin-bottom: 15px;
}
.product-detail {
display: flex;
margin-bottom: 10px;
}
.product-detail-label {
min-width: 100px;
font-weight: bold;
}
.product-detail-value {
flex: 1;
}
.certificate {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 15px;
display: flex;
align-items: center;
}
.certificate-icon {
font-size: 2rem;
color: var(--primary-color);
margin-right: 15px;
}
.certificate-info {
flex: 1;
}
.certificate-name {
font-weight: bold;
margin-bottom: 5px;
}
.certificate-date {
color: var(--secondary-color);
font-size: 0.9rem;
}
.map-container {
height: 300px;
border-radius: 10px;
overflow: hidden;
margin-bottom: 20px;
}
#map {
height: 300px;
width: 100%;
}
.farm-info {
margin-bottom: 20px;
}
.farm-name {
font-size: 1.2rem;
font-weight: bold;
margin-bottom: 10px;
}
.farm-description {
color: var(--secondary-color);
margin-bottom: 15px;
}
.farm-detail {
display: flex;
margin-bottom: 10px;
}
.farm-detail-label {
min-width: 100px;
font-weight: bold;
}
.farm-detail-value {
flex: 1;
}
.farmer-card {
display: flex;
align-items: center;
margin-bottom: 20px;
}
.farmer-avatar {
width: 80px;
height: 80px;
border-radius: 50%;
object-fit: cover;
margin-right: 15px;
}
.farmer-info {
flex: 1;
}
.farmer-name {
font-weight: bold;
margin-bottom: 5px;
}
.farmer-role {
color: var(--secondary-color);
margin-bottom: 10px;
}
.farmer-description {
color: #333;
font-size: 0.9rem;
}
.blockchain-info {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 15px;
}
.blockchain-title {
font-weight: bold;
margin-bottom: 10px;
display: flex;
align-items: center;
}
.blockchain-icon {
color: var(--primary-color);
margin-right: 10px;
}
.blockchain-data {
font-family: monospace;
word-break: break-all;
background: #333;
color: #fff;
padding: 10px;
border-radius: 5px;
font-size: 0.8rem;
}
.step-indicator {
display: flex;
justify-content: space-between;
margin-bottom: 30px;
}
.step {
flex: 1;
text-align: center;
padding: 15px;
position: relative;
}
.step::after {
content: '';
position: absolute;
top: 50%;
right: 0;
width: 100%;
height: 2px;
background-color: #ddd;
transform: translateY(-50%);
z-index: -1;
}
.step:last-child::after {
display: none;
}
.step-icon {
width: 50px;
height: 50px;
background-color: #ddd;
border-radius: 50%;
display: flex;
align-items: center;
justify-content: center;
margin: 0 auto 10px;
color: white;
position: relative;
z-index: 1;
}
.step.active .step-icon {
background-color: var(--primary-color);
}
.step.completed .step-icon {
background-color: var(--primary-color);
}
.step-title {
font-size: 0.9rem;
color: var(--secondary-color);
}
.step.active .step-title {
color: var(--primary-color);
font-weight: bold;
}
</style> </head> <body> <!-- 导航栏 -->
<div id="navbar-container"></div>
<!-- 主要内容 --> <div class="container py-5"> <div class="traceability-header"> <div class="container"> <h1>产品溯源系统</h1> <p class="lead">扫码查看从种植到销售的全过程，保障食品安全与品质</p> </div> </div> <!-- 扫码查询 --> <div class="traceability-card"> <h3 class="mb-4 text-center">产品溯源查询</h3> <div class="row"> <div class="col-md-6"> <div class="qr-scanner"> <h5 class="mb-3">扫描产品二维码</h5> <div class="qr-container"> <i class="fas fa-qrcode qr-icon"></i> </div> <button class="btn btn-success"><i class="fas fa-camera me-2"></i> 打开相机扫码</button> </div> </div> <div class="col-md-6"> <div class="qr-scanner"> <h5 class="mb-3">输入溯源码</h5> <p class="text-muted">请输入产品包装上的16位溯源码</p> <div class="code-input"> <div class="input-group mb-3"> <input type="text" class="form-control" placeholder="例如：SN12345678901234"> <button class="btn btn-success" type="button">查询</button> </div> </div> </div> </div> </div> </div> <!-- 溯源结果 --> <div class="traceability-card"> <div class="d-flex justify-content-between align-items-center mb-4"> <h3>溯源结果</h3> <div> <button class="btn btn-outline-success me-2"><i class="fas fa-share-alt me-1"></i> 分享</button> <button class="btn btn-success"><i class="fas fa-download me-1"></i> 下载证书</button> </div> </div> <!-- 溯源步骤 --> <div class="step-indicator mb-5"> <div class="step completed"> <div class="step-icon"> <i class="fas fa-seedling"></i> </div> <div class="step-title">种植</div> </div> <div class="step completed"> <div class="step-icon"> <i class="fas fa-leaf"></i> </div> <div class="step-title">生长</div> </div> <div class="step completed"> <div class="step-icon"> <i class="fas fa-clipboard-check"></i> </div> <div class="step-title">检测</div> </div> <div class="step completed"> <div class="step-icon"> <i class="fas fa-box"></i> </div> <div class="step-title">包装</div> </div> <div class="step completed"> <div class="step-icon"> <i class="fas fa-truck"></i> </div> <div class="step-title">物流</div> </div> <div class="step active"> <div class="step-icon"> <i class="fas fa-store"></i> </div> <div class="step-title">销售</div> </div> </div> <div class="row"> <div class="col-md-6"> <!-- 产品信息 --> <img src="images/product1.jpg" alt="产品图片" class="product-image"> <div class="product-info"> <div class="product-name">有机稻花香大米</div> <div class="product-description">来自浙江嘉兴的优质有机大米，口感香糯，营养丰富</div> <div class="product-detail"> <div class="product-detail-label">产品编号：</div> <div class="product-detail-value">SN12345678901234</div> </div> <div class="product-detail"> <div class="product-detail-label">生产日期：</div> <div class="product-detail-value">2023-09-15</div> </div> <div class="product-detail"> <div class="product-detail-label">保质期：</div> <div class="product-detail-value">12个月</div> </div> <div class="product-detail"> <div class="product-detail-label">净含量：</div> <div class="product-detail-value">5kg</div> </div> <div class="product-detail"> <div class="product-detail-label">品种：</div> <div class="product-detail-value">稻花香2号</div> </div> </div> <!-- 认证信息 --> <h5 class="mb-3">认证信息</h5> <div class="certificate"> <div class="certificate-icon"> <i class="fas fa-award"></i> </div> <div class="certificate-info"> <div class="certificate-name">有机食品认证</div> <div class="certificate-date">认证机构：中国有机食品认证中心</div> <div class="certificate-date">认证日期：2023-01-10</div> <div class="certificate-date">有效期至：2026-01-09</div> </div> </div> <div class="certificate"> <div class="certificate-icon"> <i class="fas fa-medal"></i> </div> <div class="certificate-info"> <div class="certificate-name">绿色食品认证</div> <div class="certificate-date">认证机构：中国绿色食品发展中心</div> <div class="certificate-date">认证日期：2022-12-05</div> <div class="certificate-date">有效期至：2025-12-04</div> </div> </div> <!-- 区块链存证 --> <h5 class="mb-3 mt-4">区块链存证</h5> <div class="blockchain-info"> <div class="blockchain-title"> <i class="fas fa-link blockchain-icon"></i> <span>交易哈希</span> </div> <div class="blockchain-data">
0x7f9e8d7c6b5a4e3d2c1b0a9f8e7d6c5b4a3f2e1d0c9b8a7f6e5d4c3b2a1f0e9d8
</div> </div> <div class="blockchain-info"> <div class="blockchain-title"> <i class="fas fa-clock blockchain-icon"></i> <span>时间戳</span> </div> <div class="blockchain-data">
2023-09-15 14:30:25 GMT+8
</div> </div> <div class="blockchain-info"> <div class="blockchain-title"> <i class="fas fa-fingerprint blockchain-icon"></i> <span>数据指纹</span> </div> <div class="blockchain-data">
SHA256: e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855
</div> </div> </div> <div class="col-md-6"> <!-- 产地信息 --> <h5 class="mb-3">产地信息</h5> <div class="map-container"> <div id="map"></div> </div> <div class="farm-info"> <div class="farm-name">浙江嘉兴水稻基地</div> <div class="farm-description">位于浙江省嘉兴市南湖区，拥有优质水稻种植环境，采用有机种植方式</div> <div class="farm-detail"> <div class="farm-detail-label">地址：</div> <div class="farm-detail-value">浙江省嘉兴市南湖区XXX镇</div> </div> <div class="farm-detail"> <div class="farm-detail-label">面积：</div> <div class="farm-detail-value">300亩</div> </div> <div class="farm-detail"> <div class="farm-detail-label">土壤类型：</div> <div class="farm-detail-value">壤土</div> </div> <div class="farm-detail"> <div class="farm-detail-label">灌溉水源：</div> <div class="farm-detail-value">地下水</div> </div> </div> <!-- 种植者信息 --> <h5 class="mb-3">种植者信息</h5> <div class="farmer-card"> <img src="images/farmer.jpg" alt="农民" class="farmer-avatar"> <div class="farmer-info"> <div class="farmer-name">李大伟</div> <div class="farmer-role">资深水稻种植专家</div> <div class="farmer-description">
从事水稻种植20余年，精通有机水稻种植技术，多次获得优质稻米评比奖项。
</div> </div> </div> <!-- 溯源时间线 --> <h5 class="mb-3">溯源时间线</h5> <div class="timeline"> <div class="timeline-item"> <div class="timeline-icon"> <i class="fas fa-seedling"></i> </div> <div class="timeline-content"> <div class="timeline-title">播种</div> <div class="timeline-date">2023-03-15</div> <div class="timeline-description">
选用优质稻花香2号种子，进行浸种、催芽处理后播种。
</div> </div> </div> <div class="timeline-item"> <div class="timeline-icon"> <i class="fas fa-leaf"></i> </div> <div class="timeline-content"> <div class="timeline-title">生长管理</div> <div class="timeline-date">2023-04-20 至 2023-08-25</div> <div class="timeline-description">
采用有机种植方式，使用有机肥料，定期进行水位管理和病虫害防治。
</div> </div> </div> <div class="timeline-item"> <div class="timeline-icon"> <i class="fas fa-cut"></i> </div> <div class="timeline-content"> <div class="timeline-title">收割</div> <div class="timeline-date">2023-09-05</div> <div class="timeline-description">
水稻成熟后，使用收割机进行收割，确保适时收获。
</div> </div> </div> <div class="timeline-item"> <div class="timeline-icon"> <i class="fas fa-sun"></i> </div> <div class="timeline-content"> <div class="timeline-title">晾晒与加工</div> <div class="timeline-date">2023-09-06 至 2023-09-10</div> <div class="timeline-description">
收获后的稻谷进行自然晾晒，然后进行脱壳、碾米等加工处理。
</div> </div> </div> <div class="timeline-item"> <div class="timeline-icon"> <i class="fas fa-flask"></i> </div> <div class="timeline-content"> <div class="timeline-title">质量检测</div> <div class="timeline-date">2023-09-12</div> <div class="timeline-description">
对大米进行农药残留、重金属含量、营养成分等多项检测，确保符合有机食品标准。
</div> </div> </div> <div class="timeline-item"> <div class="timeline-icon"> <i class="fas fa-box"></i> </div> <div class="timeline-content"> <div class="timeline-title">包装</div> <div class="timeline-date">2023-09-15</div> <div class="timeline-description">
使用环保包装材料进行真空包装，确保大米新鲜度和品质。
</div> </div> </div> <div class="timeline-item"> <div class="timeline-icon"> <i class="fas fa-truck"></i> </div> <div class="timeline-content"> <div class="timeline-title">物流配送</div> <div class="timeline-date">2023-09-16 至 2023-09-18</div> <div class="timeline-description">
通过冷链物流系统配送至各销售点，全程温湿度监控。
</div> </div> </div> <div class="timeline-item"> <div class="timeline-icon"> <i class="fas fa-store"></i> </div> <div class="timeline-content"> <div class="timeline-title">销售</div> <div class="timeline-date">2023-09-20</div> <div class="timeline-description">
产品到达销售点，上架销售。
</div> </div> </div> </div> </div> </div> </div> </div> <!-- 页脚 -->
<div id="footer-container"></div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="js/components.js"></script>

 <script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script> <script>
// 初始化地图
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(function() {
        var map = L.map('map').setView([30.7741, 120.7551], 13);
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors'
        }).addTo(map);

        // 添加标记
        L.marker([30.7741, 120.7551])
            .bindPopup('浙江嘉兴水稻基地')
            .addTo(map);
    }, 500); // 添加延时，确保地图容器已经渲染
});
</script> </body> </html>
