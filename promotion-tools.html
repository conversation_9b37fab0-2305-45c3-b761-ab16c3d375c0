<!DOCTYPE html> <html lang="zh-CN"> <head> <meta charset="UTF-8"> <meta name="viewport" content="width=device-width, initial-scale=1.0"> <title>分销推广工具 - 智慧农业平台</title> <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet"> <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet"> <link rel="stylesheet" href="css/common.css"> <link rel="stylesheet" href="css/navbar.css"> <style>
:root {
--primary-color: #28a745;
--secondary-color: #6c757d;
--light-bg: #f8f9fa;
--card-shadow: 0 4px 6px rgba(0,0,0,0.1);
--transition: all 0.3s ease;
--distribution-color: #8e44ad;
--level1-color: #3498db;
--level2-color: #e67e22;
}
body {
background-color: var(--light-bg);
font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}
.promotion-header {
background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80');
background-size: cover;
background-position: center;
height: 200px;
color: white;
display: flex;
align-items: center;
margin-bottom: 30px;
border-radius: 10px;
}
.promotion-card {
background: white;
padding: 20px;
border-radius: 10px;
box-shadow: var(--card-shadow);
margin-bottom: 30px;
min-height: auto;
}
.stat-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 20px;
transition: var(--transition);
}
.stat-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.stat-icon {
font-size: 2rem;
color: var(--distribution-color);
margin-bottom: 10px;
}
.stat-value {
font-size: 1.5rem;
font-weight: bold;
color: var(--distribution-color);
margin-bottom: 5px;
}
.stat-label {
font-size: 0.9rem;
color: var(--secondary-color);
}
.tool-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 15px;
transition: var(--transition);
display: flex;
flex-direction: column;
min-height: auto;
}
.tool-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.tool-header {
display: flex;
align-items: center;
margin-bottom: 15px;
}
.tool-icon {
width: 50px;
height: 50px;
border-radius: 10px;
background-color: var(--distribution-color);
color: white;
display: flex;
align-items: center;
justify-content: center;
font-size: 1.5rem;
margin-right: 15px;
flex-shrink: 0;
}
.tool-title {
font-weight: bold;
font-size: 1.1rem;
margin-bottom: 5px;
}
.tool-subtitle {
font-size: 0.9rem;
color: var(--secondary-color);
}
.tool-body {
flex: 1;
margin-bottom: 15px;
}
.tool-description {
color: var(--secondary-color);
margin-bottom: 15px;
}
.tool-footer {
margin-top: auto;
}
.poster-preview {
width: 100%;
border-radius: 10px;
margin-bottom: 15px;
box-shadow: var(--card-shadow);
}
.poster-options {
display: flex;
flex-wrap: wrap;
gap: 10px;
margin-bottom: 15px;
}
.poster-option {
width: 60px;
height: 60px;
border-radius: 5px;
overflow: hidden;
cursor: pointer;
border: 2px solid transparent;
transition: var(--transition);
}
.poster-option.active {
border-color: var(--distribution-color);
}
.poster-option img {
width: 100%;
min-height: auto;
object-fit: cover;
}
.link-container {
background-color: white;
border: 1px solid #ddd;
border-radius: 5px;
padding: 10px;
margin-bottom: 15px;
position: relative;
}
.link-text {
font-family: monospace;
font-size: 0.9rem;
word-break: break-all;
padding-right: 30px;
}
.copy-btn {
position: absolute;
top: 10px;
right: 10px;
background: none;
border: none;
color: var(--distribution-color);
cursor: pointer;
}
.qr-container {
display: flex;
flex-direction: column;
align-items: center;
margin-bottom: 15px;
}
.qr-code {
width: 150px;
height: 150px;
background-color: white;
border: 1px solid #ddd;
border-radius: 5px;
padding: 10px;
margin-bottom: 10px;
}
.product-card {
background: white;
border: 1px solid #ddd;
border-radius: 10px;
overflow: hidden;
margin-bottom: 15px;
transition: var(--transition);
}
.product-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.product-image {
width: 100%;
height: 150px;
object-fit: cover;
}
.product-info {
padding: 15px;
}
.product-name {
font-weight: bold;
margin-bottom: 5px;
}
.product-price {
display: flex;
align-items: center;
margin-bottom: 10px;
}
.product-original {
text-decoration: line-through;
color: var(--secondary-color);
margin-right: 10px;
font-size: 0.9rem;
}
.product-current {
color: #e74c3c;
font-weight: bold;
}
.product-commission {
font-size: 0.9rem;
color: var(--distribution-color);
margin-bottom: 10px;
}
.product-actions {
display: flex;
gap: 10px;
}
.material-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 15px;
transition: var(--transition);
}
.material-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.material-header {
display: flex;
justify-content: space-between;
align-items: center;
margin-bottom: 15px;
}
.material-title {
font-weight: bold;
display: flex;
align-items: center;
}
.material-icon {
color: var(--distribution-color);
margin-right: 10px;
}
.material-body {
margin-bottom: 15px;
}
.material-item {
display: flex;
margin-bottom: 10px;
background-color: white;
border-radius: 5px;
padding: 10px;
transition: var(--transition);
}
.material-item:hover {
transform: translateX(5px);
box-shadow: var(--card-shadow);
}
.material-image {
width: 60px;
height: 60px;
border-radius: 5px;
object-fit: cover;
margin-right: 15px;
}
.material-info {
flex: 1;
}
.material-name {
font-weight: bold;
margin-bottom: 5px;
}
.material-desc {
font-size: 0.9rem;
color: var(--secondary-color);
}
.material-actions {
display: flex;
gap: 10px;
align-items: center;
}
.faq-item {
margin-bottom: 15px;
}
.faq-question {
font-weight: bold;
margin-bottom: 5px;
cursor: pointer;
display: flex;
justify-content: space-between;
align-items: center;
}
.faq-answer {
color: var(--secondary-color);
display: none;
padding: 10px 0;
}
.faq-answer.active {
display: block;
}
.tab-content {
padding: 20px 0;
}
.nav-tabs .nav-link {
color: var(--secondary-color);
}
.nav-tabs .nav-link.active {
color: var(--distribution-color);
font-weight: bold;
}
</style> </head> <body> <!-- 导航栏 -->
<div id="navbar-container"></div>
<!-- 主要内容 --> <div class="container py-5"> <div class="promotion-header"> <div class="container"> <h1>分销推广工具</h1> <p class="lead">使用专业推广工具，提高分销效率，包括海报生成器、专属推广链接、产品素材库等多种营销工具</p> </div> </div> <!-- 顶部统计卡片 --> <div class="row mb-4"> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-share-alt stat-icon"></i> <div class="stat-value">1,256</div> <div class="stat-label">推广链接点击数</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-image stat-icon"></i> <div class="stat-value">568</div> <div class="stat-label">海报生成数</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-shopping-cart stat-icon"></i> <div class="stat-value">328</div> <div class="stat-label">推广转化订单</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-chart-line stat-icon"></i> <div class="stat-value">26.1%</div> <div class="stat-label">平均转化率</div> </div> </div> </div> <!-- 主要内容区域 --> <div class="row"> <!-- 左侧内容 --> <div class="col-md-8"> <!-- 推广工具卡片 --> <div class="promotion-card"> <h4 class="mb-4">推广工具</h4> <div class="row"> <div class="col-md-6 mb-4"> <div class="tool-card"> <div class="tool-header"> <div class="tool-icon"> <i class="fas fa-image"></i> </div> <div> <div class="tool-title">海报生成器</div> <div class="tool-subtitle">一键生成专属分销海报</div> </div> </div> <div class="tool-body"> <div class="tool-description">
根据产品信息和个人分销码，自动生成高转化率的分销海报，支持自定义模板和内容。
</div> </div> <div class="tool-footer"> <button class="btn btn-success w-100">使用工具</button> </div> </div> </div> <div class="col-md-6 mb-4"> <div class="tool-card"> <div class="tool-header"> <div class="tool-icon"> <i class="fas fa-link"></i> </div> <div> <div class="tool-title">推广链接生成器</div> <div class="tool-subtitle">生成专属跟踪链接</div> </div> </div> <div class="tool-body"> <div class="tool-description">
生成带有个人分销码的产品推广链接，支持数据统计和转化跟踪，帮助您分析推广效果。
</div> </div> <div class="tool-footer"> <button class="btn btn-success w-100">使用工具</button> </div> </div> </div> <div class="col-md-6 mb-4"> <div class="tool-card"> <div class="tool-header"> <div class="tool-icon"> <i class="fas fa-qrcode"></i> </div> <div> <div class="tool-title">二维码生成器</div> <div class="tool-subtitle">生成专属分销二维码</div> </div> </div> <div class="tool-body"> <div class="tool-description">
将您的分销链接转换为二维码，方便线下推广和分享，支持自定义样式和商标。
</div> </div> <div class="tool-footer"> <button class="btn btn-success w-100">使用工具</button> </div> </div> </div> <div class="col-md-6 mb-4"> <div class="tool-card"> <div class="tool-header"> <div class="tool-icon"> <i class="fas fa-comment-dots"></i> </div> <div> <div class="tool-title">营销话术生成器</div> <div class="tool-subtitle">智能生成销售话术</div> </div> </div> <div class="tool-body"> <div class="tool-description">
基于AI技术，根据产品特点自动生成高转化率的营销话术，帮助您更有效地介绍产品。
</div> </div> <div class="tool-footer"> <button class="btn btn-success w-100">使用工具</button> </div> </div> </div> </div> </div> <!-- 海报生成器 --> <div class="promotion-card"> <h4 class="mb-4">海报生成器</h4> <ul class="nav nav-tabs" id="posterTabs" role="tablist"> <li class="nav-item" role="presentation"> <button class="nav-link active" id="step1-tab" data-bs-toggle="tab" data-bs-target="#step1" type="button" role="tab" aria-controls="step1" aria-selected="true">选择产品</button> </li> <li class="nav-item" role="presentation"> <button class="nav-link" id="step2-tab" data-bs-toggle="tab" data-bs-target="#step2" type="button" role="tab" aria-controls="step2" aria-selected="false">选择模板</button> </li> <li class="nav-item" role="presentation"> <button class="nav-link" id="step3-tab" data-bs-toggle="tab" data-bs-target="#step3" type="button" role="tab" aria-controls="step3" aria-selected="false">自定义设置</button> </li> <li class="nav-item" role="presentation"> <button class="nav-link" id="step4-tab" data-bs-toggle="tab" data-bs-target="#step4" type="button" role="tab" aria-controls="step4" aria-selected="false">生成海报</button> </li> </ul> <div class="tab-content" id="posterTabsContent"> <!-- 步骤1：选择产品 --> <div class="tab-pane fade show active" id="step1" role="tabpanel" aria-labelledby="step1-tab"> <div class="row"> <div class="col-md-4 mb-3"> <div class="product-card"> <img src="images/product1.jpg" alt="产品图片" class="product-image"> <div class="product-info"> <div class="product-name">有机稻花香大米</div> <div class="product-price"> <div class="product-original">￥128.00</div> <div class="product-current">￥108.00</div> </div> <div class="product-commission">佣金：￥16.20 (15%)</div> <div class="product-actions"> <button class="btn btn-sm btn-success w-100">选择此产品</button> </div> </div> </div> </div> <div class="col-md-4 mb-3"> <div class="product-card"> <img src="images/product2.jpg" alt="产品图片" class="product-image"> <div class="product-info"> <div class="product-name">有机豆浆</div> <div class="product-price"> <div class="product-original">￥98.00</div> <div class="product-current">￥85.00</div> </div> <div class="product-commission">佣金：￥12.75 (15%)</div> <div class="product-actions"> <button class="btn btn-sm btn-outline-success w-100">选择此产品</button> </div> </div> </div> </div> <div class="col-md-4 mb-3"> <div class="product-card"> <img src="images/product3.jpg" alt="产品图片" class="product-image"> <div class="product-info"> <div class="product-name">有机黄瓜</div> <div class="product-price"> <div class="product-original">￥68.00</div> <div class="product-current">￥58.00</div> </div> <div class="product-commission">佣金：￥8.70 (15%)</div> <div class="product-actions"> <button class="btn btn-sm btn-outline-success w-100">选择此产品</button> </div> </div> </div> </div> </div> <div class="d-flex justify-content-between mt-3"> <button class="btn btn-outline-secondary" disabled>上一步</button> <button class="btn btn-success" onclick="document.getElementById('step2-tab').click()">下一步</button> </div> </div> <!-- 步骤2：选择模板 --> <div class="tab-pane fade" id="step2" role="tabpanel" aria-labelledby="step2-tab"> <div class="row"> <div class="col-md-6 mb-4"> <div class="poster-options"> <div class="poster-option active"> <img src="images/poster-template1.jpg" alt="海报模板1"> </div> <div class="poster-option"> <img src="images/poster-template2.jpg" alt="海报模板2"> </div> <div class="poster-option"> <img src="images/poster-template3.jpg" alt="海报模板3"> </div> <div class="poster-option"> <img src="images/poster-template4.jpg" alt="海报模板4"> </div> </div> <img src="images/poster-preview1.jpg" alt="海报预览" class="poster-preview"> </div> <div class="col-md-6"> <div class="mb-3"> <label class="form-label">模板名称</label> <input type="text" class="form-control" value="精美风格模板" readonly> </div> <div class="mb-3"> <label class="form-label">模板特点</label> <ul> <li>简洁现代风格，突出产品特点</li> <li>适合食品类产品推广</li> <li>支持自定义文案和二维码</li> <li>高转化率设计，提升点击率</li> </ul> </div> <div class="mb-3"> <label class="form-label">适用场景</label> <div class="form-text">微信朋友圈、微信群、微博、小红书等社交平台</div> </div> </div> </div> <div class="d-flex justify-content-between mt-3"> <button class="btn btn-outline-secondary" onclick="document.getElementById('step1-tab').click()">上一步</button> <button class="btn btn-success" onclick="document.getElementById('step3-tab').click()">下一步</button> </div> </div> <!-- 步骤3：自定义设置 --> <div class="tab-pane fade" id="step3" role="tabpanel" aria-labelledby="step3-tab"> <div class="row"> <div class="col-md-6"> <div class="mb-3"> <label class="form-label">海报标题</label> <input type="text" class="form-control" value="精选有机稻花香大米，香糊可口"> </div> <div class="mb-3"> <label class="form-label">产品描述</label> <textarea class="form-control" rows="3">精选优质稻花香大米，采用有机种植技术，无添加剂，纯天然绿色食品，香糊可口，营养丰富。</textarea> </div> <div class="mb-3"> <label class="form-label">促销信息</label> <input type="text" class="form-control" value="限时特惠，原价￥128，现价仅需￥108"> </div> <div class="mb-3"> <label class="form-label">底部文案</label> <input type="text" class="form-control" value="扫码购买，享受健康生活"> </div> </div> <div class="col-md-6"> <div class="mb-3"> <label class="form-label">显示二维码</label> <div class="form-check form-switch"> <input class="form-check-input" type="checkbox" id="showQrCode" checked> <label class="form-check-label" for="showQrCode">在海报上显示二维码</label> </div> </div> <div class="mb-3"> <label class="form-label">显示价格</label> <div class="form-check form-switch"> <input class="form-check-input" type="checkbox" id="showPrice" checked> <label class="form-check-label" for="showPrice">在海报上显示价格</label> </div> </div> <div class="mb-3"> <label class="form-label">显示佣金</label> <div class="form-check form-switch"> <input class="form-check-input" type="checkbox" id="showCommission" checked> <label class="form-check-label" for="showCommission">在海报上显示佣金（仅自己可见）</label> </div> </div> <div class="mb-3"> <label class="form-label">添加水印</label> <div class="form-check form-switch"> <input class="form-check-input" type="checkbox" id="showWatermark"> <label class="form-check-label" for="showWatermark">在海报上添加个人水印</label> </div> </div> </div> </div> <div class="d-flex justify-content-between mt-3"> <button class="btn btn-outline-secondary" onclick="document.getElementById('step2-tab').click()">上一步</button> <button class="btn btn-success" onclick="document.getElementById('step4-tab').click()">下一步</button> </div> </div> <!-- 步骤4：生成海报 --> <div class="tab-pane fade" id="step4" role="tabpanel" aria-labelledby="step4-tab"> <div class="row"> <div class="col-md-6 text-center"> <img src="images/poster-final.jpg" alt="最终海报" class="poster-preview"> <div class="mt-3"> <button class="btn btn-success me-2"><i class="fas fa-download me-1"></i>下载海报</button> <button class="btn btn-primary"><i class="fas fa-share-alt me-1"></i>分享海报</button> </div> </div> <div class="col-md-6"> <div class="alert alert-success"> <i class="fas fa-check-circle me-2"></i>
海报生成成功！您可以下载或直接分享到社交平台。
</div> <div class="qr-container"> <img src="images/qr-code.png" alt="二维码" class="qr-code"> <button class="btn btn-sm btn-outline-success"><i class="fas fa-download me-1"></i>下载二维码</button> </div> <div class="link-container"> <div class="link-text">https://smart-farm.com/p/rice?ref=DP38629</div> <button class="copy-btn" title="复制链接"><i class="fas fa-copy"></i></button> </div> <div class="mt-3"> <label class="form-label">分享到：</label> <div class="d-flex gap-2"> <button class="btn btn-outline-success"><i class="fab fa-weixin me-1"></i>微信</button> <button class="btn btn-outline-success"><i class="fab fa-weibo me-1"></i>微博</button> <button class="btn btn-outline-success"><i class="fas fa-comment me-1"></i>短信</button> <button class="btn btn-outline-success"><i class="fas fa-ellipsis-h me-1"></i>更多</button> </div> </div> </div> </div> <div class="d-flex justify-content-between mt-3"> <button class="btn btn-outline-secondary" onclick="document.getElementById('step3-tab').click()">上一步</button> <button class="btn btn-success" onclick="document.getElementById('step1-tab').click()">创建新海报</button> </div> </div> </div> </div> </div> <!-- 右侧内容 --> <div class="col-md-4"> <!-- 推广素材库 --> <div class="promotion-card"> <h4 class="mb-4">推广素材库</h4> <div class="material-card"> <div class="material-header"> <div class="material-title"> <i class="fas fa-images material-icon"></i> <span>产品图片</span> </div> </div> <div class="material-body"> <div class="material-item"> <img src="images/material1.jpg" alt="产品图片" class="material-image"> <div class="material-info"> <div class="material-name">有机稻花香大米图片集</div> <div class="material-desc">12张高清产品图片，包含产品展示、包装等</div> </div> <div class="material-actions"> <button class="btn btn-sm btn-outline-success"><i class="fas fa-download"></i></button> </div> </div> <div class="material-item"> <img src="images/material2.jpg" alt="产品图片" class="material-image"> <div class="material-info"> <div class="material-name">有机蔬菜系列图片</div> <div class="material-desc">8张高清产品图片，包含多种有机蔬菜</div> </div> <div class="material-actions"> <button class="btn btn-sm btn-outline-success"><i class="fas fa-download"></i></button> </div> </div> <div class="material-item"> <img src="images/material3.jpg" alt="产品图片" class="material-image"> <div class="material-info"> <div class="material-name">农场实景图片集</div> <div class="material-desc">15张高清农场实景图片，展示种植环境</div> </div> <div class="material-actions"> <button class="btn btn-sm btn-outline-success"><i class="fas fa-download"></i></button> </div> </div> </div> </div> <div class="material-card"> <div class="material-header"> <div class="material-title"> <i class="fas fa-file-alt material-icon"></i> <span>产品文案</span> </div> </div> <div class="material-body"> <div class="material-item"> <div class="material-info"> <div class="material-name">有机稻花香大米产品文案</div> <div class="material-desc">包含产品介绍、特点、适用人群等内容</div> </div> <div class="material-actions"> <button class="btn btn-sm btn-outline-success"><i class="fas fa-copy"></i></button> </div> </div> <div class="material-item"> <div class="material-info"> <div class="material-name">有机食品系列推广文案</div> <div class="material-desc">适用于各类有机食品的推广文案模板</div> </div> <div class="material-actions"> <button class="btn btn-sm btn-outline-success"><i class="fas fa-copy"></i></button> </div> </div> <div class="material-item"> <div class="material-info"> <div class="material-name">节日促销文案合集</div> <div class="material-desc">适用于各个节日的促销活动文案</div> </div> <div class="material-actions"> <button class="btn btn-sm btn-outline-success"><i class="fas fa-copy"></i></button> </div> </div> </div> </div> <div class="material-card"> <div class="material-header"> <div class="material-title"> <i class="fas fa-video material-icon"></i> <span>视频素材</span> </div> </div> <div class="material-body"> <div class="material-item"> <img src="images/video1.jpg" alt="视频素材" class="material-image"> <div class="material-info"> <div class="material-name">有机稻花香大米种植过程</div> <div class="material-desc">3分钟视频，展示从种植到收获的全过程</div> </div> <div class="material-actions"> <button class="btn btn-sm btn-outline-success"><i class="fas fa-download"></i></button> </div> </div> <div class="material-item"> <img src="images/video2.jpg" alt="视频素材" class="material-image"> <div class="material-info"> <div class="material-name">产品使用教程</div> <div class="material-desc">2分钟视频，展示产品的正确使用方法</div> </div> <div class="material-actions"> <button class="btn btn-sm btn-outline-success"><i class="fas fa-download"></i></button> </div> </div> </div> </div> </div> <!-- 推广数据统计 --> <div class="promotion-card"> <h4 class="mb-4">推广数据统计</h4> <div class="alert alert-primary"> <i class="fas fa-info-circle me-2"></i>
以下是您近7天的推广数据统计，可以帮助您分析推广效果。
</div> <div class="table-responsive"> <table class="table table-bordered table-hover"> <thead class="table-light"> <tr> <th>日期</th> <th>点击数</th> <th>访问量</th> <th>转化数</th> <th>转化率</th> </tr> </thead> <tbody> <tr> <td>06-20</td> <td>256</td> <td>189</td> <td>45</td> <td>23.8%</td> </tr> <tr> <td>06-19</td> <td>198</td> <td>145</td> <td>38</td> <td>26.2%</td> </tr> <tr> <td>06-18</td> <td>210</td> <td>156</td> <td>42</td> <td>26.9%</td> </tr> <tr> <td>06-17</td> <td>185</td> <td>132</td> <td>35</td> <td>26.5%</td> </tr> <tr> <td>06-16</td> <td>168</td> <td>120</td> <td>32</td> <td>26.7%</td> </tr> </tbody> </table> </div> <div class="text-center mt-3"> <button class="btn btn-outline-success"><i class="fas fa-download me-1"></i>导出数据</button> </div> </div> <!-- 常见问题 --> <div class="promotion-card"> <h4 class="mb-4">常见问题</h4> <div class="faq-item"> <div class="faq-question" onclick="toggleFaq(this)"> <span>如何提高推广转化率？</span> <i class="fas fa-chevron-down"></i> </div> <div class="faq-answer">
提高推广转化率的关键是选择适合的产品、定位精准的目标群体和使用有吸引力的推广文案。建议您使用海报生成器创建高质量的推广图片，并结合营销话术生成器编写有说服力的文案。同时，定期分析推广数据，调整推广策略。
</div> </div> <div class="faq-item"> <div class="faq-question" onclick="toggleFaq(this)"> <span>如何使用海报生成器？</span> <i class="fas fa-chevron-down"></i> </div> <div class="faq-answer">
使用海报生成器非常简单，只需四步：1) 选择要推广的产品；2) 选择海报模板；3) 自定义海报文案和设置；4) 生成并下载或分享海报。海报上会自动包含您的分销二维码，方便客户扫码购买。
</div> </div> <div class="faq-item"> <div class="faq-question" onclick="toggleFaq(this)"> <span>推广链接如何跟踪效果？</span> <i class="fas fa-chevron-down"></i> </div> <div class="faq-answer">
每个推广链接都包含您的唯一分销码，系统会自动记录链接的点击数、访问量、转化数等数据。您可以在“推广数据统计”模块中查看这7天的推广效果，并可以导出详细数据进行分析。
</div> </div> <div class="faq-item"> <div class="faq-question" onclick="toggleFaq(this)"> <span>推广素材如何下载和使用？</span> <i class="fas fa-chevron-down"></i> </div> <div class="faq-answer">
在“推广素材库”中，您可以浏览各类产品的图片、文案和视频素材。点击素材右侧的下载按钮即可下载到本地。对于文案素材，点击复制按钮可直接复制到剪贴板。这些素材可用于您的社交媒体、聊天工具等渠道的推广活动。
</div> </div> </div> </div> </div> </div> <!-- 页脚 -->
<div id="footer-container"></div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="js/login-config.js"></script>
<script src="js/components.js"></script>
<script>
// 使用 common.js 加载导航栏

// 切换FAQ显示/隐藏
function toggleFaq(element) {
const answer = element.nextElementSibling;
const icon = element.querySelector('i');
if (answer.classList.contains('active')) {
answer.classList.remove('active');
icon.classList.remove('fa-chevron-up');
icon.classList.add('fa-chevron-down');
} else {
answer.classList.add('active');
icon.classList.remove('fa-chevron-down');
icon.classList.add('fa-chevron-up');
}
}
// 海报模板选择
document.querySelectorAll('.poster-option').forEach(option => {
option.addEventListener('click', function() {
document.querySelectorAll('.poster-option').forEach(opt => {
opt.classList.remove('active');
});
this.classList.add('active');
// 在实际应用中，这里应该更新海报预览图片
const posterPreview = document.querySelector('.poster-preview');
const templateSrc = this.querySelector('img').src;
const previewNumber = templateSrc.match(/template(\d+)/)[1];
posterPreview.src = `images/poster-preview${previewNumber}.jpg`;
});
});
// 产品选择按钮点击事件
document.querySelectorAll('.product-actions .btn').forEach(btn => {
btn.addEventListener('click', function() {
document.querySelectorAll('.product-actions .btn').forEach(b => {
b.classList.remove('btn-success');
b.classList.add('btn-outline-success');
});
this.classList.remove('btn-outline-success');
this.classList.add('btn-success');
});
});
// 复制链接按钮点击事件
document.querySelector('.copy-btn').addEventListener('click', function() {
const linkText = this.previousElementSibling.textContent;
navigator.clipboard.writeText(linkText).then(() => {
alert('链接已复制到剪贴板！');
});
});
// 下载海报按钮点击事件
document.querySelector('.btn-success[title="下载海报"]').addEventListener('click', function() {
alert('正在下载海报，请稍后...');
});
// 分享海报按钮点击事件
document.querySelector('.btn-primary').addEventListener('click', function() {
alert('请选择分享方式');
});
// 素材库下载按钮点击事件
document.querySelectorAll('.material-actions .btn').forEach(btn => {
btn.addEventListener('click', function() {
const materialName = this.closest('.material-item').querySelector('.material-name').textContent;
alert(`正在下载 ${materialName}，请稍后...`);
});
});
// 导出数据按钮点击事件
document.querySelector('.btn-outline-success[title="导出数据"]').addEventListener('click', function() {
alert('正在导出数据，请稍后...');
});
// 使用工具按钮点击事件
document.querySelectorAll('.tool-footer .btn').forEach(btn => {
btn.addEventListener('click', function() {
const toolName = this.closest('.tool-card').querySelector('.tool-title').textContent;
if (toolName === '海报生成器') {
document.querySelector('.promotion-card:nth-child(2)').scrollIntoView({ behavior: 'smooth' });
} else {
alert(`正在打开 ${toolName}，请稍后...`);
}
});
});

// 确保导航栏在页面加载完成后可见
window.onload = function() {
    // 确保导航栏可见
    const navbarContainer = document.getElementById('navbar-container');
    if (navbarContainer) {
        navbarContainer.style.display = '';
    }
};
</script>