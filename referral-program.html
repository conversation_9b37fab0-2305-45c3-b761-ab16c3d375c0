<!DOCTYPE html> <html lang="zh-CN"> <head> <meta charset="UTF-8"> <meta name="viewport" content="width=device-width, initial-scale=1.0"> <title>邀请好友赠农资礼包 - 智慧农业平台</title> <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet"> <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet"> <link rel="stylesheet" href="css/common.css"> <link rel="stylesheet" href="css/navbar.css"> <style>
:root {
--primary-color: #28a745;
--secondary-color: #6c757d;
--light-bg: #f8f9fa;
--card-shadow: 0 4px 6px rgba(0,0,0,0.1);
--transition: all 0.3s ease;
--referral-color: #f39c12;
}
body {
background-color: var(--light-bg);
font-family: 'PingFang SC', '<PERSON> YaHei', sans-serif;
}
.referral-header {
background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80');
background-size: cover;
background-position: center;
height: 300px;
color: white;
display: flex;
align-items: center;
margin-bottom: 30px;
border-radius: 10px;
}
.referral-card {
background: white;
padding: 20px;
border-radius: 10px;
box-shadow: var(--card-shadow);
margin-bottom: 30px;
min-height: auto;
}
.stat-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 20px;
transition: var(--transition);
}
.stat-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.stat-icon {
font-size: 2rem;
color: var(--referral-color);
margin-bottom: 10px;
}
.stat-value {
font-size: 1.5rem;
font-weight: bold;
color: var(--referral-color);
margin-bottom: 5px;
}
.stat-label {
font-size: 0.9rem;
color: var(--secondary-color);
}
.gift-card {
background: var(--light-bg);
border-radius: 10px;
overflow: hidden;
margin-bottom: 20px;
transition: var(--transition);
min-height: auto;
border: 1px solid #eee;
position: relative;
}
.gift-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.gift-badge {
position: absolute;
top: 10px;
right: 10px;
background-color: var(--referral-color);
color: white;
padding: 5px 10px;
border-radius: 20px;
font-size: 0.8rem;
z-index: 1;
}
.gift-img {
width: 100%;
height: 200px;
object-fit: cover;
}
.gift-info {
padding: 15px;
}
.gift-title {
font-weight: bold;
font-size: 1.1rem;
margin-bottom: 5px;
}
.gift-desc {
color: var(--secondary-color);
margin-bottom: 10px;
font-size: 0.9rem;
height: 60px;
overflow: hidden;
}
.gift-meta {
display: flex;
justify-content: space-between;
margin-bottom: 10px;
font-size: 0.9rem;
}
.gift-value {
font-weight: bold;
color: var(--referral-color);
}
.gift-requirement {
color: var(--secondary-color);
}
.gift-items {
margin-bottom: 10px;
}
.gift-item {
display: flex;
align-items: center;
margin-bottom: 5px;
font-size: 0.9rem;
}
.gift-item i {
color: var(--referral-color);
margin-right: 5px;
}
.gift-actions {
display: flex;
gap: 10px;
}
.step-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 15px;
transition: var(--transition);
position: relative;
}
.step-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.step-header {
display: flex;
align-items: center;
margin-bottom: 15px;
}
.step-number {
width: 40px;
height: 40px;
border-radius: 50%;
background-color: var(--referral-color);
color: white;
display: flex;
align-items: center;
justify-content: center;
font-weight: bold;
margin-right: 15px;
flex-shrink: 0;
}
.step-title {
font-weight: bold;
font-size: 1.1rem;
}
.step-body {
margin-bottom: 15px;
}
.step-description {
color: var(--secondary-color);
margin-bottom: 15px;
}
.step-connector {
position: absolute;
left: 20px;
top: 55px;
width: 2px;
height: calc(100% - 40px);
background-color: var(--referral-color);
z-index: 0;
}
.benefit-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 15px;
transition: var(--transition);
min-height: auto;
}
.benefit-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.benefit-header {
display: flex;
align-items: center;
margin-bottom: 15px;
}
.benefit-icon {
width: 50px;
height: 50px;
border-radius: 10px;
background-color: var(--referral-color);
color: white;
display: flex;
align-items: center;
justify-content: center;
font-size: 1.5rem;
margin-right: 15px;
flex-shrink: 0;
}
.benefit-title {
font-weight: bold;
font-size: 1.1rem;
margin-bottom: 5px;
}
.benefit-subtitle {
font-size: 0.9rem;
color: var(--secondary-color);
}
.benefit-body {
margin-bottom: 15px;
}
.benefit-description {
color: var(--secondary-color);
margin-bottom: 15px;
}
.faq-item {
margin-bottom: 15px;
}
.faq-question {
font-weight: bold;
margin-bottom: 5px;
cursor: pointer;
display: flex;
justify-content: space-between;
align-items: center;
}
.faq-answer {
color: var(--secondary-color);
display: none;
padding: 10px 0;
}
.faq-answer.active {
display: block;
}
.ranking-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 15px;
transition: var(--transition);
}
.ranking-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.ranking-header {
display: flex;
align-items: center;
margin-bottom: 10px;
}
.ranking-number {
width: 30px;
height: 30px;
border-radius: 50%;
background-color: var(--referral-color);
color: white;
display: flex;
align-items: center;
justify-content: center;
font-weight: bold;
margin-right: 10px;
flex-shrink: 0;
}
.ranking-avatar {
width: 40px;
height: 40px;
border-radius: 50%;
object-fit: cover;
margin-right: 10px;
}
.ranking-user {
flex: 1;
}
.ranking-name {
font-weight: bold;
margin-bottom: 0;
}
.ranking-date {
font-size: 0.8rem;
color: var(--secondary-color);
}
.ranking-count {
font-weight: bold;
color: var(--referral-color);
font-size: 1.1rem;
}
.share-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 15px;
transition: var(--transition);
}
.share-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.share-qrcode {
width: 200px;
height: 200px;
margin: 0 auto 15px;
background-color: white;
padding: 10px;
border-radius: 10px;
}
.share-qrcode img {
width: 100%;
min-height: auto;
object-fit: contain;
}
.share-code {
text-align: center;
margin-bottom: 15px;
}
.share-code-value {
font-weight: bold;
font-size: 1.2rem;
color: var(--referral-color);
background-color: rgba(243, 156, 18, 0.1);
padding: 5px 10px;
border-radius: 5px;
display: inline-block;
}
.share-buttons {
display: flex;
justify-content: center;
gap: 10px;
margin-bottom: 15px;
}
.share-button {
width: 40px;
height: 40px;
border-radius: 50%;
display: flex;
align-items: center;
justify-content: center;
color: white;
font-size: 1.2rem;
cursor: pointer;
transition: var(--transition);
}
.share-button:hover {
transform: scale(1.1);
}
.share-wechat {
background-color: #07C160;
}
.share-weibo {
background-color: #E6162D;
}
.share-qq {
background-color: #12B7F5;
}
.share-link {
background-color: #6c757d;
}
.chart-container {
position: relative;
height: 300px;
width: 100%;
margin-bottom: 20px;
}
.progress-container {
margin-bottom: 20px;
}
.progress-label {
display: flex;
justify-content: space-between;
margin-bottom: 5px;
}
.progress-title {
font-weight: bold;
}
.progress-value {
color: var(--referral-color);
font-weight: bold;
}
.progress {
height: 10px;
border-radius: 5px;
}
.progress-bar {
background-color: var(--referral-color);
}
</style> </head> <body> <!-- 导航栏 -->
<div id="navbar-container"></div>
<!-- 主要内容 --> <div class="container py-5"> <div class="referral-header"> <div class="container"> <h1>邀请好友赠农资礼包</h1> <p class="lead">邀请好友注册使用智慧农业平台，双方均可获得丰厚农资礼包，邀请越多奖励越多，共同助力智慧农业发展</p> <div class="mt-4"> <button class="btn btn-warning btn-lg me-2"><i class="fas fa-share-alt me-2"></i>立即邀请好友</button> <button class="btn btn-outline-light btn-lg"><i class="fas fa-gift me-2"></i>查看我的礼包</button> </div> </div> </div> <!-- 顶部统计卡片 --> <div class="row mb-4"> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-users stat-icon"></i> <div class="stat-value">12,580</div> <div class="stat-label">成功邀请人数</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-gift stat-icon"></i> <div class="stat-value">35,640</div> <div class="stat-label">已发放礼包数</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-award stat-icon"></i> <div class="stat-value">256万</div> <div class="stat-label">礼包总价值(元)</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-user-plus stat-icon"></i> <div class="stat-value">68</div> <div class="stat-label">单人最高邀请数</div> </div> </div> </div> <!-- 主要内容区域 --> <div class="row"> <!-- 左侧内容 --> <div class="col-md-8"> <!-- 礼包列表 --> <div class="referral-card mb-4"> <h4 class="mb-4">邀请好友礼包</h4> <div class="row"> <div class="col-md-6 mb-4"> <div class="gift-card"> <div class="gift-badge">新手礼包</div> <img src="images/gift1.jpg" alt="新手礼包" class="gift-img"> <div class="gift-info"> <div class="gift-title">新手礼包</div> <div class="gift-desc">邀请1位好友注册并实名认证成功，双方均可获得价值100元的新手礼包，包含多种实用农资产品</div> <div class="gift-meta"> <div class="gift-value">价值：100元</div> <div class="gift-requirement">需邀请：1人</div> </div> <div class="gift-items"> <div class="gift-item"><i class="fas fa-check-circle"></i> 有机复合肥 1包</div> <div class="gift-item"><i class="fas fa-check-circle"></i> 高品质种子 1包</div> <div class="gift-item"><i class="fas fa-check-circle"></i> 生物农药 1瓶</div> </div> <div class="gift-actions"> <button class="btn btn-warning w-100"><i class="fas fa-share-alt me-2"></i>立即邀请</button> </div> </div> </div> </div> <div class="col-md-6 mb-4"> <div class="gift-card"> <div class="gift-badge">高级礼包</div> <img src="images/gift2.jpg" alt="高级礼包" class="gift-img"> <div class="gift-info"> <div class="gift-title">高级礼包</div> <div class="gift-desc">累计邀请5位好友注册并实名认证成功，可获得价值500元的高级礼包，包含多种高品质农资产品</div> <div class="gift-meta"> <div class="gift-value">价值：500元</div> <div class="gift-requirement">需邀请：5人</div> </div> <div class="gift-items"> <div class="gift-item"><i class="fas fa-check-circle"></i> 高效有机肥 2包</div> <div class="gift-item"><i class="fas fa-check-circle"></i> 特级种子礼盒 1盒</div> <div class="gift-item"><i class="fas fa-check-circle"></i> 生物农药套装 1套</div> </div> <div class="gift-actions"> <button class="btn btn-warning w-100"><i class="fas fa-share-alt me-2"></i>立即邀请</button> </div> </div> </div> </div> <div class="col-md-6 mb-4"> <div class="gift-card"> <div class="gift-badge">超级礼包</div> <img src="images/gift3.jpg" alt="超级礼包" class="gift-img"> <div class="gift-info"> <div class="gift-title">超级礼包</div> <div class="gift-desc">累计邀请10位好友注册并实名认证成功，可获得价值1000元的超级礼包，包含高端农资产品和智能设备</div> <div class="gift-meta"> <div class="gift-value">价值：1000元</div> <div class="gift-requirement">需邀请：10人</div> </div> <div class="gift-items"> <div class="gift-item"><i class="fas fa-check-circle"></i> 高端有机肥套装 1套</div> <div class="gift-item"><i class="fas fa-check-circle"></i> 特级种子礼盒 2盒</div> <div class="gift-item"><i class="fas fa-check-circle"></i> 智能土壤监测仪 1台</div> </div> <div class="gift-actions"> <button class="btn btn-warning w-100"><i class="fas fa-share-alt me-2"></i>立即邀请</button> </div> </div> </div> </div> <div class="col-md-6 mb-4"> <div class="gift-card"> <div class="gift-badge">至尊礼包</div> <img src="images/gift4.jpg" alt="至尊礼包" class="gift-img"> <div class="gift-info"> <div class="gift-title">至尊礼包</div> <div class="gift-desc">累计邀请20位好友注册并实名认证成功，可获得价值2000元的至尊礼包，包含高端农资产品和智能设备</div> <div class="gift-meta"> <div class="gift-value">价值：2000元</div> <div class="gift-requirement">需邀请：20人</div> </div> <div class="gift-items"> <div class="gift-item"><i class="fas fa-check-circle"></i> 高端有机肥套装 2套</div> <div class="gift-item"><i class="fas fa-check-circle"></i> 智能微泳系统 1套</div> <div class="gift-item"><i class="fas fa-check-circle"></i> 农资优惠券 2000元</div> </div> <div class="gift-actions"> <button class="btn btn-warning w-100"><i class="fas fa-share-alt me-2"></i>立即邀请</button> </div> </div> </div> </div> </div> </div> <!-- 邀请步骤 --> <div class="referral-card mb-4"> <h4 class="mb-4">邀请步骤</h4> <div class="step-card"> <div class="step-header"> <div class="step-number">1</div> <div class="step-title">获取邀请链接或二维码</div> </div> <div class="step-body"> <div class="step-description">
点击“立即邀请”按钮，获取您的专属邀请链接或二维码。每个用户都有唯一的邀请码，系统会自动记录您的邀请数据。
</div> </div> <div class="step-connector"></div> </div> <div class="step-card"> <div class="step-header"> <div class="step-number">2</div> <div class="step-title">分享给好友</div> </div> <div class="step-body"> <div class="step-description">
将邀请链接或二维码分享给您的好友，可以通过微信、QQ、微博等社交平台分享，也可以直接发送短信或展示二维码给好友扫码。
</div> </div> <div class="step-connector"></div> </div> <div class="step-card"> <div class="step-header"> <div class="step-number">3</div> <div class="step-title">好友注册并实名认证</div> </div> <div class="step-body"> <div class="step-description">
好友点击您分享的链接或扫描二维码，进入智慧农业平台注册页面。完成注册并进行实名认证，系统将自动记录您的邀请关系。
</div> </div> <div class="step-connector"></div> </div> <div class="step-card"> <div class="step-header"> <div class="step-number">4</div> <div class="step-title">双方获得礼包</div> </div> <div class="step-body"> <div class="step-description">
好友实名认证成功后，您和好友都将获得新手礼包。您可以在“我的礼包”中查看已获得的礼包，并可以选择立即使用或安排配送。
</div> </div> <div class="step-connector"></div> </div> <div class="step-card"> <div class="step-header"> <div class="step-number">5</div> <div class="step-title">继续邀请获得更多礼包</div> </div> <div class="step-body"> <div class="step-description">
继续邀请更多好友加入，累计邀请人数达到相应标准后，您将自动获得高级礼包、超级礼包或至尊礼包。邀请的好友越多，获得的礼包价值越高。
</div> </div> </div> </div> <!-- 邀请排行榜 --> <div class="referral-card mb-4"> <h4 class="mb-4">邀请排行榜</h4> <div class="ranking-card"> <div class="ranking-header"> <div class="ranking-number">1</div> <img src="images/user1.jpg" alt="用户头像" class="ranking-avatar"> <div class="ranking-user"> <h6 class="ranking-name">王农民</h6> <div class="ranking-date">浙江省杭州市</div> </div> <div class="ranking-count">68人</div> </div> </div> <div class="ranking-card"> <div class="ranking-header"> <div class="ranking-number">2</div> <img src="images/user2.jpg" alt="用户头像" class="ranking-avatar"> <div class="ranking-user"> <h6 class="ranking-name">李农民</h6> <div class="ranking-date">江苏省苏州市</div> </div> <div class="ranking-count">56人</div> </div> </div> <div class="ranking-card"> <div class="ranking-header"> <div class="ranking-number">3</div> <img src="images/user3.jpg" alt="用户头像" class="ranking-avatar"> <div class="ranking-user"> <h6 class="ranking-name">张农民</h6> <div class="ranking-date">山东省济南市</div> </div> <div class="ranking-count">45人</div> </div> </div> <div class="ranking-card"> <div class="ranking-header"> <div class="ranking-number">4</div> <img src="images/user4.jpg" alt="用户头像" class="ranking-avatar"> <div class="ranking-user"> <h6 class="ranking-name">赵农民</h6> <div class="ranking-date">安徽省合肥市</div> </div> <div class="ranking-count">42人</div> </div> </div> <div class="ranking-card"> <div class="ranking-header"> <div class="ranking-number">5</div> <img src="images/user5.jpg" alt="用户头像" class="ranking-avatar"> <div class="ranking-user"> <h6 class="ranking-name">刘农民</h6> <div class="ranking-date">河南省郑州市</div> </div> <div class="ranking-count">38人</div> </div> </div> <div class="text-center mt-3"> <button class="btn btn-outline-primary">查看完整排行榜</button> </div> </div> </div> <!-- 右侧内容 --> <div class="col-md-4"> <!-- 我的邀请码 --> <div class="referral-card"> <h4 class="mb-4">我的邀请码</h4> <div class="share-card"> <div class="share-qrcode"> <img src="images/referral-qrcode.jpg" alt="邀请二维码"> </div> <div class="share-code"> <div class="mb-2">邀请码</div> <div class="share-code-value">FARM88520</div> </div> <div class="share-buttons"> <div class="share-button share-wechat"><i class="fab fa-weixin"></i></div> <div class="share-button share-weibo"><i class="fab fa-weibo"></i></div> <div class="share-button share-qq"><i class="fab fa-qq"></i></div> <div class="share-button share-link"><i class="fas fa-link"></i></div> </div> <div class="d-grid"> <button class="btn btn-warning"><i class="fas fa-share-alt me-2"></i>立即邀请好友</button> </div> </div> </div> <!-- 我的邀请进度 --> <div class="referral-card mt-4"> <h4 class="mb-4">我的邀请进度</h4> <div class="progress-container"> <div class="progress-label"> <div class="progress-title">新手礼包</div> <div class="progress-value">3/1</div> </div> <div class="progress"> <div class="progress-bar" style="width: 100%"></div> </div> </div> <div class="progress-container"> <div class="progress-label"> <div class="progress-title">高级礼包</div> <div class="progress-value">3/5</div> </div> <div class="progress"> <div class="progress-bar" style="width: 60%"></div> </div> </div> <div class="progress-container"> <div class="progress-label"> <div class="progress-title">超级礼包</div> <div class="progress-value">3/10</div> </div> <div class="progress"> <div class="progress-bar" style="width: 30%"></div> </div> </div> <div class="progress-container"> <div class="progress-label"> <div class="progress-title">至尊礼包</div> <div class="progress-value">3/20</div> </div> <div class="progress"> <div class="progress-bar" style="width: 15%"></div> </div> </div> <div class="alert alert-success mt-3"> <i class="fas fa-info-circle me-2"></i>
您已成功邀请<strong>3</strong>位好友，获得<strong>1</strong>个新手礼包，还需邀请<strong>2</strong>位好友即可获得高级礼包！
</div> </div> <!-- 常见问题 --> <div class="referral-card mt-4"> <h4 class="mb-4">常见问题</h4> <div class="faq-item"> <div class="faq-question" onclick="toggleFaq(this)"> <span>如何邀请好友加入？</span> <i class="fas fa-chevron-down"></i> </div> <div class="faq-answer">
邀请好友加入非常简单，您可以点击“立即邀请”按钮，获取您的专属邀请链接或二维码，然后通过微信、QQ、微博等社交平台分享给您的好友。好友点击链接或扫描二维码后，完成注册并实名认证，即可成为您邀请的好友。
</div> </div> <div class="faq-item"> <div class="faq-question" onclick="toggleFaq(this)"> <span>邀请好友有哪些奖励？</span> <i class="fas fa-chevron-down"></i> </div> <div class="faq-answer">
邀请好友有丰厚的奖励。每成功邀请1位好友注册并实名认证，您和好友都将获得价值100元的新手礼包。累计邀请5位好友，您将获得价值500元的高级礼包。累计邀请10位好友，您将获得价值1000元的超级礼包。累计邀请20位好友，您将获得价值2000元的至尊礼包。
</div> </div> <div class="faq-item"> <div class="faq-question" onclick="toggleFaq(this)"> <span>礼包内包含哪些农资产品？</span> <i class="fas fa-chevron-down"></i> </div> <div class="faq-answer">
不同级别的礼包包含不同的农资产品。新手礼包包含有机复合肥、高品质种子和生物农药等基础农资产品。高级礼包包含高效有机肥、特级种子礼盒和生物农药套装等高品质农资产品。超级礼包和至尊礼包还包含智能土壤监测仪、智能微泳系统等高端智能设备和农资优惠券。
</div> </div> <div class="faq-item"> <div class="faq-question" onclick="toggleFaq(this)"> <span>如何查看和使用我的礼包？</span> <i class="fas fa-chevron-down"></i> </div> <div class="faq-answer">
您可以在“我的礼包”页面查看已获得的礼包。点击“查看我的礼包”按钮，进入礼包管理页面。在该页面中，您可以查看礼包详情、选择立即使用或安排配送。如果选择配送，需要填写收货地址和联系方式，我们将在3-5个工作日内安排配送。
</div> </div> <div class="faq-item"> <div class="faq-question" onclick="toggleFaq(this)"> <span>邀请的好友需要满足哪些条件？</span> <i class="fas fa-chevron-down"></i> </div> <div class="faq-answer">
您邀请的好友需要满足以下条件：1) 必须是新用户，即之前未在智慧农业平台注册过；2) 需要完成注册并进行实名认证；3) 需要通过您的邀请链接或二维码进入平台注册页面。只有满足这些条件，系统才会将其计入您的邀请人数中，并触发相应的礼包奖励。
</div> </div> </div> <!-- 邀请好友优势 --> <div class="referral-card mt-4"> <h4 class="mb-4">邀请好友优势</h4> <div class="benefit-card"> <div class="benefit-header"> <div class="benefit-icon"> <i class="fas fa-gift"></i> </div> <div> <div class="benefit-title">双方获益</div> <div class="benefit-subtitle">邀请人和被邀请人都能获得礼包</div> </div> </div> <div class="benefit-body"> <div class="benefit-description">
每成功邀请1位好友，您和好友都将获得价值100元的新手礼包，实现双方共赢。
</div> </div> </div> <div class="benefit-card"> <div class="benefit-header"> <div class="benefit-icon"> <i class="fas fa-award"></i> </div> <div> <div class="benefit-title">累计奖励</div> <div class="benefit-subtitle">邀请越多奖励越多</div> </div> </div> <div class="benefit-body"> <div class="benefit-description">
邀请的好友越多，获得的礼包价值越高。累计邀请20位好友，可获得总价值超过2000元的农资礼包。
</div> </div> </div> <div class="benefit-card"> <div class="benefit-header"> <div class="benefit-icon"> <i class="fas fa-trophy"></i> </div> <div> <div class="benefit-title">排行榜奖励</div> <div class="benefit-subtitle">排名靠前可获额外奖励</div> </div> </div> <div class="benefit-body"> <div class="benefit-description">
每月邀请人数排名前10的用户，可获得额外的农资礼包和智慧农业平台会员特权。
</div> </div> </div> </div> </div> </div> </div> <!-- 页脚 -->
<div id="footer-container"></div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="js/login-config.js"></script>
<script src="js/components.js"></script>
<script>
// 使用 common.js 加载导航栏

// 切换FAQ显示/隐藏
function toggleFaq(element) {
const answer = element.nextElementSibling;
const icon = element.querySelector('i');
if (answer.classList.contains('active')) {
answer.classList.remove('active');
icon.classList.remove('fa-chevron-up');
icon.classList.add('fa-chevron-down');
} else {
answer.classList.add('active');
icon.classList.remove('fa-chevron-down');
icon.classList.add('fa-chevron-up');
}
}
// 立即邀请按钮点击事件
document.querySelectorAll('.gift-actions .btn').forEach(btn => {
btn.addEventListener('click', function() {
const giftTitle = this.closest('.gift-card').querySelector('.gift-title').textContent;
alert(`您正在邀请好友获取 ${giftTitle}，请选择分享方式。`);
document.querySelector('.referral-card:nth-child(1)').scrollIntoView({ behavior: 'smooth' });
});
});
// 分享按钮点击事件
document.querySelectorAll('.share-button').forEach(btn => {
btn.addEventListener('click', function() {
let platform = '';
if (this.classList.contains('share-wechat')) platform = '微信';
else if (this.classList.contains('share-weibo')) platform = '微博';
else if (this.classList.contains('share-qq')) platform = 'QQ';
else if (this.classList.contains('share-link')) platform = '复制链接';
alert(`正在通过${platform}分享您的邀请码，请稍后...`);
});
});
// 立即邀请好友按钮点击事件
document.querySelector('.share-card .btn-warning').addEventListener('click', function() {
alert('请选择分享方式，将您的邀请码分享给好友。');
});
// 查看完整排行榜按钮点击事件
document.querySelector('.btn-outline-primary').addEventListener('click', function() {
alert('正在加载完整排行榜，请稍后...');
});
// 立即邀请好友按钮点击事件
document.querySelector('.btn-warning.btn-lg').addEventListener('click', function() {
document.querySelector('.referral-card:nth-child(1)').scrollIntoView({ behavior: 'smooth' });
});
// 查看我的礼包按钮点击事件
document.querySelector('.btn-outline-light.btn-lg').addEventListener('click', function() {
alert('正在跳转到我的礼包页面，请稍后...');
});

// 确保导航栏在页面加载完成后可见

// 页面加载完成后确保导航栏可见
window.onload = function() {
    // 确保导航栏可见
    const navbarContainer = document.getElementById('navbar-container');
    if (navbarContainer) {
        // 强制设置为可见
        navbarContainer.style.display = 'block';
        
        // 确保导航栏内容已加载
        if (!navbarContainer.innerHTML || navbarContainer.innerHTML.trim() === '') {
            // 如果导航栏内容为空，尝试重新加载
            if (typeof navbarHTML !== 'undefined') {
                navbarContainer.innerHTML = navbarHTML;
                
                // 重新应用登录状态和模块可见性
                if (typeof loadLoginState === 'function') loadLoginState();
                if (typeof updateNavbarDisplay === 'function') updateNavbarDisplay();
                if (typeof setActiveNavItem === 'function') setActiveNavItem();
                if (typeof setupLoginEvents === 'function') setupLoginEvents();
            }
        }
    }
};
</script> </body> </html>