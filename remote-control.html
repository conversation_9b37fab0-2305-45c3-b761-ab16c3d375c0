<!DOCTYPE html> <html lang="zh-CN"> <head> <meta charset="UTF-8"> <meta name="viewport" content="width=device-width, initial-scale=1.0"> <title>远程控制农业设备 - 智慧农业平台</title> <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet"> <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet"> <link rel="stylesheet" href="css/common.css"> <link rel="stylesheet" href="css/navbar.css"> <style>
:root {
--primary-color: #28a745;
--secondary-color: #6c757d;
--light-bg: #f8f9fa;
--card-shadow: 0 4px 6px rgba(0,0,0,0.1);
--transition: all 0.3s ease;
--device-color: #3498db;
--online-color: #28a745;
--offline-color: #dc3545;
--warning-color: #ffc107;
}
body {
background-color: var(--light-bg);
font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}
.remote-header {
background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80');
background-size: cover;
background-position: center;
height: 200px;
color: white;
display: flex;
align-items: center;
margin-bottom: 30px;
border-radius: 10px;
}
.remote-card {
background: white;
padding: 20px;
border-radius: 10px;
box-shadow: var(--card-shadow);
margin-bottom: 30px;
min-height: auto;
}
.stat-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 20px;
transition: var(--transition);
}
.stat-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.stat-icon {
font-size: 2rem;
margin-bottom: 10px;
}
.stat-icon.online {
color: var(--online-color);
}
.stat-icon.offline {
color: var(--offline-color);
}
.stat-icon.warning {
color: var(--warning-color);
}
.stat-icon.device {
color: var(--device-color);
}
.stat-value {
font-size: 1.5rem;
font-weight: bold;
margin-bottom: 5px;
}
.stat-label {
font-size: 0.9rem;
color: var(--secondary-color);
}
.map-container {
height: 400px;
border-radius: 10px;
overflow: hidden;
margin-bottom: 20px;
}
#map {
min-height: auto;
width: 100%;
}
.filter-btn {
padding: 5px 15px;
border-radius: 20px;
background-color: white;
border: 1px solid #ddd;
margin-right: 10px;
margin-bottom: 10px;
cursor: pointer;
transition: var(--transition);
}
.filter-btn.active {
background-color: var(--primary-color);
color: white;
border-color: var(--primary-color);
}
.filter-btn:hover:not(.active) {
background-color: #f0f0f0;
}
.device-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 15px;
transition: var(--transition);
}
.device-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.device-header {
display: flex;
justify-content: space-between;
align-items: center;
margin-bottom: 15px;
}
.device-title {
display: flex;
align-items: center;
}
.device-icon {
width: 40px;
height: 40px;
background-color: white;
border-radius: 50%;
display: flex;
align-items: center;
justify-content: center;
margin-right: 15px;
color: var(--device-color);
font-size: 1.2rem;
}
.device-name {
font-weight: bold;
}
.device-status {
display: inline-block;
padding: 3px 8px;
border-radius: 15px;
font-size: 0.8rem;
}
.status-online {
background-color: rgba(40, 167, 69, 0.1);
color: var(--online-color);
}
.status-offline {
background-color: rgba(220, 53, 69, 0.1);
color: var(--offline-color);
}
.status-warning {
background-color: rgba(255, 193, 7, 0.1);
color: var(--warning-color);
}
.device-body {
margin-bottom: 15px;
}
.device-info {
display: flex;
justify-content: space-between;
margin-bottom: 10px;
}
.device-label {
color: var(--secondary-color);
}
.device-value {
font-weight: bold;
}
.device-controls {
display: flex;
flex-wrap: wrap;
gap: 10px;
}
.control-item {
flex: 1;
min-width: 120px;
}
.control-label {
font-size: 0.9rem;
margin-bottom: 5px;
display: flex;
justify-content: space-between;
align-items: center;
}
.control-value {
font-size: 0.8rem;
color: var(--secondary-color);
}
.toggle-switch {
position: relative;
display: inline-block;
width: 60px;
height: 34px;
}
.toggle-switch input {
opacity: 0;
width: 0;
height: 0;
}
.toggle-slider {
position: absolute;
cursor: pointer;
top: 0;
left: 0;
right: 0;
bottom: 0;
background-color: #ccc;
transition: .4s;
border-radius: 34px;
}
.toggle-slider:before {
position: absolute;
content: "";
height: 26px;
width: 26px;
left: 4px;
bottom: 4px;
background-color: white;
transition: .4s;
border-radius: 50%;
}
input:checked + .toggle-slider {
background-color: var(--primary-color);
}
input:focus + .toggle-slider {
box-shadow: 0 0 1px var(--primary-color);
}
input:checked + .toggle-slider:before {
transform: translateX(26px);
}
.slider-control {
width: 100%;
}
.slider-value {
text-align: right;
font-weight: bold;
}
.device-footer {
display: flex;
justify-content: space-between;
align-items: center;
}
.device-battery {
display: flex;
align-items: center;
font-size: 0.9rem;
color: var(--secondary-color);
}
.battery-icon {
margin-right: 5px;
}
.device-actions {
display: flex;
gap: 10px;
}
.schedule-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 15px;
transition: var(--transition);
}
.schedule-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.schedule-header {
display: flex;
justify-content: space-between;
align-items: center;
margin-bottom: 15px;
}
.schedule-title {
display: flex;
align-items: center;
font-weight: bold;
}
.schedule-icon {
margin-right: 10px;
color: var(--device-color);
}
.schedule-body {
margin-bottom: 15px;
}
.schedule-info {
display: flex;
justify-content: space-between;
margin-bottom: 10px;
}
.schedule-label {
color: var(--secondary-color);
}
.schedule-value {
font-weight: bold;
}
.schedule-footer {
display: flex;
justify-content: flex-end;
gap: 10px;
}
.history-item {
display: flex;
margin-bottom: 15px;
padding-bottom: 15px;
border-bottom: 1px solid #eee;
}
.history-icon {
width: 40px;
height: 40px;
background-color: var(--light-bg);
border-radius: 50%;
display: flex;
align-items: center;
justify-content: center;
margin-right: 15px;
color: var(--device-color);
}
.history-content {
flex: 1;
}
.history-title {
font-weight: bold;
margin-bottom: 5px;
}
.history-time {
font-size: 0.9rem;
color: var(--secondary-color);
margin-bottom: 5px;
}
.history-desc {
font-size: 0.9rem;
}
.alert-item {
padding: 10px 15px;
border-radius: 5px;
margin-bottom: 10px;
display: flex;
align-items: center;
}
.alert-icon {
margin-right: 15px;
font-size: 1.2rem;
}
.alert-content {
flex: 1;
}
.alert-title {
font-weight: bold;
margin-bottom: 5px;
}
.alert-desc {
font-size: 0.9rem;
}
.alert-warning {
background-color: rgba(255, 193, 7, 0.2);
border-left: 4px solid #ffc107;
}
.alert-danger {
background-color: rgba(220, 53, 69, 0.2);
border-left: 4px solid #dc3545;
}
.alert-info {
background-color: rgba(13, 202, 240, 0.2);
border-left: 4px solid #0dcaf0;
}
.camera-feed {
width: 100%;
height: 200px;
background-color: #000;
border-radius: 10px;
margin-bottom: 15px;
position: relative;
overflow: hidden;
}
.camera-image {
width: 100%;
min-height: auto;
object-fit: cover;
}
.camera-overlay {
position: absolute;
top: 10px;
left: 10px;
background-color: rgba(0, 0, 0, 0.5);
color: white;
padding: 5px 10px;
border-radius: 5px;
font-size: 0.8rem;
}
.camera-controls {
display: flex;
justify-content: center;
gap: 10px;
margin-bottom: 15px;
}
.camera-btn {
width: 40px;
height: 40px;
border-radius: 50%;
background-color: white;
border: 1px solid #ddd;
display: flex;
align-items: center;
justify-content: center;
cursor: pointer;
transition: var(--transition);
}
.camera-btn:hover {
background-color: var(--light-bg);
}
.camera-btn.active {
background-color: var(--device-color);
color: white;
}
</style> </head> <body> <!-- 导航栏 -->
<div id="navbar-container"></div>
<!-- 主要内容 --> <div class="container py-5"> <div class="remote-header"> <div class="container"> <h1>远程控制农业设备</h1> <p class="lead">通过智能物联网技术，远程监控和控制农业设备，提高农业生产效率，降低人力成本</p> </div> </div> <!-- 顶部统计卡片 --> <div class="row mb-4"> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-robot stat-icon device"></i> <div class="stat-value">24</div> <div class="stat-label">设备总数</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-check-circle stat-icon online"></i> <div class="stat-value">18</div> <div class="stat-label">在线设备</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-times-circle stat-icon offline"></i> <div class="stat-value">6</div> <div class="stat-label">离线设备</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-exclamation-triangle stat-icon warning"></i> <div class="stat-value">3</div> <div class="stat-label">警告设备</div> </div> </div> </div> <!-- 主要内容区域 --> <div class="row"> <!-- 左侧内容 --> <div class="col-md-8"> <!-- 设备地图 --> <div class="remote-card"> <div class="d-flex justify-content-between align-items-center mb-4"> <h4>设备分布地图</h4> <div> <button class="filter-btn active">全部</button> <button class="filter-btn">灵溪设备</button> <button class="filter-btn">监控设备</button> <button class="filter-btn">农机设备</button> </div> </div> <div class="map-container"> <div id="map"></div> </div> </div> <!-- 设备控制卡片 --> <div class="remote-card"> <div class="d-flex justify-content-between align-items-center mb-4"> <h4>设备控制</h4> <div> <button class="btn btn-sm btn-outline-success me-2">全部</button> <button class="btn btn-sm btn-success me-2">在线</button> <button class="btn btn-sm btn-outline-success">离线</button> </div> </div> <!-- 电磁阀设备 --> <div class="device-card"> <div class="device-header"> <div class="device-title"> <div class="device-icon"> <i class="fas fa-faucet"></i> </div> <div> <div class="device-name">电磁阀 #001</div> <div class="device-location">区域A - 水稻田</div> </div> </div> <div class="device-status status-online">在线</div> </div> <div class="device-body"> <div class="device-info"> <div class="device-label">当前状态：</div> <div class="device-value">开启</div> </div> <div class="device-info"> <div class="device-label">当前流量：</div> <div class="device-value">2.5 m³/h</div> </div> <div class="device-info"> <div class="device-label">运行时间：</div> <div class="device-value">30分钟</div> </div> <div class="device-controls mt-3"> <div class="control-item"> <div class="control-label"> <span>电源开关</span> </div> <label class="toggle-switch"> <input type="checkbox" checked> <span class="toggle-slider"></span> </label> </div> <div class="control-item"> <div class="control-label"> <span>流量调节</span> <span class="control-value">2.5 m³/h</span> </div> <input type="range" class="form-range slider-control" min="0" max="5" step="0.1" value="2.5"> </div> <div class="control-item"> <div class="control-label"> <span>定时关闭</span> <span class="control-value">30分钟</span> </div> <input type="range" class="form-range slider-control" min="0" max="60" step="5" value="30"> </div> </div> </div> <div class="device-footer"> <div class="device-battery"> <i class="fas fa-battery-three-quarters battery-icon"></i> <span>85%</span> </div> <div class="device-actions"> <button class="btn btn-sm btn-outline-primary"><i class="fas fa-history"></i> 历史记录</button> <button class="btn btn-sm btn-outline-success"><i class="fas fa-cog"></i> 设置</button> </div> </div> </div> <!-- 水泵设备 --> <div class="device-card"> <div class="device-header"> <div class="device-title"> <div class="device-icon"> <i class="fas fa-tint"></i> </div> <div> <div class="device-name">水泵 #002</div> <div class="device-location">区域A - 水源站</div> </div> </div> <div class="device-status status-online">在线</div> </div> <div class="device-body"> <div class="device-info"> <div class="device-label">当前状态：</div> <div class="device-value">开启</div> </div> <div class="device-info"> <div class="device-label">当前压力：</div> <div class="device-value">0.35 MPa</div> </div> <div class="device-info"> <div class="device-label">运行时间：</div> <div class="device-value">2小时</div> </div> <div class="device-controls mt-3"> <div class="control-item"> <div class="control-label"> <span>电源开关</span> </div> <label class="toggle-switch"> <input type="checkbox" checked> <span class="toggle-slider"></span> </label> </div> <div class="control-item"> <div class="control-label"> <span>压力调节</span> <span class="control-value">0.35 MPa</span> </div> <input type="range" class="form-range slider-control" min="0" max="0.5" step="0.01" value="0.35"> </div> <div class="control-item"> <div class="control-label"> <span>定时关闭</span> <span class="control-value">2小时</span> </div> <input type="range" class="form-range slider-control" min="0" max="24" step="0.5" value="2"> </div> </div> </div> <div class="device-footer"> <div class="device-battery"> <i class="fas fa-battery-full battery-icon"></i> <span>92%</span> </div> <div class="device-actions"> <button class="btn btn-sm btn-outline-primary"><i class="fas fa-history"></i> 历史记录</button> <button class="btn btn-sm btn-outline-success"><i class="fas fa-cog"></i> 设置</button> </div> </div> </div> <!-- 监控摄像头 --> <div class="device-card"> <div class="device-header"> <div class="device-title"> <div class="device-icon"> <i class="fas fa-video"></i> </div> <div> <div class="device-name">监控摄像头 #003</div> <div class="device-location">区域A - 田间道</div> </div> </div> <div class="device-status status-online">在线</div> </div> <div class="device-body"> <div class="camera-feed"> <img src="images/farm-camera.jpg" alt="监控画面" class="camera-image"> <div class="camera-overlay">A区块 - 实时监控</div> </div> <div class="camera-controls"> <button class="camera-btn"><i class="fas fa-arrow-left"></i></button> <button class="camera-btn"><i class="fas fa-arrow-up"></i></button> <button class="camera-btn"><i class="fas fa-arrow-down"></i></button> <button class="camera-btn"><i class="fas fa-arrow-right"></i></button> <button class="camera-btn"><i class="fas fa-plus"></i></button> <button class="camera-btn"><i class="fas fa-minus"></i></button> <button class="camera-btn active"><i class="fas fa-camera"></i></button> <button class="camera-btn"><i class="fas fa-video"></i></button> </div> <div class="device-controls"> <div class="control-item"> <div class="control-label"> <span>电源开关</span> </div> <label class="toggle-switch"> <input type="checkbox" checked> <span class="toggle-slider"></span> </label> </div> <div class="control-item"> <div class="control-label"> <span>画面亮度</span> <span class="control-value">70%</span> </div> <input type="range" class="form-range slider-control" min="0" max="100" step="10" value="70"> </div> <div class="control-item"> <div class="control-label"> <span>自动巡视</span> </div> <label class="toggle-switch"> <input type="checkbox"> <span class="toggle-slider"></span> </label> </div> </div> </div> <div class="device-footer"> <div class="device-battery"> <i class="fas fa-battery-half battery-icon"></i> <span>65%</span> </div> <div class="device-actions"> <button class="btn btn-sm btn-outline-primary"><i class="fas fa-history"></i> 历史记录</button> <button class="btn btn-sm btn-outline-success"><i class="fas fa-cog"></i> 设置</button> </div> </div> </div> <!-- 自动播种机 --> <div class="device-card"> <div class="device-header"> <div class="device-title"> <div class="device-icon"> <i class="fas fa-tractor"></i> </div> <div> <div class="device-name">自动播种机 #004</div> <div class="device-location">区域B - 小麦田</div> </div> </div> <div class="device-status status-warning">警告</div> </div> <div class="device-body"> <div class="alert-item alert-warning mb-3"> <div class="alert-icon"> <i class="fas fa-exclamation-triangle"></i> </div> <div class="alert-content"> <div class="alert-title">设备警告</div> <div class="alert-desc">播种机种子仓存量低，请及时添加种子。</div> </div> </div> <div class="device-info"> <div class="device-label">当前状态：</div> <div class="device-value">待机</div> </div> <div class="device-info"> <div class="device-label">种子仓存量：</div> <div class="device-value">15%</div> </div> <div class="device-info"> <div class="device-label">上次播种时间：</div> <div class="device-value">2023-06-15 09:30</div> </div> <div class="device-controls mt-3"> <div class="control-item"> <div class="control-label"> <span>电源开关</span> </div> <label class="toggle-switch"> <input type="checkbox"> <span class="toggle-slider"></span> </label> </div> <div class="control-item"> <div class="control-label"> <span>播种密度</span> <span class="control-value">中等</span> </div> <select class="form-select form-select-sm"> <option>低密度</option> <option selected>中等</option> <option>高密度</option> </select> </div> <div class="control-item"> <div class="control-label"> <span>播种深度</span> <span class="control-value">3cm</span> </div> <select class="form-select form-select-sm"> <option>2cm</option> <option selected>3cm</option> <option>4cm</option> <option>5cm</option> </select> </div> </div> </div> <div class="device-footer"> <div class="device-battery"> <i class="fas fa-battery-quarter battery-icon text-warning"></i> <span>25%</span> </div> <div class="device-actions"> <button class="btn btn-sm btn-outline-primary"><i class="fas fa-history"></i> 历史记录</button> <button class="btn btn-sm btn-outline-success"><i class="fas fa-cog"></i> 设置</button> </div> </div> </div> <!-- 气象站 --> <div class="device-card"> <div class="device-header"> <div class="device-title"> <div class="device-icon"> <i class="fas fa-cloud-sun"></i> </div> <div> <div class="device-name">气象站 #005</div> <div class="device-location">区域A - 中心区域</div> </div> </div> <div class="device-status status-offline">离线</div> </div> <div class="device-body"> <div class="alert-item alert-danger mb-3"> <div class="alert-icon"> <i class="fas fa-exclamation-circle"></i> </div> <div class="alert-content"> <div class="alert-title">设备离线</div> <div class="alert-desc">气象站已离线，请检查电源或网络连接。</div> </div> </div> <div class="device-info"> <div class="device-label">当前状态：</div> <div class="device-value">离线</div> </div> <div class="device-info"> <div class="device-label">最后在线时间：</div> <div class="device-value">2023-06-18 15:45</div> </div> <div class="device-info"> <div class="device-label">最后数据更新：</div> <div class="device-value">2023-06-18 15:40</div> </div> </div> <div class="device-footer"> <div class="device-battery"> <i class="fas fa-battery-empty battery-icon text-danger"></i> <span>0%</span> </div> <div class="device-actions"> <button class="btn btn-sm btn-outline-primary"><i class="fas fa-history"></i> 历史记录</button> <button class="btn btn-sm btn-danger"><i class="fas fa-sync"></i> 重启设备</button> </div> </div> </div> </div> </div> <!-- 右侧内容 --> <div class="col-md-4"> <!-- 设备定时任务 --> <div class="remote-card"> <div class="d-flex justify-content-between align-items-center mb-4"> <h4>设备定时任务</h4> <button class="btn btn-sm btn-success"><i class="fas fa-plus"></i> 新建任务</button> </div> <div class="schedule-card"> <div class="schedule-header"> <div class="schedule-title"> <i class="fas fa-calendar-alt schedule-icon"></i> <span>A区块定时灵溪</span> </div> <div class="form-check form-switch"> <input class="form-check-input" type="checkbox" id="scheduleSwitch1" checked> </div> </div> <div class="schedule-body"> <div class="schedule-info"> <div class="schedule-label">执行时间：</div> <div class="schedule-value">每天 06:00 - 06:30</div> </div> <div class="schedule-info"> <div class="schedule-label">目标设备：</div> <div class="schedule-value">电磁阀 #001, #002, #003</div> </div> <div class="schedule-info"> <div class="schedule-label">任务设置：</div> <div class="schedule-value">流量2.5 m³/h，持续30分钟</div> </div> <div class="schedule-info"> <div class="schedule-label">下次执行：</div> <div class="schedule-value">明天 06:00</div> </div> </div> <div class="schedule-footer"> <button class="btn btn-sm btn-outline-primary"><i class="fas fa-edit"></i> 编辑</button> <button class="btn btn-sm btn-outline-danger"><i class="fas fa-trash"></i> 删除</button> </div> </div> <div class="schedule-card"> <div class="schedule-header"> <div class="schedule-title"> <i class="fas fa-calendar-alt schedule-icon"></i> <span>B区块播种任务</span> </div> <div class="form-check form-switch"> <input class="form-check-input" type="checkbox" id="scheduleSwitch2" checked> </div> </div> <div class="schedule-body"> <div class="schedule-info"> <div class="schedule-label">执行时间：</div> <div class="schedule-value">2023-06-25 08:00</div> </div> <div class="schedule-info"> <div class="schedule-label">目标设备：</div> <div class="schedule-value">自动播种机 #004</div> </div> <div class="schedule-info"> <div class="schedule-label">任务设置：</div> <div class="schedule-value">中等密度，播种深度3cm</div> </div> <div class="schedule-info"> <div class="schedule-label">剩余时间：</div> <div class="schedule-value">6天 16小时</div> </div> </div> <div class="schedule-footer"> <button class="btn btn-sm btn-outline-primary"><i class="fas fa-edit"></i> 编辑</button> <button class="btn btn-sm btn-outline-danger"><i class="fas fa-trash"></i> 删除</button> </div> </div> <div class="schedule-card"> <div class="schedule-header"> <div class="schedule-title"> <i class="fas fa-calendar-alt schedule-icon"></i> <span>监控摄像头巡视</span> </div> <div class="form-check form-switch"> <input class="form-check-input" type="checkbox" id="scheduleSwitch3"> </div> </div> <div class="schedule-body"> <div class="schedule-info"> <div class="schedule-label">执行时间：</div> <div class="schedule-value">每小时执行一次</div> </div> <div class="schedule-info"> <div class="schedule-label">目标设备：</div> <div class="schedule-value">监控摄像头 #003</div> </div> <div class="schedule-info"> <div class="schedule-label">任务设置：</div> <div class="schedule-value">自动巡视，持续5分钟</div> </div> <div class="schedule-info"> <div class="schedule-label">状态：</div> <div class="schedule-value">已暂停</div> </div> </div> <div class="schedule-footer"> <button class="btn btn-sm btn-outline-primary"><i class="fas fa-edit"></i> 编辑</button> <button class="btn btn-sm btn-outline-danger"><i class="fas fa-trash"></i> 删除</button> </div> </div> </div> <!-- 设备警报 --> <div class="remote-card"> <h4 class="mb-4">设备警报</h4> <div class="alert-item alert-danger"> <div class="alert-icon"> <i class="fas fa-exclamation-circle"></i> </div> <div class="alert-content"> <div class="alert-title">气象站离线</div> <div class="alert-desc">气象站 #005 已离线，请检查电源或网络连接。</div> <div class="text-end mt-2"> <button class="btn btn-sm btn-danger">处理</button> </div> </div> </div> <div class="alert-item alert-warning"> <div class="alert-icon"> <i class="fas fa-exclamation-triangle"></i> </div> <div class="alert-content"> <div class="alert-title">播种机种子仓存量低</div> <div class="alert-desc">自动播种机 #004 种子仓存量低，请及时添加种子。</div> <div class="text-end mt-2"> <button class="btn btn-sm btn-warning">处理</button> </div> </div> </div> <div class="alert-item alert-warning"> <div class="alert-icon"> <i class="fas fa-exclamation-triangle"></i> </div> <div class="alert-content"> <div class="alert-title">监控摄像头电量低</div> <div class="alert-desc">监控摄像头 #003 电量低于30%，请及时充电。</div> <div class="text-end mt-2"> <button class="btn btn-sm btn-warning">处理</button> </div> </div> </div> <div class="alert-item alert-info"> <div class="alert-icon"> <i class="fas fa-info-circle"></i> </div> <div class="alert-content"> <div class="alert-title">电磁阀维护提醒</div> <div class="alert-desc">电磁阀 #001 运行时间超过500小时，建议进行维护。</div> <div class="text-end mt-2"> <button class="btn btn-sm btn-info">处理</button> </div> </div> </div> </div> <!-- 操作历史 --> <div class="remote-card"> <h4 class="mb-4">操作历史</h4> <div class="history-item"> <div class="history-icon"> <i class="fas fa-power-off"></i> </div> <div class="history-content"> <div class="history-title">电磁阀开启</div> <div class="history-time">2023-06-20 08:30</div> <div class="history-desc">电磁阀 #001 被手动开启，流量设置为2.5 m³/h。</div> </div> </div> <div class="history-item"> <div class="history-icon"> <i class="fas fa-cog"></i> </div> <div class="history-content"> <div class="history-title">水泵参数调整</div> <div class="history-time">2023-06-19 14:45</div> <div class="history-desc">水泵 #002 压力参数从0.3 MPa调整为0.35 MPa。</div> </div> </div> <div class="history-item"> <div class="history-icon"> <i class="fas fa-camera"></i> </div> <div class="history-content"> <div class="history-title">监控摄像头拍照</div> <div class="history-time">2023-06-18 10:15</div> <div class="history-desc">监控摄像头 #003 拍摄了一张照片。</div> </div> </div> <div class="history-item"> <div class="history-icon"> <i class="fas fa-power-off"></i> </div> <div class="history-content"> <div class="history-title">电磁阀关闭</div> <div class="history-time">2023-06-18 09:00</div> <div class="history-desc">电磁阀 #001 自动关闭，运行时间30分钟。</div> </div> </div> </div> </div> </div> </div> <!-- 页脚 -->
<div id="footer-container"></div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="js/login-config.js"></script>
<script src="js/components.js"></script>
<script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script> <script>
// 使用 common.js 加载导航栏

// 初始化地图
var map = L.map('map').setView([30.7741, 120.7551], 14);
L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
attribution: '© OpenStreetMap contributors'
}).addTo(map);
// 添加设备标记
const devices = [
{ id: 1, name: "电磁阀 #001", position: [30.7741, 120.7551], type: "irrigation", status: "online" },
{ id: 2, name: "电磁阀 #002", position: [30.7731, 120.7561], type: "irrigation", status: "online" },
{ id: 3, name: "电磁阀 #003", position: [30.7721, 120.7571], type: "irrigation", status: "online" },
{ id: 4, name: "水泵 #002", position: [30.7751, 120.7541], type: "irrigation", status: "online" },
{ id: 5, name: "监控摄像头 #003", position: [30.7761, 120.7531], type: "monitoring", status: "online" },
{ id: 6, name: "自动播种机 #004", position: [30.7841, 120.7651], type: "machinery", status: "warning" },
{ id: 7, name: "气象站 #005", position: [30.7701, 120.7591], type: "monitoring", status: "offline" }
];
devices.forEach(device => {
// 设置图标颜色
let color = '#28a745';
if (device.status === 'offline') {
color = '#dc3545';
} else if (device.status === 'warning') {
color = '#ffc107';
}
// 创建标记
const marker = L.marker(device.position)
.bindPopup(`
<div class="text-center"> <h6>${device.name}</h6> <p class="mb-1">类型: ${device.type}</p> <p class="mb-1">状态: ${device.status}</p> <button class="btn btn-sm btn-success mt-2">控制设备</button> </div>
`)
.addTo(map);
});
// 筛选按钮点击事件
document.querySelectorAll('.filter-btn').forEach(btn => {
btn.addEventListener('click', function() {
document.querySelectorAll('.filter-btn').forEach(b => {
b.classList.remove('active');
});
this.classList.add('active');
});
});
// 设备筛选按钮点击事件
document.querySelectorAll('.remote-card .btn-sm').forEach(btn => {
if (btn.classList.contains('btn-success') || btn.classList.contains('btn-outline-success')) {
btn.addEventListener('click', function() {
if (!this.classList.contains('btn-success')) {
document.querySelectorAll('.remote-card .btn-sm').forEach(b => {
if (b.classList.contains('btn-success') || b.classList.contains('btn-outline-success')) {
b.classList.remove('btn-success');
b.classList.add('btn-outline-success');
}
});
this.classList.remove('btn-outline-success');
this.classList.add('btn-success');
}
});
}
});
// 电源开关点击事件
document.querySelectorAll('.toggle-switch input').forEach(toggle => {
toggle.addEventListener('change', function() {
const deviceCard = this.closest('.device-card');
const deviceName = deviceCard.querySelector('.device-name').textContent;
const deviceStatus = deviceCard.querySelector('.device-value');
if (this.checked) {
deviceStatus.textContent = '开启';
alert(`${deviceName} 已开启！`);
} else {
deviceStatus.textContent = '关闭';
alert(`${deviceName} 已关闭！`);
}
});
});
// 流量调节滑块事件
document.querySelectorAll('.slider-control').forEach(slider => {
slider.addEventListener('input', function() {
const controlLabel = this.previousElementSibling;
const controlValue = controlLabel.querySelector('.control-value');
const labelText = controlLabel.querySelector('span:first-child').textContent;
if (labelText.includes('流量')) {
controlValue.textContent = `${this.value} m³/h`;
} else if (labelText.includes('压力')) {
controlValue.textContent = `${this.value} MPa`;
} else if (labelText.includes('定时')) {
if (parseInt(this.value) >= 60) {
const hours = Math.floor(this.value / 60);
const minutes = this.value % 60;
controlValue.textContent = `${hours}小时${minutes > 0 ? ` ${minutes}分钟` : ''}`;
} else {
controlValue.textContent = `${this.value}分钟`;
}
} else if (labelText.includes('亮度')) {
controlValue.textContent = `${this.value}%`;
}
});
});
// 摄像头控制按钮点击事件
document.querySelectorAll('.camera-btn').forEach(btn => {
btn.addEventListener('click', function() {
if (this.querySelector('i').classList.contains('fa-camera') ||
this.querySelector('i').classList.contains('fa-video')) {
document.querySelectorAll('.camera-btn').forEach(b => {
if (b.querySelector('i').classList.contains('fa-camera') ||
b.querySelector('i').classList.contains('fa-video')) {
b.classList.remove('active');
}
});
this.classList.add('active');
}
});
});
// 定时任务开关点击事件
document.querySelectorAll('.form-check-input').forEach(toggle => {
toggle.addEventListener('change', function() {
const scheduleCard = this.closest('.schedule-card');
const scheduleTitle = scheduleCard.querySelector('.schedule-title span').textContent;
if (this.checked) {
alert(`${scheduleTitle} 已启用！`);
} else {
alert(`${scheduleTitle} 已暂停！`);
}
});
});
// 新建任务按钮点击事件
document.querySelector('.remote-card .btn-success').addEventListener('click', function() {
alert('正在创建新任务...');
});
// 警报处理按钮点击事件
document.querySelectorAll('.alert-item .btn').forEach(btn => {
btn.addEventListener('click', function() {
const alertItem = this.closest('.alert-item');
const alertTitle = alertItem.querySelector('.alert-title').textContent;
alert(`正在处理: ${alertTitle}`);
});
});

// 确保导航栏在页面加载完成后可见

// 页面加载完成后确保导航栏可见
window.onload = function() {
    // 确保导航栏可见
    const navbarContainer = document.getElementById('navbar-container');
    if (navbarContainer) {
        // 强制设置为可见
        navbarContainer.style.display = 'block';
        
        // 确保导航栏内容已加载
        if (!navbarContainer.innerHTML || navbarContainer.innerHTML.trim() === '') {
            // 如果导航栏内容为空，尝试重新加载
            if (typeof navbarHTML !== 'undefined') {
                navbarContainer.innerHTML = navbarHTML;
                
                // 重新应用登录状态和模块可见性
                if (typeof loadLoginState === 'function') loadLoginState();
                if (typeof updateNavbarDisplay === 'function') updateNavbarDisplay();
                if (typeof setActiveNavItem === 'function') setActiveNavItem();
                if (typeof setupLoginEvents === 'function') setupLoginEvents();
            }
        }
    }
};
</script>