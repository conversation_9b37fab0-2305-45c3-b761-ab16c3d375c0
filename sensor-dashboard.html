<!DOCTYPE html> <html lang="zh-CN"> <head> <meta charset="UTF-8"> <meta name="viewport" content="width=device-width, initial-scale=1.0"> <title>智能传感数据看板 - 智慧农业平台</title> <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet">
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />
<link rel="stylesheet" href="css/common.css">
<link rel="stylesheet" href="css/navbar.css"> <style>
:root {
--primary-color: #28a745;
--secondary-color: #6c757d;
--light-bg: #f8f9fa;
--card-shadow: 0 4px 6px rgba(0,0,0,0.1);
--transition: all 0.3s ease;
}
body {
background-color: var(--light-bg);
font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}
.dashboard-header {
background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80');
background-size: cover;
background-position: center;
height: 200px;
color: white;
display: flex;
align-items: center;
margin-bottom: 30px;
border-radius: 10px;
}
.dashboard-card {
background: white;
padding: 20px;
border-radius: 10px;
box-shadow: var(--card-shadow);
margin-bottom: 30px;
min-height: auto;
}
.sensor-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 20px;
transition: var(--transition);
}
.sensor-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.sensor-icon {
font-size: 2rem;
color: var(--primary-color);
margin-bottom: 10px;
}
.sensor-value {
font-size: 2rem;
font-weight: bold;
color: var(--primary-color);
margin-bottom: 5px;
}
.sensor-label {
font-size: 0.9rem;
color: var(--secondary-color);
}
.chart-container {
height: 300px;
width: 100%;
margin-bottom: 20px;
}
.data-table {
width: 100%;
margin-bottom: 20px;
}
.data-table th {
background-color: var(--primary-color);
color: white;
}
.alert-badge {
display: inline-block;
padding: 5px 10px;
border-radius: 20px;
font-size: 0.8rem;
margin-right: 10px;
}
.alert-normal {
background-color: #28a745;
color: white;
}
.alert-warning {
background-color: #ffc107;
color: black;
}
.alert-danger {
background-color: #dc3545;
color: white;
}
.nav-tabs .nav-link {
color: var(--secondary-color);
}
.nav-tabs .nav-link.active {
color: var(--primary-color);
font-weight: bold;
}
.tab-content {
padding: 20px 0;
}
</style> </head> <body> <!-- 导航栏 -->
<div id="navbar-container"></div>
<!-- 主要内容 --> <div class="container py-5"> <div class="dashboard-header"> <div class="container"> <h1>智能传感数据看板</h1> <p class="lead">实时监测土壤、气候等环境数据，为农业生产提供精准决策支持</p> </div> </div> <!-- 顶部统计卡片 --> <div class="row mb-4"> <div class="col-md-3"> <div class="sensor-card text-center"> <i class="fas fa-thermometer-half sensor-icon"></i> <div class="sensor-value">26.5°C</div> <div class="sensor-label">当前温度</div> </div> </div> <div class="col-md-3"> <div class="sensor-card text-center"> <i class="fas fa-tint sensor-icon"></i> <div class="sensor-value">68%</div> <div class="sensor-label">空气湿度</div> </div> </div> <div class="col-md-3"> <div class="sensor-card text-center"> <i class="fas fa-cloud-sun sensor-icon"></i> <div class="sensor-value">856 W/m²</div> <div class="sensor-label">光照强度</div> </div> </div> <div class="col-md-3"> <div class="sensor-card text-center"> <i class="fas fa-wind sensor-icon"></i> <div class="sensor-value">3.2 m/s</div> <div class="sensor-label">风速</div> </div> </div> </div> <!-- 主要内容区域 --> <div class="row"> <!-- 左侧内容 --> <div class="col-md-8"> <!-- 数据图表 --> <div class="dashboard-card mb-4"> <h4 class="mb-4">环境数据趋势</h4> <ul class="nav nav-tabs" id="dataTab" role="tablist"> <li class="nav-item" role="presentation"> <button class="nav-link active" id="temperature-tab" data-bs-toggle="tab" data-bs-target="#temperature" type="button" role="tab" aria-controls="temperature" aria-selected="true">温度</button> </li> <li class="nav-item" role="presentation"> <button class="nav-link" id="humidity-tab" data-bs-toggle="tab" data-bs-target="#humidity" type="button" role="tab" aria-controls="humidity" aria-selected="false">湿度</button> </li> <li class="nav-item" role="presentation"> <button class="nav-link" id="light-tab" data-bs-toggle="tab" data-bs-target="#light" type="button" role="tab" aria-controls="light" aria-selected="false">光照</button> </li> <li class="nav-item" role="presentation"> <button class="nav-link" id="soil-tab" data-bs-toggle="tab" data-bs-target="#soil" type="button" role="tab" aria-controls="soil" aria-selected="false">土壤</button> </li> </ul> <div class="tab-content" id="dataTabContent"> <div class="tab-pane fade show active" id="temperature" role="tabpanel" aria-labelledby="temperature-tab"> <div class="chart-container">
    <canvas id="temperatureChart"></canvas>
</div> <div class="d-flex justify-content-between"> <div> <div><strong>最高温度：</strong> 28.5°C</div> <div><strong>最低温度：</strong> 18.2°C</div> </div> <div> <div><strong>平均温度：</strong> 24.3°C</div> <div><strong>温度波动：</strong> 10.3°C</div> </div> <div> <div><strong>状态：</strong> <span class="alert-badge alert-normal">正常</span></div> <div><strong>建议：</strong> 保持现有管理方式</div> </div> </div> </div> <div class="tab-pane fade" id="humidity" role="tabpanel" aria-labelledby="humidity-tab"> <div class="chart-container">
    <canvas id="humidityChart"></canvas>
</div> <div class="d-flex justify-content-between"> <div> <div><strong>最高湿度：</strong> 85%</div> <div><strong>最低湿度：</strong> 55%</div> </div> <div> <div><strong>平均湿度：</strong> 68%</div> <div><strong>湿度波动：</strong> 30%</div> </div> <div> <div><strong>状态：</strong> <span class="alert-badge alert-normal">正常</span></div> <div><strong>建议：</strong> 保持现有管理方式</div> </div> </div> </div> <div class="tab-pane fade" id="light" role="tabpanel" aria-labelledby="light-tab"> <div class="chart-container">
    <canvas id="lightChart"></canvas>
</div> <div class="d-flex justify-content-between"> <div> <div><strong>最高光照：</strong> 1200 W/m²</div> <div><strong>最低光照：</strong> 0 W/m²</div> </div> <div> <div><strong>平均光照：</strong> 650 W/m²</div> <div><strong>光照时长：</strong> 12.5小时/天</div> </div> <div> <div><strong>状态：</strong> <span class="alert-badge alert-normal">正常</span></div> <div><strong>建议：</strong> 保持现有管理方式</div> </div> </div> </div> <div class="tab-pane fade" id="soil" role="tabpanel" aria-labelledby="soil-tab"> <div class="chart-container">
    <canvas id="soilChart"></canvas>
</div> <div class="d-flex justify-content-between"> <div> <div><strong>土壤湿度：</strong> 35%</div> <div><strong>土壤温度：</strong> 22.5°C</div> </div> <div> <div><strong>土壤pH值：</strong> 6.8</div> <div><strong>土壤EC值：</strong> 1.2 mS/cm</div> </div> <div> <div><strong>状态：</strong> <span class="alert-badge alert-warning">偏干</span></div> <div><strong>建议：</strong> 适当增加灌溉量</div> </div> </div> </div> </div> </div> <!-- 传感器数据表格 --> <div class="dashboard-card mb-4"> <h4 class="mb-4">传感器数据记录</h4> <div class="table-responsive"> <table class="table table-striped data-table"> <thead> <tr> <th>时间</th> <th>温度(°C)</th> <th>湿度(%)</th> <th>光照(W/m²)</th> <th>土壤湿度(%)</th> <th>状态</th> </tr> </thead> <tbody> <tr> <td>2023-10-15 14:00</td> <td>26.5</td> <td>68</td> <td>856</td> <td>35</td> <td><span class="alert-badge alert-normal">正常</span></td> </tr> <tr> <td>2023-10-15 13:00</td> <td>27.2</td> <td>65</td> <td>920</td> <td>36</td> <td><span class="alert-badge alert-normal">正常</span></td> </tr> <tr> <td>2023-10-15 12:00</td> <td>28.1</td> <td>62</td> <td>1050</td> <td>38</td> <td><span class="alert-badge alert-normal">正常</span></td> </tr> <tr> <td>2023-10-15 11:00</td> <td>27.8</td> <td>60</td> <td>1120</td> <td>40</td> <td><span class="alert-badge alert-normal">正常</span></td> </tr> <tr> <td>2023-10-15 10:00</td> <td>26.3</td> <td>63</td> <td>980</td> <td>42</td> <td><span class="alert-badge alert-normal">正常</span></td> </tr> <tr> <td>2023-10-15 09:00</td> <td>24.5</td> <td>68</td> <td>820</td> <td>45</td> <td><span class="alert-badge alert-normal">正常</span></td> </tr> <tr> <td>2023-10-15 08:00</td> <td>22.8</td> <td>72</td> <td>650</td> <td>48</td> <td><span class="alert-badge alert-normal">正常</span></td> </tr> </tbody> </table> </div> <div class="d-flex justify-content-between align-items-center">
    <div>
        <button class="btn btn-outline-success me-2">查看更多数据</button>
    </div>
    <div class="dropdown">
        <button class="btn btn-outline-primary dropdown-toggle" type="button" id="exportDropdown" data-bs-toggle="dropdown" aria-expanded="false">
            <i class="fas fa-download me-1"></i> 导出数据
        </button>
        <ul class="dropdown-menu" aria-labelledby="exportDropdown">
            <li><a class="dropdown-item" href="#" id="exportCSV"><i class="fas fa-file-csv me-2"></i>CSV 格式</a></li>
            <li><a class="dropdown-item" href="#" id="exportExcel"><i class="fas fa-file-excel me-2"></i>Excel 格式</a></li>
            <li><a class="dropdown-item" href="#" id="exportPDF"><i class="fas fa-file-pdf me-2"></i>PDF 格式</a></li>
        </ul>
    </div>
</div> </div> </div> <!-- 右侧内容 --> <div class="col-md-4"> <!-- 传感器状态 --> <div class="dashboard-card"> <h4 class="mb-4">传感器状态</h4> <div class="d-flex justify-content-between mb-3"> <div>温度传感器</div> <div><span class="badge bg-success">在线</span></div> </div> <div class="d-flex justify-content-between mb-3"> <div>湿度传感器</div> <div><span class="badge bg-success">在线</span></div> </div> <div class="d-flex justify-content-between mb-3"> <div>光照传感器</div> <div><span class="badge bg-success">在线</span></div> </div> <div class="d-flex justify-content-between mb-3"> <div>土壤湿度传感器</div> <div><span class="badge bg-success">在线</span></div> </div> <div class="d-flex justify-content-between mb-3"> <div>土壤pH传感器</div> <div><span class="badge bg-success">在线</span></div> </div> <div class="d-flex justify-content-between mb-3"> <div>风速传感器</div> <div><span class="badge bg-success">在线</span></div> </div> <div class="d-flex justify-content-between mb-3"> <div>雨量传感器</div> <div><span class="badge bg-warning">离线</span></div> </div> <div class="alert alert-info mt-3"> <i class="fas fa-info-circle me-2"></i>
雨量传感器已离线，技术人员正在处理中。
</div> </div> <!-- 传感器位置地图 -->
<div class="dashboard-card mt-4">
    <h4 class="mb-4">传感器分布地图</h4>
    <div id="sensor-map" style="height: 250px; border-radius: 5px;"></div>
    <div class="d-flex justify-content-between mt-3">
        <div><i class="fas fa-thermometer-half text-danger me-1"></i> 温度传感器</div>
        <div><i class="fas fa-tint text-primary me-1"></i> 湿度传感器</div>
        <div><i class="fas fa-sun text-warning me-1"></i> 光照传感器</div>
    </div>
</div>

<!-- 预警信息 --> <div class="dashboard-card mt-4"> <h4 class="mb-4">预警信息</h4> <div class="alert alert-warning"> <div class="d-flex justify-content-between"> <div><i class="fas fa-exclamation-triangle me-2"></i> 土壤湿度偏低</div> <div>10:15</div> </div> <div class="mt-2">当前土壤湿度为35%，低于正常范围(40%-60%)，建议适当增加灌溉量。</div> </div> <div class="alert alert-info"> <div class="d-flex justify-content-between"> <div><i class="fas fa-info-circle me-2"></i> 天气预报</div> <div>08:30</div> </div> <div class="mt-2">明天预计有小雨，降水概率80%，建议暂停明天的灌溉计划。</div> </div> <div class="alert alert-success"> <div class="d-flex justify-content-between"> <div><i class="fas fa-check-circle me-2"></i> 系统检测</div> <div>06:00</div> </div> <div class="mt-2">所有传感器数据在正常范围内，作物生长状况良好。</div> </div> </div> <!-- 操作建议 --> <div class="dashboard-card mt-4"> <h4 class="mb-4">操作建议</h4> <div class="mb-3"> <div class="fw-bold mb-2">灌溉建议</div> <div class="d-flex align-items-center"> <div class="progress flex-grow-1 me-3" style="height: 10px;"> <div class="progress-bar bg-warning" role="progressbar" style="width: 35%;" aria-valuenow="35" aria-valuemin="0" aria-valuemax="100"></div> </div> <div>35%</div> </div> <div class="text-muted mt-1">建议增加灌溉量，目标土壤湿度45%</div> </div> <div class="mb-3"> <div class="fw-bold mb-2">施肥建议</div> <div class="d-flex align-items-center"> <div class="progress flex-grow-1 me-3" style="height: 10px;"> <div class="progress-bar bg-success" role="progressbar" style="width: 80%;" aria-valuenow="80" aria-valuemin="0" aria-valuemax="100"></div> </div> <div>80%</div> </div> <div class="text-muted mt-1">土壤养分充足，暂不需要施肥</div> </div> <div class="mb-3"> <div class="fw-bold mb-2">病虫害防治</div> <div class="d-flex align-items-center"> <div class="progress flex-grow-1 me-3" style="height: 10px;"> <div class="progress-bar bg-success" role="progressbar" style="width: 90%;" aria-valuenow="90" aria-valuemin="0" aria-valuemax="100"></div> </div> <div>90%</div> </div> <div class="text-muted mt-1">未检测到病虫害，继续定期监测</div> </div> <div class="mt-4"> <button class="btn btn-success w-100">执行建议操作</button> </div> </div> </div> </div> </div> <!-- 页脚 -->
<div id="footer-container"></div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
<script src="js/login-config.js"></script>
<script src="js/components.js"></script>

 <script>
document.addEventListener('DOMContentLoaded', function() {
    // 初始化温度图表
    const temperatureCtx = document.getElementById('temperatureChart').getContext('2d');
    const temperatureChart = new Chart(temperatureCtx, {
        type: 'line',
        data: {
            labels: ['08:00', '09:00', '10:00', '11:00', '12:00', '13:00', '14:00'],
            datasets: [{
                label: '温度 (°C)',
                data: [22.8, 24.5, 26.3, 27.8, 28.1, 27.2, 26.5],
                borderColor: 'rgba(255, 99, 132, 1)',
                backgroundColor: 'rgba(255, 99, 132, 0.2)',
                borderWidth: 2,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                },
                tooltip: {
                    mode: 'index',
                    intersect: false
                }
            },
            scales: {
                y: {
                    beginAtZero: false,
                    min: 15,
                    max: 35
                }
            }
        }
    });

    // 初始化湿度图表
    const humidityCtx = document.getElementById('humidityChart').getContext('2d');
    const humidityChart = new Chart(humidityCtx, {
        type: 'line',
        data: {
            labels: ['08:00', '09:00', '10:00', '11:00', '12:00', '13:00', '14:00'],
            datasets: [{
                label: '湿度 (%)',
                data: [72, 68, 63, 60, 62, 65, 68],
                borderColor: 'rgba(54, 162, 235, 1)',
                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                borderWidth: 2,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                },
                tooltip: {
                    mode: 'index',
                    intersect: false
                }
            },
            scales: {
                y: {
                    beginAtZero: false,
                    min: 50,
                    max: 90
                }
            }
        }
    });

    // 初始化光照图表
    const lightCtx = document.getElementById('lightChart').getContext('2d');
    const lightChart = new Chart(lightCtx, {
        type: 'line',
        data: {
            labels: ['08:00', '09:00', '10:00', '11:00', '12:00', '13:00', '14:00'],
            datasets: [{
                label: '光照强度 (W/m²)',
                data: [650, 820, 980, 1120, 1050, 920, 856],
                borderColor: 'rgba(255, 206, 86, 1)',
                backgroundColor: 'rgba(255, 206, 86, 0.2)',
                borderWidth: 2,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                },
                tooltip: {
                    mode: 'index',
                    intersect: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // 初始化土壤图表
    const soilCtx = document.getElementById('soilChart').getContext('2d');
    const soilChart = new Chart(soilCtx, {
        type: 'line',
        data: {
            labels: ['08:00', '09:00', '10:00', '11:00', '12:00', '13:00', '14:00'],
            datasets: [{
                label: '土壤湿度 (%)',
                data: [48, 45, 42, 40, 38, 36, 35],
                borderColor: 'rgba(75, 192, 192, 1)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                borderWidth: 2,
                fill: true,
                tension: 0.4
            }, {
                label: '土壤温度 (°C)',
                data: [20.5, 21.2, 21.8, 22.3, 22.5, 22.5, 22.5],
                borderColor: 'rgba(153, 102, 255, 1)',
                backgroundColor: 'rgba(153, 102, 255, 0.2)',
                borderWidth: 2,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                },
                tooltip: {
                    mode: 'index',
                    intersect: false
                }
            },
            scales: {
                y: {
                    beginAtZero: false,
                    min: 20,
                    max: 60
                }
            }
        }
    });

    // 初始化传感器分布地图
    const map = L.map('sensor-map').setView([30.7741, 120.7551], 14);
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors'
    }).addTo(map);

    // 添加传感器标记
    const sensors = [
        { id: 1, type: 'temperature', name: '温度传感器1', position: [30.7741, 120.7551], status: 'online' },
        { id: 2, type: 'temperature', name: '温度传感器2', position: [30.7721, 120.7531], status: 'online' },
        { id: 3, type: 'humidity', name: '湿度传感器1', position: [30.7751, 120.7561], status: 'online' },
        { id: 4, type: 'humidity', name: '湿度传感器2', position: [30.7731, 120.7541], status: 'online' },
        { id: 5, type: 'light', name: '光照传感器1', position: [30.7761, 120.7571], status: 'online' },
        { id: 6, type: 'light', name: '光照传感器2', position: [30.7711, 120.7521], status: 'online' },
        { id: 7, type: 'soil', name: '土壤传感器1', position: [30.7771, 120.7581], status: 'online' },
        { id: 8, type: 'soil', name: '土壤传感器2', position: [30.7701, 120.7511], status: 'online' }
    ];

    // 定义不同类型传感器的图标
    const sensorIcons = {
        temperature: L.divIcon({
            html: '<i class="fas fa-thermometer-half" style="color: #dc3545;"></i>',
            className: 'sensor-map-icon',
            iconSize: [20, 20]
        }),
        humidity: L.divIcon({
            html: '<i class="fas fa-tint" style="color: #0d6efd;"></i>',
            className: 'sensor-map-icon',
            iconSize: [20, 20]
        }),
        light: L.divIcon({
            html: '<i class="fas fa-sun" style="color: #ffc107;"></i>',
            className: 'sensor-map-icon',
            iconSize: [20, 20]
        }),
        soil: L.divIcon({
            html: '<i class="fas fa-seedling" style="color: #198754;"></i>',
            className: 'sensor-map-icon',
            iconSize: [20, 20]
        })
    };

    // 添加传感器标记到地图
    sensors.forEach(sensor => {
        const marker = L.marker(sensor.position, { icon: sensorIcons[sensor.type] })
            .bindPopup(`
                <div class="text-center">
                    <h6>${sensor.name}</h6>
                    <p class="mb-1">状态: ${sensor.status === 'online' ? '<span class="text-success">在线</span>' : '<span class="text-warning">离线</span>'}</p>
                    <button class="btn btn-sm btn-outline-primary mt-2">查看详情</button>
                </div>
            `)
            .addTo(map);
    });

    // 查看更多数据按钮点击事件
    document.querySelector('.btn-outline-success').addEventListener('click', function() {
        alert('正在加载更多数据，请稍后...');
    });

    // 执行建议操作按钮点击事件
    document.querySelector('.btn-success').addEventListener('click', function() {
        alert('正在执行建议操作，请稍后...');
    });

    // 导出数据按钮点击事件
    document.getElementById('exportCSV').addEventListener('click', function(e) {
        e.preventDefault();
        alert('正在导出CSV格式数据，请稍后...');
    });

    document.getElementById('exportExcel').addEventListener('click', function(e) {
        e.preventDefault();
        alert('正在导出Excel格式数据，请稍后...');
    });

    document.getElementById('exportPDF').addEventListener('click', function(e) {
        e.preventDefault();
        alert('正在导出PDF格式数据，请稍后...');
    });

    // 添加实时数据更新功能
    setInterval(function() {
        // 模拟实时数据更新
        const now = new Date();
        const hours = now.getHours().toString().padStart(2, '0');
        const minutes = now.getMinutes().toString().padStart(2, '0');
        const timeString = `${hours}:${minutes}`;

        // 生成随机数据
        const newTemp = (25 + Math.random() * 5).toFixed(1);
        const newHumidity = (65 + Math.random() * 10).toFixed(0);
        const newLight = (800 + Math.random() * 200).toFixed(0);
        const newSoilMoisture = (35 + Math.random() * 5).toFixed(0);

        // 更新顶部卡片数据
        document.querySelector('.sensor-card:nth-child(1) .sensor-value').textContent = `${newTemp}°C`;
        document.querySelector('.sensor-card:nth-child(2) .sensor-value').textContent = `${newHumidity}%`;
        document.querySelector('.sensor-card:nth-child(3) .sensor-value').textContent = `${newLight} W/m²`;

        // 添加闪烁效果
        const cards = document.querySelectorAll('.sensor-card');
        cards.forEach(card => {
            card.classList.add('highlight');
            setTimeout(() => {
                card.classList.remove('highlight');
            }, 1000);
        });
    }, 30000); // 每30秒更新一次
});

// 确保导航栏在页面加载完成后可见

// 页面加载完成后确保导航栏可见
window.onload = function() {
    // 确保导航栏可见
    const navbarContainer = document.getElementById('navbar-container');
    if (navbarContainer) {
        // 强制设置为可见
        navbarContainer.style.display = 'block';
        
        // 确保导航栏内容已加载
        if (!navbarContainer.innerHTML || navbarContainer.innerHTML.trim() === '') {
            // 如果导航栏内容为空，尝试重新加载
            if (typeof navbarHTML !== 'undefined') {
                navbarContainer.innerHTML = navbarHTML;
                
                // 重新应用登录状态和模块可见性
                if (typeof loadLoginState === 'function') loadLoginState();
                if (typeof updateNavbarDisplay === 'function') updateNavbarDisplay();
                if (typeof setActiveNavItem === 'function') setActiveNavItem();
                if (typeof setupLoginEvents === 'function') setupLoginEvents();
            }
        }
    }
};
</script>

<style>
/* 添加传感器图标样式 */
.sensor-map-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px !important;
    height: 30px !important;
    background-color: white;
    border-radius: 50%;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

.sensor-map-icon i {
    font-size: 16px;
}

/* 添加数据更新闪烁效果 */
@keyframes highlight {
    0% { transform: translateY(0); }
    50% { transform: translateY(-5px); background-color: rgba(40, 167, 69, 0.1); }
    100% { transform: translateY(0); }
}

.sensor-card.highlight {
    animation: highlight 1s ease;
}
</style> </body> </html>
