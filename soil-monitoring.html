<!DOCTYPE html> <html lang="zh-CN"> <head> <meta charset="UTF-8"> <meta name="viewport" content="width=device-width, initial-scale=1.0"> <title>土壤监测 - 智慧农业平台</title> <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet"> <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet"> <link rel="stylesheet" href="css/common.css"> <link rel="stylesheet" href="css/navbar.css"> <script src="https://cdn.jsdelivr.net/npm/chart.js"></script> <style>
:root {
--primary-color: #28a745;
--secondary-color: #6c757d;
--light-bg: #f8f9fa;
--card-shadow: 0 4px 6px rgba(0,0,0,0.1);
--transition: all 0.3s ease;
}
body {
background-color: var(--light-bg);
font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}
.monitoring-header {
background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80');
background-size: cover;
background-position: center;
height: 200px;
color: white;
display: flex;
align-items: center;
margin-bottom: 30px;
border-radius: 10px;
}
.monitoring-card {
background: white;
padding: 20px;
border-radius: 10px;
box-shadow: var(--card-shadow);
margin-bottom: 30px;
min-height: auto;
}
.stat-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 20px;
transition: var(--transition);
}
.stat-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.stat-icon {
font-size: 2rem;
color: var(--primary-color);
margin-bottom: 10px;
}
.stat-value {
font-size: 1.5rem;
font-weight: bold;
color: var(--primary-color);
margin-bottom: 5px;
}
.stat-label {
font-size: 0.9rem;
color: var(--secondary-color);
}
.chart-container {
position: relative;
height: 300px;
width: 100%;
}
.map-container {
height: 400px;
border-radius: 10px;
overflow: hidden;
margin-bottom: 20px;
}
#map {
min-height: auto;
width: 100%;
}
.sensor-marker {
width: 30px;
height: 30px;
background-color: var(--primary-color);
border-radius: 50%;
display: flex;
align-items: center;
justify-content: center;
color: white;
font-weight: bold;
}
.sensor-marker.warning {
background-color: #ffc107;
}
.sensor-marker.danger {
background-color: #dc3545;
}
.filter-btn {
padding: 5px 15px;
border-radius: 20px;
background-color: white;
border: 1px solid #ddd;
margin-right: 10px;
margin-bottom: 10px;
cursor: pointer;
transition: var(--transition);
}
.filter-btn.active {
background-color: var(--primary-color);
color: white;
border-color: var(--primary-color);
}
.filter-btn:hover:not(.active) {
background-color: #f0f0f0;
}
.sensor-card {
display: flex;
align-items: center;
padding: 15px;
border-radius: 10px;
background: var(--light-bg);
margin-bottom: 15px;
transition: var(--transition);
}
.sensor-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.sensor-icon {
width: 50px;
height: 50px;
background-color: white;
border-radius: 50%;
display: flex;
align-items: center;
justify-content: center;
margin-right: 15px;
color: var(--primary-color);
font-size: 1.5rem;
}
.sensor-info {
flex: 1;
}
.sensor-name {
font-weight: bold;
margin-bottom: 5px;
}
.sensor-status {
font-size: 0.9rem;
color: var(--secondary-color);
}
.sensor-status.normal {
color: var(--primary-color);
}
.sensor-status.warning {
color: #ffc107;
}
.sensor-status.danger {
color: #dc3545;
}
.sensor-actions {
margin-left: 15px;
}
.soil-data-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 15px;
transition: var(--transition);
}
.soil-data-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.soil-data-title {
font-weight: bold;
margin-bottom: 10px;
display: flex;
align-items: center;
}
.soil-data-icon {
margin-right: 10px;
color: var(--primary-color);
}
.soil-data-value {
font-size: 1.5rem;
font-weight: bold;
margin-bottom: 5px;
}
.soil-data-desc {
font-size: 0.9rem;
color: var(--secondary-color);
}
.soil-data-status {
display: inline-block;
padding: 3px 8px;
border-radius: 15px;
font-size: 0.8rem;
margin-left: 10px;
}
.status-normal {
background-color: rgba(40, 167, 69, 0.2);
color: var(--primary-color);
}
.status-warning {
background-color: rgba(255, 193, 7, 0.2);
color: #ffc107;
}
.status-danger {
background-color: rgba(220, 53, 69, 0.2);
color: #dc3545;
}
.recommendation-card {
background-color: rgba(40, 167, 69, 0.1);
border-left: 4px solid var(--primary-color);
padding: 15px;
margin-bottom: 15px;
border-radius: 0 5px 5px 0;
}
.recommendation-title {
font-weight: bold;
margin-bottom: 10px;
display: flex;
align-items: center;
}
.recommendation-icon {
color: var(--primary-color);
margin-right: 10px;
}
.recommendation-content {
color: var(--secondary-color);
}
.alert-item {
padding: 10px 15px;
border-radius: 5px;
margin-bottom: 10px;
display: flex;
align-items: center;
}
.alert-icon {
margin-right: 15px;
font-size: 1.2rem;
}
.alert-content {
flex: 1;
}
.alert-title {
font-weight: bold;
margin-bottom: 5px;
}
.alert-desc {
font-size: 0.9rem;
}
.alert-warning {
background-color: rgba(255, 193, 7, 0.2);
border-left: 4px solid #ffc107;
}
.alert-danger {
background-color: rgba(220, 53, 69, 0.2);
border-left: 4px solid #dc3545;
}
.alert-info {
background-color: rgba(13, 202, 240, 0.2);
border-left: 4px solid #0dcaf0;
}
.history-item {
display: flex;
margin-bottom: 15px;
padding-bottom: 15px;
border-bottom: 1px solid #eee;
}
.history-icon {
width: 40px;
height: 40px;
background-color: var(--light-bg);
border-radius: 50%;
display: flex;
align-items: center;
justify-content: center;
margin-right: 15px;
color: var(--primary-color);
}
.history-content {
flex: 1;
}
.history-title {
font-weight: bold;
margin-bottom: 5px;
}
.history-time {
font-size: 0.9rem;
color: var(--secondary-color);
margin-bottom: 5px;
}
.history-desc {
font-size: 0.9rem;
}
</style> </head> <body> <!-- 导航栏 -->
<div id="navbar-container"></div>
<!-- 主要内容 --> <div class="container py-5"> <div class="monitoring-header"> <div class="container"> <h1>土壤监测</h1> <p class="lead">实时监控土壤湿度、温度、pH值和养分含量，为农作物生长提供最佳环境</p> </div> </div> <!-- 顶部统计卡片 --> <div class="row mb-4"> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-tint stat-icon"></i> <div class="stat-value">35%</div> <div class="stat-label">平均土壤湿度</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-thermometer-half stat-icon"></i> <div class="stat-value">22.5°C</div> <div class="stat-label">平均土壤温度</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-flask stat-icon"></i> <div class="stat-value">6.8</div> <div class="stat-label">平均pH值</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-leaf stat-icon"></i> <div class="stat-value">85%</div> <div class="stat-label">土壤肥力指数</div> </div> </div> </div> <!-- 主要内容区域 --> <div class="row"> <!-- 左侧内容 --> <div class="col-md-8"> <!-- 传感器地图 --> <div class="monitoring-card"> <div class="d-flex justify-content-between align-items-center mb-4"> <h4>土壤传感器分布地图</h4> <div> <button class="filter-btn active">全部</button> <button class="filter-btn">湿度传感器</button> <button class="filter-btn">温度传感器</button> <button class="filter-btn">pH值传感器</button> <button class="filter-btn">养分传感器</button> </div> </div> <div class="map-container"> <div id="map"></div> </div> </div> <!-- 土壤数据图表 --> <div class="monitoring-card"> <div class="d-flex justify-content-between align-items-center mb-4"> <h4>土壤数据趋势</h4> <div> <button class="btn btn-sm btn-outline-success me-2">日</button> <button class="btn btn-sm btn-success me-2">周</button> <button class="btn btn-sm btn-outline-success">月</button> </div> </div> <div class="chart-container"> <canvas id="soilChart"></canvas> </div> </div> <!-- 传感器状态 --> <div class="monitoring-card"> <div class="d-flex justify-content-between align-items-center mb-4"> <h4>传感器状态</h4> <a href="#" class="btn btn-sm btn-outline-success">查看全部</a> </div> <div class="sensor-card"> <div class="sensor-icon"> <i class="fas fa-tint"></i> </div> <div class="sensor-info"> <div class="sensor-name">土壤湿度传感器 #001</div> <div class="sensor-status normal">正常 - 当前数据: 38% - 电池电量: 85%</div> </div> <div class="sensor-actions"> <button class="btn btn-sm btn-outline-success">查看详情</button> </div> </div> <div class="sensor-card"> <div class="sensor-icon"> <i class="fas fa-thermometer-half"></i> </div> <div class="sensor-info"> <div class="sensor-name">土壤温度传感器 #002</div> <div class="sensor-status normal">正常 - 当前数据: 22.8°C - 电池电量: 92%</div> </div> <div class="sensor-actions"> <button class="btn btn-sm btn-outline-success">查看详情</button> </div> </div> <div class="sensor-card"> <div class="sensor-icon"> <i class="fas fa-flask"></i> </div> <div class="sensor-info"> <div class="sensor-name">pH值传感器 #003</div> <div class="sensor-status normal">正常 - 当前数据: 6.8 - 电池电量: 78%</div> </div> <div class="sensor-actions"> <button class="btn btn-sm btn-outline-success">查看详情</button> </div> </div> <div class="sensor-card"> <div class="sensor-icon"> <i class="fas fa-leaf"></i> </div> <div class="sensor-info"> <div class="sensor-name">土壤养分传感器 #004</div> <div class="sensor-status warning">警告 - 氮含量偏低 - 电池电量: 65%</div> </div> <div class="sensor-actions"> <button class="btn btn-sm btn-outline-warning">查看详情</button> </div> </div> <div class="sensor-card"> <div class="sensor-icon"> <i class="fas fa-tint"></i> </div> <div class="sensor-info"> <div class="sensor-name">土壤湿度传感器 #005</div> <div class="sensor-status danger">危险 - 湿度过低 (25%) - 电池电量: 72%</div> </div> <div class="sensor-actions"> <button class="btn btn-sm btn-outline-danger">查看详情</button> </div> </div> </div> </div> <!-- 右侧内容 --> <div class="col-md-4"> <!-- 当前土壤数据 --> <div class="monitoring-card"> <h4 class="mb-4">当前土壤数据</h4> <div class="soil-data-card"> <div class="soil-data-title"> <i class="fas fa-tint soil-data-icon"></i> <span>土壤湿度</span> <span class="soil-data-status status-warning">偏低</span> </div> <div class="soil-data-value">35%</div> <div class="soil-data-desc">适宜范围：40% - 60%</div> </div> <div class="soil-data-card"> <div class="soil-data-title"> <i class="fas fa-thermometer-half soil-data-icon"></i> <span>土壤温度</span> <span class="soil-data-status status-normal">正常</span> </div> <div class="soil-data-value">22.5°C</div> <div class="soil-data-desc">适宜范围：18°C - 25°C</div> </div> <div class="soil-data-card"> <div class="soil-data-title"> <i class="fas fa-flask soil-data-icon"></i> <span>pH值</span> <span class="soil-data-status status-normal">正常</span> </div> <div class="soil-data-value">6.8</div> <div class="soil-data-desc">适宜范围：6.5 - 7.5</div> </div> <div class="soil-data-card"> <div class="soil-data-title"> <i class="fas fa-leaf soil-data-icon"></i> <span>养分含量</span> <span class="soil-data-status status-warning">氮含量偏低</span> </div> <div class="soil-data-value">氮: 0.15% | 磺: 0.08% | 钾: 0.20%</div> <div class="soil-data-desc">适宜范围：氮: 0.2% - 0.3% | 磺: 0.05% - 0.1% | 钾: 0.15% - 0.25%</div> </div> </div> <!-- 智能建议 --> <div class="monitoring-card"> <h4 class="mb-4">智能建议</h4> <div class="recommendation-card"> <div class="recommendation-title"> <i class="fas fa-lightbulb recommendation-icon"></i> <span>灵溪建议</span> </div> <div class="recommendation-content"> <p>当前土壤湿度偏低，建议在上午进行灵溪，每些电磁阀开启30分钟。</p> <button class="btn btn-sm btn-success mt-2">立即执行</button> </div> </div> <div class="recommendation-card"> <div class="recommendation-title"> <i class="fas fa-lightbulb recommendation-icon"></i> <span>施肥建议</span> </div> <div class="recommendation-content"> <p>土壤氮含量偏低，建议追施氮肥，每些用量15kg。</p> <button class="btn btn-sm btn-success mt-2">查看施肥计划</button> </div> </div> </div> <!-- 预警信息 --> <div class="monitoring-card"> <h4 class="mb-4">预警信息</h4> <div class="alert-item alert-danger"> <div class="alert-icon"> <i class="fas fa-exclamation-circle"></i> </div> <div class="alert-content"> <div class="alert-title">土壤湿度过低</div> <div class="alert-desc">A区块土壤湿度低于30%，建议及时灵溪</div> </div> </div> <div class="alert-item alert-warning"> <div class="alert-icon"> <i class="fas fa-exclamation-triangle"></i> </div> <div class="alert-content"> <div class="alert-title">养分不足</div> <div class="alert-desc">B区块土壤氮含量偏低，可能影响作物生长</div> </div> </div> </div> <!-- 操作历史 --> <div class="monitoring-card"> <h4 class="mb-4">操作历史</h4> <div class="history-item"> <div class="history-icon"> <i class="fas fa-tint"></i> </div> <div class="history-content"> <div class="history-title">灵溪操作</div> <div class="history-time">2023-06-15 08:30</div> <div class="history-desc">对A区块进行了灵溪，持续时间30分钟</div> </div> </div> <div class="history-item"> <div class="history-icon"> <i class="fas fa-prescription-bottle"></i> </div> <div class="history-content"> <div class="history-title">施肥操作</div> <div class="history-time">2023-06-10 09:15</div> <div class="history-desc">对B区块追施氮肥，每些用量15kg</div> </div> </div> <div class="history-item"> <div class="history-icon"> <i class="fas fa-tools"></i> </div> <div class="history-content"> <div class="history-title">传感器维护</div> <div class="history-time">2023-06-05 10:30</div> <div class="history-desc">更换了C区块土壤湿度传感器电池</div> </div> </div> </div> </div> </div> </div> <!-- 页脚 -->
<div id="footer-container"></div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="js/login-config.js"></script>
<script src="js/components.js"></script>
<script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script> <script>
// 使用 common.js 加载导航栏

// 初始化地图
var map = L.map('map').setView([30.7741, 120.7551], 14);
L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
attribution: '© OpenStreetMap contributors'
}).addTo(map);
// 添加传感器标记
const sensors = [
{ id: 1, name: "土壤湿度传感器 #001", position: [30.7741, 120.7551], type: "moisture", status: "normal", value: "38%" },
{ id: 2, name: "土壤温度传感器 #002", position: [30.7841, 120.7651], type: "temperature", status: "normal", value: "22.8°C" },
{ id: 3, name: "pH值传感器 #003", position: [30.7641, 120.7451], type: "ph", status: "normal", value: "6.8" },
{ id: 4, name: "土壤养分传感器 #004", position: [30.7541, 120.7351], type: "nutrient", status: "warning", value: "氮含量偏低" },
{ id: 5, name: "土壤湿度传感器 #005", position: [30.7941, 120.7751], type: "moisture", status: "danger", value: "25%" }
];
sensors.forEach(sensor => {
const markerClass = sensor.status === 'normal' ? '' : sensor.status;
const marker = L.marker(sensor.position)
.bindPopup(`
<div class="text-center"> <h6>${sensor.name}</h6> <p class="mb-1">类型: ${sensor.type}</p> <p class="mb-1">状态: ${sensor.status}</p> <p class="mb-1">当前值: ${sensor.value}</p> <button class="btn btn-sm btn-success mt-2">查看详情</button> </div>
`)
.addTo(map);
});
// 初始化土壤数据图表
var ctx = document.getElementById('soilChart').getContext('2d');
var soilChart = new Chart(ctx, {
type: 'line',
data: {
labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
datasets: [
{
label: '土壤湿度 (%)',
data: [45, 42, 40, 38, 36, 35, 35],
borderColor: '#0dcaf0',
backgroundColor: 'rgba(13, 202, 240, 0.1)',
tension: 0.4,
fill: true
},
{
label: '土壤温度 (°C)',
data: [21, 21.5, 22, 22.5, 22.8, 22.5, 22.5],
borderColor: '#dc3545',
backgroundColor: 'rgba(220, 53, 69, 0.1)',
tension: 0.4,
fill: true
},
{
label: 'pH值',
data: [6.7, 6.7, 6.8, 6.8, 6.8, 6.8, 6.8],
borderColor: '#6c757d',
backgroundColor: 'rgba(108, 117, 125, 0.1)',
tension: 0.4,
fill: true
},
{
label: '氮含量 (%)',
data: [0.18, 0.17, 0.16, 0.16, 0.15, 0.15, 0.15],
borderColor: '#28a745',
backgroundColor: 'rgba(40, 167, 69, 0.1)',
tension: 0.4,
fill: true
}
]
},
options: {
responsive: true,
maintainAspectRatio: false,
plugins: {
legend: {
position: 'top',
},
tooltip: {
mode: 'index',
intersect: false
}
},
scales: {
y: {
beginAtZero: false
}
}
}
});
// 筛选按钮点击事件
document.querySelectorAll('.filter-btn').forEach(btn => {
btn.addEventListener('click', function() {
document.querySelectorAll('.filter-btn').forEach(b => {
b.classList.remove('active');
});
this.classList.add('active');
});
});
// 立即执行按钮点击事件
document.querySelector('.recommendation-card .btn-success').addEventListener('click', function() {
alert('已发送灵溪指令，电磁阀将在几秒后开启！');
});
</script>