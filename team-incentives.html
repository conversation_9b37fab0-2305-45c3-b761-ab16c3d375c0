<!DOCTYPE html> <html lang="zh-CN"> <head> <meta charset="UTF-8"> <meta name="viewport" content="width=device-width, initial-scale=1.0"> <title>区域团队激励 - 智慧农业平台</title> <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet"> <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet"> <link rel="stylesheet" href="css/common.css"> <link rel="stylesheet" href="css/navbar.css"> <style>
:root {
--primary-color: #28a745;
--secondary-color: #6c757d;
--light-bg: #f8f9fa;
--card-shadow: 0 4px 6px rgba(0,0,0,0.1);
--transition: all 0.3s ease;
--distribution-color: #8e44ad;
--level1-color: #3498db;
--level2-color: #e67e22;
--team-color: #9b59b6;
--gold-color: #f1c40f;
--silver-color: #bdc3c7;
--bronze-color: #e67e22;
}
body {
background-color: var(--light-bg);
font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}
.team-header {
background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80');
background-size: cover;
background-position: center;
height: 200px;
color: white;
display: flex;
align-items: center;
margin-bottom: 30px;
border-radius: 10px;
}
.team-card {
background: white;
padding: 20px;
border-radius: 10px;
box-shadow: var(--card-shadow);
margin-bottom: 30px;
min-height: auto;
}
.stat-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 20px;
transition: var(--transition);
}
.stat-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.stat-icon {
font-size: 2rem;
color: var(--team-color);
margin-bottom: 10px;
}
.stat-value {
font-size: 1.5rem;
font-weight: bold;
color: var(--team-color);
margin-bottom: 5px;
}
.stat-label {
font-size: 0.9rem;
color: var(--secondary-color);
}
.filter-btn {
padding: 5px 15px;
border-radius: 20px;
background-color: white;
border: 1px solid #ddd;
margin-right: 10px;
margin-bottom: 10px;
cursor: pointer;
transition: var(--transition);
}
.filter-btn.active {
background-color: var(--primary-color);
color: white;
border-color: var(--primary-color);
}
.filter-btn:hover:not(.active) {
background-color: #f0f0f0;
}
.incentive-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 15px;
transition: var(--transition);
min-height: auto;
}
.incentive-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.incentive-header {
display: flex;
align-items: center;
margin-bottom: 15px;
}
.incentive-icon {
width: 50px;
height: 50px;
border-radius: 10px;
background-color: var(--team-color);
color: white;
display: flex;
align-items: center;
justify-content: center;
font-size: 1.5rem;
margin-right: 15px;
flex-shrink: 0;
}
.incentive-title {
font-weight: bold;
font-size: 1.1rem;
margin-bottom: 5px;
}
.incentive-subtitle {
font-size: 0.9rem;
color: var(--secondary-color);
}
.incentive-body {
margin-bottom: 15px;
}
.incentive-description {
color: var(--secondary-color);
margin-bottom: 15px;
}
.incentive-condition {
background-color: white;
padding: 10px;
border-radius: 5px;
margin-bottom: 10px;
}
.incentive-condition-title {
font-weight: bold;
margin-bottom: 5px;
display: flex;
align-items: center;
}
.incentive-condition-icon {
color: var(--team-color);
margin-right: 5px;
}
.incentive-condition-text {
font-size: 0.9rem;
}
.incentive-footer {
text-align: center;
}
.team-item {
display: flex;
align-items: center;
padding: 15px;
background-color: var(--light-bg);
border-radius: 10px;
margin-bottom: 15px;
transition: var(--transition);
}
.team-item:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.team-number {
width: 40px;
height: 40px;
border-radius: 50%;
display: flex;
align-items: center;
justify-content: center;
margin-right: 15px;
font-weight: bold;
color: white;
flex-shrink: 0;
}
.team-rank-1 {
background-color: var(--gold-color);
}
.team-rank-2 {
background-color: var(--silver-color);
}
.team-rank-3 {
background-color: var(--bronze-color);
}
.team-rank-other {
background-color: var(--secondary-color);
}
.team-avatar {
width: 50px;
height: 50px;
border-radius: 50%;
object-fit: cover;
margin-right: 15px;
}
.team-info {
flex: 1;
}
.team-name {
font-weight: bold;
margin-bottom: 5px;
}
.team-role {
font-size: 0.9rem;
color: var(--secondary-color);
}
.team-stats {
display: flex;
gap: 15px;
font-size: 0.9rem;
margin-top: 5px;
}
.team-stat {
display: flex;
align-items: center;
}
.team-stat-icon {
color: var(--team-color);
margin-right: 5px;
}
.team-value {
display: flex;
flex-direction: column;
align-items: flex-end;
margin-left: 15px;
}
.team-score {
font-weight: bold;
font-size: 1.2rem;
color: var(--team-color);
}
.team-label {
font-size: 0.8rem;
color: var(--secondary-color);
}
.progress-container {
height: 8px;
background-color: #e9ecef;
border-radius: 4px;
margin-bottom: 5px;
overflow: hidden;
}
.progress-bar {
min-height: auto;
border-radius: 4px;
}
.progress-team {
background-color: var(--team-color);
}
.progress-text {
display: flex;
justify-content: space-between;
font-size: 0.8rem;
color: var(--secondary-color);
}
.chart-container {
position: relative;
height: 300px;
width: 100%;
margin-bottom: 20px;
}
.member-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 15px;
transition: var(--transition);
display: flex;
align-items: center;
}
.member-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.member-avatar {
width: 60px;
height: 60px;
border-radius: 50%;
object-fit: cover;
margin-right: 15px;
}
.member-info {
flex: 1;
margin-right: 15px;
}
.member-name {
font-weight: bold;
margin-bottom: 5px;
display: flex;
align-items: center;
}
.member-badge {
display: inline-block;
padding: 2px 8px;
border-radius: 15px;
font-size: 0.7rem;
margin-left: 10px;
}
.badge-level1 {
background-color: rgba(52, 152, 219, 0.1);
color: var(--level1-color);
}
.badge-level2 {
background-color: rgba(230, 126, 34, 0.1);
color: var(--level2-color);
}
.member-role {
font-size: 0.9rem;
color: var(--secondary-color);
margin-bottom: 5px;
}
.member-stats {
display: flex;
gap: 15px;
font-size: 0.9rem;
}
.member-stat {
display: flex;
align-items: center;
}
.member-stat-icon {
color: var(--team-color);
margin-right: 5px;
}
.member-actions {
display: flex;
gap: 10px;
}
.status-badge {
display: inline-block;
padding: 3px 8px;
border-radius: 15px;
font-size: 0.8rem;
}
.status-active {
background-color: rgba(40, 167, 69, 0.1);
color: var(--primary-color);
}
.status-inactive {
background-color: rgba(108, 117, 125, 0.1);
color: var(--secondary-color);
}
.status-pending {
background-color: rgba(255, 193, 7, 0.1);
color: #ffc107;
}
.faq-item {
margin-bottom: 15px;
}
.faq-question {
font-weight: bold;
margin-bottom: 5px;
cursor: pointer;
display: flex;
justify-content: space-between;
align-items: center;
}
.faq-answer {
color: var(--secondary-color);
display: none;
padding: 10px 0;
}
.faq-answer.active {
display: block;
}
.nav-tabs .nav-link {
color: var(--secondary-color);
}
.nav-tabs .nav-link.active {
color: var(--team-color);
font-weight: bold;
}
.tab-content {
padding: 20px 0;
}
.map-container {
height: 400px;
width: 100%;
border-radius: 10px;
overflow: hidden;
margin-bottom: 20px;
}
</style> </head> <body> <!-- 导航栏 -->
<div id="navbar-container"></div>
<!-- 主要内容 --> <div class="container py-5"> <div class="team-header"> <div class="container"> <h1>区域团队激励</h1> <p class="lead">基于区域团队的销售业绩和发展情况，提供多层次的激励机制，促进团队协作和区域市场拓展</p> </div> </div> <!-- 顶部统计卡片 --> <div class="row mb-4"> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-map-marked-alt stat-icon"></i> <div class="stat-value">12</div> <div class="stat-label">区域团队数</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-users stat-icon"></i> <div class="stat-value">256</div> <div class="stat-label">团队成员数</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-award stat-icon"></i> <div class="stat-value">68,500</div> <div class="stat-label">本月激励金额</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-chart-line stat-icon"></i> <div class="stat-value">+32.5%</div> <div class="stat-label">团队业绩增长率</div> </div> </div> </div> <!-- 主要内容区域 --> <div class="row"> <!-- 左侧内容 --> <div class="col-md-8"> <!-- 区域团队激励机制 --> <div class="team-card"> <h4 class="mb-4">区域团队激励机制</h4> <div class="row"> <div class="col-md-6 mb-4"> <div class="incentive-card"> <div class="incentive-header"> <div class="incentive-icon"> <i class="fas fa-trophy"></i> </div> <div> <div class="incentive-title">区域销售冠军奖</div> <div class="incentive-subtitle">基于区域团队销售业绩</div> </div> </div> <div class="incentive-body"> <div class="incentive-description">
每月评选销售业绩最佳的区域团队，提供现金奖励和荣誉称号，激励团队提高销售业绩。
</div> <div class="incentive-condition"> <div class="incentive-condition-title"> <i class="fas fa-medal incentive-condition-icon" style="color: var(--gold-color);"></i> <span>区域销售冠军</span> </div> <div class="incentive-condition-text">奖励金额：20,000元 + 团队荣誉证书</div> </div> <div class="incentive-condition"> <div class="incentive-condition-title"> <i class="fas fa-medal incentive-condition-icon" style="color: var(--silver-color);"></i> <span>区域销售亚军</span> </div> <div class="incentive-condition-text">奖励金额：10,000元 + 团队荣誉证书</div> </div> <div class="incentive-condition"> <div class="incentive-condition-title"> <i class="fas fa-medal incentive-condition-icon" style="color: var(--bronze-color);"></i> <span>区域销售季军</span> </div> <div class="incentive-condition-text">奖励金额：5,000元 + 团队荣誉证书</div> </div> </div> <div class="incentive-footer"> <button class="btn btn-outline-success">查看历史获奖团队</button> </div> </div> </div> <div class="col-md-6 mb-4"> <div class="incentive-card"> <div class="incentive-header"> <div class="incentive-icon"> <i class="fas fa-chart-line"></i> </div> <div> <div class="incentive-title">区域增长之星奖</div> <div class="incentive-subtitle">基于区域团队增长率</div> </div> </div> <div class="incentive-body"> <div class="incentive-description">
每月评选销售增长率最高的区域团队，提供现金奖励和增长之星称号，激励团队持续增长。
</div> <div class="incentive-condition"> <div class="incentive-condition-title"> <i class="fas fa-star incentive-condition-icon" style="color: var(--gold-color);"></i> <span>金牌增长之星</span> </div> <div class="incentive-condition-text">增长率超过50%，奖励金额：15,000元</div> </div> <div class="incentive-condition"> <div class="incentive-condition-title"> <i class="fas fa-star incentive-condition-icon" style="color: var(--silver-color);"></i> <span>银牌增长之星</span> </div> <div class="incentive-condition-text">增长率超过30%，奖励金额：8,000元</div> </div> <div class="incentive-condition"> <div class="incentive-condition-title"> <i class="fas fa-star incentive-condition-icon" style="color: var(--bronze-color);"></i> <span>铜牌增长之星</span> </div> <div class="incentive-condition-text">增长率超过20%，奖励金额：3,000元</div> </div> </div> <div class="incentive-footer"> <button class="btn btn-outline-success">查看增长排行榜</button> </div> </div> </div> <div class="col-md-6 mb-4"> <div class="incentive-card"> <div class="incentive-header"> <div class="incentive-icon"> <i class="fas fa-users"></i> </div> <div> <div class="incentive-title">团队发展奖</div> <div class="incentive-subtitle">基于团队规模和成员发展</div> </div> </div> <div class="incentive-body"> <div class="incentive-description">
根据区域团队的规模扩大和成员发展情况，提供团队发展奖励，鼓励团队扩大和成员成长。
</div> <div class="incentive-condition"> <div class="incentive-condition-title"> <i class="fas fa-user-plus incentive-condition-icon"></i> <span>团队规模奖</span> </div> <div class="incentive-condition-text">每发展10名新成员，奖励团队负责人2,000元</div> </div> <div class="incentive-condition"> <div class="incentive-condition-title"> <i class="fas fa-user-graduate incentive-condition-icon"></i> <span>成员晋升奖</span> </div> <div class="incentive-condition-text">每有5名二级分销商晋升为一级，奖励团队负责人3,000元</div> </div> <div class="incentive-condition"> <div class="incentive-condition-title"> <i class="fas fa-user-tie incentive-condition-icon"></i> <span>团队结构奖</span> </div> <div class="incentive-condition-text">团队一级与二级分销商比例达到1:5，奖励团队5,000元</div> </div> </div> <div class="incentive-footer"> <button class="btn btn-outline-success">查看团队发展数据</button> </div> </div> </div> <div class="col-md-6 mb-4"> <div class="incentive-card"> <div class="incentive-header"> <div class="incentive-icon"> <i class="fas fa-bullseye"></i> </div> <div> <div class="incentive-title">区域目标激励</div> <div class="incentive-subtitle">基于区域目标完成情况</div> </div> </div> <div class="incentive-body"> <div class="incentive-description">
根据区域团队的季度和年度目标完成情况，提供阶梯式的目标激励，鼓励团队达成和超越目标。
</div> <div class="incentive-condition"> <div class="incentive-condition-title"> <i class="fas fa-check-circle incentive-condition-icon"></i> <span>目标达成奖</span> </div> <div class="incentive-condition-text">完成季度目标100%，奖励团队总销售额的5%</div> </div> <div class="incentive-condition"> <div class="incentive-condition-title"> <i class="fas fa-arrow-circle-up incentive-condition-icon"></i> <span>目标超越奖</span> </div> <div class="incentive-condition-text">超越季度目标，每超越10%增加1%的奖励，最高不超10%</div> </div> <div class="incentive-condition"> <div class="incentive-condition-title"> <i class="fas fa-calendar-check incentive-condition-icon"></i> <span>年度目标奖</span> </div> <div class="incentive-condition-text">完成年度目标，团队成员可获得旅游奖励或等值现金</div> </div> </div> <div class="incentive-footer"> <button class="btn btn-outline-success">查看目标完成情况</button> </div> </div> </div> </div> </div> <!-- 区域团队排行榜 --> <div class="team-card"> <div class="d-flex justify-content-between align-items-center mb-4"> <h4>区域团队排行榜</h4> <div> <button class="filter-btn">周</button> <button class="filter-btn active">月</button> <button class="filter-btn">季</button> <button class="filter-btn">年</button> </div> </div> <ul class="nav nav-tabs" id="teamTabs" role="tablist"> <li class="nav-item" role="presentation"> <button class="nav-link active" id="sales-tab" data-bs-toggle="tab" data-bs-target="#sales" type="button" role="tab" aria-controls="sales" aria-selected="true">销售业绩排行</button> </li> <li class="nav-item" role="presentation"> <button class="nav-link" id="growth-tab" data-bs-toggle="tab" data-bs-target="#growth" type="button" role="tab" aria-controls="growth" aria-selected="false">增长率排行</button> </li> <li class="nav-item" role="presentation"> <button class="nav-link" id="development-tab" data-bs-toggle="tab" data-bs-target="#development" type="button" role="tab" aria-controls="development" aria-selected="false">团队发展排行</button> </li> </ul> <div class="tab-content" id="teamTabsContent"> <!-- 销售业绩排行选项卡 --> <div class="tab-pane fade show active" id="sales" role="tabpanel" aria-labelledby="sales-tab"> <div class="tab-content pt-3"> <div class="team-item"> <div class="team-number team-rank-1">1</div> <img src="images/team1.jpg" alt="团队头像" class="team-avatar"> <div class="team-info"> <div class="team-name">浙江区域团队</div> <div class="team-role">团队负责人：王地主 | 成员数：32人</div> <div class="team-stats"> <div class="team-stat"> <i class="fas fa-user-tie team-stat-icon"></i> <span>一级分销商：8人</span> </div> <div class="team-stat"> <i class="fas fa-user-friends team-stat-icon"></i> <span>二级分销商：24人</span> </div> </div> </div> <div class="team-value"> <div class="team-score">256,800元</div> <div class="team-label">销售业绩</div> </div> </div> <div class="team-item"> <div class="team-number team-rank-2">2</div> <img src="images/team2.jpg" alt="团队头像" class="team-avatar"> <div class="team-info"> <div class="team-name">江苏区域团队</div> <div class="team-role">团队负责人：李地主 | 成员数：28人</div> <div class="team-stats"> <div class="team-stat"> <i class="fas fa-user-tie team-stat-icon"></i> <span>一级分销商：6人</span> </div> <div class="team-stat"> <i class="fas fa-user-friends team-stat-icon"></i> <span>二级分销商：22人</span> </div> </div> </div> <div class="team-value"> <div class="team-score">198,500元</div> <div class="team-label">销售业绩</div> </div> </div> <div class="team-item"> <div class="team-number team-rank-3">3</div> <img src="images/team3.jpg" alt="团队头像" class="team-avatar"> <div class="team-info"> <div class="team-name">安徽区域团队</div> <div class="team-role">团队负责人：张地主 | 成员数：25人</div> <div class="team-stats"> <div class="team-stat"> <i class="fas fa-user-tie team-stat-icon"></i> <span>一级分销商：5人</span> </div> <div class="team-stat"> <i class="fas fa-user-friends team-stat-icon"></i> <span>二级分销商：20人</span> </div> </div> </div> <div class="team-value"> <div class="team-score">165,300元</div> <div class="team-label">销售业绩</div> </div> </div> <div class="team-item"> <div class="team-number team-rank-other">4</div> <img src="images/team4.jpg" alt="团队头像" class="team-avatar"> <div class="team-info"> <div class="team-name">山东区域团队</div> <div class="team-role">团队负责人：赵地主 | 成员数：20人</div> <div class="team-stats"> <div class="team-stat"> <i class="fas fa-user-tie team-stat-icon"></i> <span>一级分销商：4人</span> </div> <div class="team-stat"> <i class="fas fa-user-friends team-stat-icon"></i> <span>二级分销商：16人</span> </div> </div> </div> <div class="team-value"> <div class="team-score">128,600元</div> <div class="team-label">销售业绩</div> </div> </div> <div class="team-item"> <div class="team-number team-rank-other">5</div> <img src="images/team5.jpg" alt="团队头像" class="team-avatar"> <div class="team-info"> <div class="team-name">河南区域团队</div> <div class="team-role">团队负责人：孙地主 | 成员数：18人</div> <div class="team-stats"> <div class="team-stat"> <i class="fas fa-user-tie team-stat-icon"></i> <span>一级分销商：3人</span> </div> <div class="team-stat"> <i class="fas fa-user-friends team-stat-icon"></i> <span>二级分销商：15人</span> </div> </div> </div> <div class="team-value"> <div class="team-score">105,800元</div> <div class="team-label">销售业绩</div> </div> </div> <div class="text-center mt-3"> <button class="btn btn-outline-primary">查看完整排行榜</button> </div> </div> </div> <!-- 增长率排行选项卡 --> <div class="tab-pane fade" id="growth" role="tabpanel" aria-labelledby="growth-tab"> <div class="tab-content pt-3"> <div class="team-item"> <div class="team-number team-rank-1">1</div> <img src="images/team3.jpg" alt="团队头像" class="team-avatar"> <div class="team-info"> <div class="team-name">安徽区域团队</div> <div class="team-role">团队负责人：张地主 | 成员数：25人</div> <div class="team-stats"> <div class="team-stat"> <i class="fas fa-shopping-cart team-stat-icon"></i> <span>上月销售：98,500元</span> </div> <div class="team-stat"> <i class="fas fa-shopping-cart team-stat-icon"></i> <span>本月销售：165,300元</span> </div> </div> </div> <div class="team-value"> <div class="team-score">+67.8%</div> <div class="team-label">增长率</div> </div> </div> <div class="team-item"> <div class="team-number team-rank-2">2</div> <img src="images/team5.jpg" alt="团队头像" class="team-avatar"> <div class="team-info"> <div class="team-name">河南区域团队</div> <div class="team-role">团队负责人：孙地主 | 成员数：18人</div> <div class="team-stats"> <div class="team-stat"> <i class="fas fa-shopping-cart team-stat-icon"></i> <span>上月销售：68,200元</span> </div> <div class="team-stat"> <i class="fas fa-shopping-cart team-stat-icon"></i> <span>本月销售：105,800元</span> </div> </div> </div> <div class="team-value"> <div class="team-score">+55.1%</div> <div class="team-label">增长率</div> </div> </div> <div class="team-item"> <div class="team-number team-rank-3">3</div> <img src="images/team1.jpg" alt="团队头像" class="team-avatar"> <div class="team-info"> <div class="team-name">浙江区域团队</div> <div class="team-role">团队负责人：王地主 | 成员数：32人</div> <div class="team-stats"> <div class="team-stat"> <i class="fas fa-shopping-cart team-stat-icon"></i> <span>上月销售：198,600元</span> </div> <div class="team-stat"> <i class="fas fa-shopping-cart team-stat-icon"></i> <span>本月销售：256,800元</span> </div> </div> </div> <div class="team-value"> <div class="team-score">+29.3%</div> <div class="team-label">增长率</div> </div> </div> <div class="text-center mt-3"> <button class="btn btn-outline-primary">查看完整排行榜</button> </div> </div> </div> <!-- 团队发展排行选项卡 --> <div class="tab-pane fade" id="development" role="tabpanel" aria-labelledby="development-tab"> <div class="tab-content pt-3"> <div class="team-item"> <div class="team-number team-rank-1">1</div> <img src="images/team1.jpg" alt="团队头像" class="team-avatar"> <div class="team-info"> <div class="team-name">浙江区域团队</div> <div class="team-role">团队负责人：王地主 | 成员数：32人</div> <div class="team-stats"> <div class="team-stat"> <i class="fas fa-user-plus team-stat-icon"></i> <span>新增成员：8人</span> </div> <div class="team-stat"> <i class="fas fa-user-graduate team-stat-icon"></i> <span>成员晋升：3人</span> </div> </div> </div> <div class="team-value"> <div class="team-score">85分</div> <div class="team-label">发展分数</div> </div> </div> <div class="team-item"> <div class="team-number team-rank-2">2</div> <img src="images/team2.jpg" alt="团队头像" class="team-avatar"> <div class="team-info"> <div class="team-name">江苏区域团队</div> <div class="team-role">团队负责人：李地主 | 成员数：28人</div> <div class="team-stats"> <div class="team-stat"> <i class="fas fa-user-plus team-stat-icon"></i> <span>新增成员：6人</span> </div> <div class="team-stat"> <i class="fas fa-user-graduate team-stat-icon"></i> <span>成员晋升：2人</span> </div> </div> </div> <div class="team-value"> <div class="team-score">68分</div> <div class="team-label">发展分数</div> </div> </div> <div class="team-item"> <div class="team-number team-rank-3">3</div> <img src="images/team3.jpg" alt="团队头像" class="team-avatar"> <div class="team-info"> <div class="team-name">安徽区域团队</div> <div class="team-role">团队负责人：张地主 | 成员数：25人</div> <div class="team-stats"> <div class="team-stat"> <i class="fas fa-user-plus team-stat-icon"></i> <span>新增成员：5人</span> </div> <div class="team-stat"> <i class="fas fa-user-graduate team-stat-icon"></i> <span>成员晋升：2人</span> </div> </div> </div> <div class="team-value"> <div class="team-score">60分</div> <div class="team-label">发展分数</div> </div> </div> <div class="text-center mt-3"> <button class="btn btn-outline-primary">查看完整排行榜</button> </div> </div> </div> </div> </div> </div> <!-- 右侧内容 --> <div class="col-md-4"> <!-- 区域分布地图 --> <div class="team-card"> <h4 class="mb-4">区域分布地图</h4> <div class="map-container"> <img src="images/china-map.jpg" alt="区域分布地图" style="width: 100%; height: 100%; object-fit: cover;"> </div> <div class="d-flex justify-content-between mt-3"> <div> <div class="stat-value">12</div> <div class="stat-label">区域团队</div> </div> <div> <div class="stat-value">28</div> <div class="stat-label">覆盖城市</div> </div> <div> <div class="stat-value">68%</div> <div class="stat-label">市场覆盖率</div> </div> </div> </div> <!-- 团队成员列表 --> <div class="team-card"> <div class="d-flex justify-content-between align-items-center mb-4"> <h4>浙江区域团队成员</h4> <div class="input-group" style="width: 200px;"> <input type="text" class="form-control form-control-sm" placeholder="搜索成员"> <button class="btn btn-sm btn-outline-secondary"><i class="fas fa-search"></i></button> </div> </div> <div class="member-card"> <img src="images/avatar1.jpg" alt="分销商头像" class="member-avatar"> <div class="member-info"> <div class="member-name">
王地主 <span class="member-badge badge-level1">一级</span> </div> <div class="member-role">团队负责人 | 加入时间：2023-01-15</div> <div class="member-stats"> <div class="member-stat"> <i class="fas fa-user-friends member-stat-icon"></i> <span>32个村民</span> </div> <div class="member-stat"> <i class="fas fa-shopping-cart member-stat-icon"></i> <span>128笔订单</span> </div> </div> </div> <div class="member-actions"> <button class="btn btn-sm btn-outline-primary"><i class="fas fa-eye"></i></button> </div> </div> <div class="member-card"> <img src="images/avatar6.jpg" alt="分销商头像" class="member-avatar"> <div class="member-info"> <div class="member-name">
张村民 <span class="member-badge badge-level2">二级</span> </div> <div class="member-role">团队成员 | 加入时间：2023-03-10</div> <div class="member-stats"> <div class="member-stat"> <i class="fas fa-shopping-cart member-stat-icon"></i> <span>32笔订单</span> </div> <div class="member-stat"> <i class="fas fa-yen-sign member-stat-icon"></i> <span>6,450元</span> </div> </div> </div> <div class="member-actions"> <button class="btn btn-sm btn-outline-primary"><i class="fas fa-eye"></i></button> </div> </div> <div class="member-card"> <img src="images/avatar9.jpg" alt="分销商头像" class="member-avatar"> <div class="member-info"> <div class="member-name">
王村民 <span class="member-badge badge-level2">二级</span> </div> <div class="member-role">团队成员 | 加入时间：2023-03-12</div> <div class="member-stats"> <div class="member-stat"> <i class="fas fa-shopping-cart member-stat-icon"></i> <span>28笔订单</span> </div> <div class="member-stat"> <i class="fas fa-yen-sign member-stat-icon"></i> <span>5,680元</span> </div> </div> </div> <div class="member-actions"> <button class="btn btn-sm btn-outline-primary"><i class="fas fa-eye"></i></button> </div> </div> <div class="text-center mt-3"> <button class="btn btn-outline-primary">查看更多成员</button> </div> </div> <!-- 团队目标进度 --> <div class="team-card"> <h4 class="mb-4">团队目标进度</h4> <div class="mb-4"> <div class="d-flex justify-content-between mb-2"> <div>月度销售目标</div> <div>256,800元 / 300,000元</div> </div> <div class="progress-container"> <div class="progress-bar progress-team" style="width: 85.6%;"></div> </div> <div class="progress-text"> <span>完成率：85.6%</span> <span>剩余时间：10天</span> </div> </div> <div class="mb-4"> <div class="d-flex justify-content-between mb-2"> <div>团队发展目标</div> <div>32人 / 40人</div> </div> <div class="progress-container"> <div class="progress-bar progress-team" style="width: 80%;"></div> </div> <div class="progress-text"> <span>完成率：80%</span> <span>剩余时间：10天</span> </div> </div> <div class="mb-4"> <div class="d-flex justify-content-between mb-2"> <div>季度销售目标</div> <div>680,500元 / 900,000元</div> </div> <div class="progress-container"> <div class="progress-bar progress-team" style="width: 75.6%;"></div> </div> <div class="progress-text"> <span>完成率：75.6%</span> <span>剩余时间：40天</span> </div> </div> <div class="text-center mt-3"> <button class="btn btn-outline-success">查看详细目标</button> </div> </div> </div> </div> </div> <!-- 页脚 -->
<div id="footer-container"></div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="js/login-config.js"></script>
<script src="js/components.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script> <script>
// 使用 common.js 加载导航栏

// 筛选按钮点击事件
document.querySelectorAll('.filter-btn').forEach(btn => {
btn.addEventListener('click', function() {
const filterBtns = this.parentElement.querySelectorAll('.filter-btn');
filterBtns.forEach(b => {
b.classList.remove('active');
});
this.classList.add('active');
});
});
// 团队成员卡片点击事件
document.querySelectorAll('.member-card').forEach(card => {
card.addEventListener('click', function() {
const memberName = this.querySelector('.member-name').textContent.trim().split(' ')[0];
alert(`查看 ${memberName} 的详细信息`);
});
});
// 团队成员操作按钮点击事件
document.querySelectorAll('.member-actions .btn').forEach(btn => {
btn.addEventListener('click', function(e) {
e.stopPropagation();
const memberCard = this.closest('.member-card');
const memberName = memberCard.querySelector('.member-name').textContent.trim().split(' ')[0];
alert(`查看 ${memberName} 的详细信息`);
});
});
// 激励卡片按钮点击事件
document.querySelectorAll('.incentive-card .btn').forEach(btn => {
btn.addEventListener('click', function() {
const incentiveTitle = this.closest('.incentive-card').querySelector('.incentive-title').textContent;
alert(`查看 ${incentiveTitle} 的详细信息`);
});
});
// 团队排行榜按钮点击事件
document.querySelectorAll('.team-item').forEach(item => {
item.addEventListener('click', function() {
const teamName = this.querySelector('.team-name').textContent;
alert(`查看 ${teamName} 的详细信息`);
});
});
// 查看完整排行榜按钮点击事件
document.querySelectorAll('.btn-outline-primary').forEach(btn => {
btn.addEventListener('click', function() {
alert('正在加载更多数据，请稍后...');
});
});
// 查看详细目标按钮点击事件
document.querySelector('.btn-outline-success').addEventListener('click', function() {
alert('正在加载目标详情，请稍后...');
});

// 确保导航栏在页面加载完成后可见

// 页面加载完成后确保导航栏可见
window.onload = function() {
    // 确保导航栏可见
    const navbarContainer = document.getElementById('navbar-container');
    if (navbarContainer) {
        // 强制设置为可见
        navbarContainer.style.display = 'block';
        
        // 确保导航栏内容已加载
        if (!navbarContainer.innerHTML || navbarContainer.innerHTML.trim() === '') {
            // 如果导航栏内容为空，尝试重新加载
            if (typeof navbarHTML !== 'undefined') {
                navbarContainer.innerHTML = navbarHTML;
                
                // 重新应用登录状态和模块可见性
                if (typeof loadLoginState === 'function') loadLoginState();
                if (typeof updateNavbarDisplay === 'function') updateNavbarDisplay();
                if (typeof setActiveNavItem === 'function') setActiveNavItem();
                if (typeof setupLoginEvents === 'function') setupLoginEvents();
            }
        }
    }
};
</script> </body> </html>