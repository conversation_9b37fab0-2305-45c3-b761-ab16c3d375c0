<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户角色权限测试 - 智慧农业平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="css/navbar.css">
    
    <!-- 添加内联样式确保导航栏始终可见 -->
    <style>
    #navbar-container {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
        position: relative !important;
        z-index: 1000 !important;
    }
    
    .test-card {
        background: white;
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }
    
    .role-badge {
        display: inline-block;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.8em;
        font-weight: bold;
        margin-left: 8px;
    }
    
    .role-user { background-color: #e3f2fd; color: #1976d2; }
    .role-admin { background-color: #f3e5f5; color: #7b1fa2; }
    .role-landlord { background-color: #e8f5e8; color: #388e3c; }
    .role-farmer { background-color: #fff3e0; color: #f57c00; }
    </style>
</head>
<body>
    <!-- 导航栏 - 使用内联方式直接加载，避免异步加载问题 -->
    <div id="navbar-container">
    <!-- 导航栏内容将在页面加载时由JavaScript填充，但即使JavaScript失败，这个容器也会保持可见 -->
    </div>

    <!-- 主要内容 -->
    <div class="container py-5">
        <div class="row">
            <div class="col-12">
                <h2 class="mb-4">用户角色权限测试页面</h2>
                <p class="text-muted mb-4">此页面用于测试不同用户角色的权限控制功能。请使用不同的账号登录，观察导航栏模块的变化。</p>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="test-card">
                    <h5>测试账号列表</h5>
                    <div class="mb-3">
                        <strong>普通用户</strong><span class="role-badge role-user">USER</span><br>
                        <small>用户名: user | 密码: 123456</small><br>
                        <small class="text-muted">权限: 可访问模块1（基地土地招租与订单种植）、模块2（农产品品牌化运营）</small>
                    </div>
                    
                    <div class="mb-3">
                        <strong>管理员</strong><span class="role-badge role-admin">ADMIN</span><br>
                        <small>用户名: admin | 密码: 123456</small><br>
                        <small class="text-muted">权限: 可访问所有模块（模块1-6）</small>
                    </div>
                    
                    <div class="mb-3">
                        <strong>地主</strong><span class="role-badge role-landlord">LANDLORD</span><br>
                        <small>用户名: landlord | 密码: 123456</small><br>
                        <small class="text-muted">权限: 可访问模块3（全过程监控系统）</small>
                    </div>
                    
                    <div class="mb-3">
                        <strong>农民</strong><span class="role-badge role-farmer">FARMER</span><br>
                        <small>用户名: farmer | 密码: 123456</small><br>
                        <small class="text-muted">权限: 可访问模块4（二级分销体系）</small>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="test-card">
                    <h5>当前登录状态</h5>
                    <div id="currentStatus">
                        <p class="text-muted">未登录</p>
                        <small>当前可见模块: 模块1、模块2</small>
                    </div>
                    
                    <hr>
                    
                    <h6>测试步骤</h6>
                    <ol class="small">
                        <li>点击右上角的"登录"按钮</li>
                        <li>使用上述任一测试账号登录</li>
                        <li>观察导航栏中显示的模块变化</li>
                        <li>尝试访问不同模块下的页面</li>
                        <li>点击用户名退出登录，测试其他账号</li>
                    </ol>
                </div>
                
                <div class="test-card">
                    <h5>快速登录</h5>
                    <p class="small text-muted">点击下方按钮快速登录不同角色的账号</p>
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-primary btn-sm" onclick="quickLogin('user')">
                            <i class="fas fa-user me-1"></i>普通用户登录
                        </button>
                        <button class="btn btn-outline-success btn-sm" onclick="quickLogin('admin')">
                            <i class="fas fa-user-shield me-1"></i>管理员登录
                        </button>
                        <button class="btn btn-outline-warning btn-sm" onclick="quickLogin('landlord')">
                            <i class="fas fa-home me-1"></i>地主登录
                        </button>
                        <button class="btn btn-outline-info btn-sm" onclick="quickLogin('farmer')">
                            <i class="fas fa-seedling me-1"></i>农民登录
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <div id="footer-container"></div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/login-config.js"></script>
    <script src="js/components.js"></script>
    
    <script>
    // 立即执行函数，确保导航栏在页面加载过程中就开始处理
    (function() {
        console.log('测试页面脚本开始执行');
        
        // 确保导航栏可见
        function ensureNavbarVisible() {
            const navbarContainer = document.getElementById('navbar-container');
            if (navbarContainer) {
                navbarContainer.style.display = 'block';
                navbarContainer.style.visibility = 'visible';
                navbarContainer.style.opacity = '1';
                
                if (!navbarContainer.innerHTML || navbarContainer.innerHTML.trim() === '') {
                    if (typeof navbarHTML !== 'undefined') {
                        navbarContainer.innerHTML = navbarHTML;
                        
                        if (typeof loadLoginState === 'function') loadLoginState();
                        if (typeof updateNavbarDisplay === 'function') updateNavbarDisplay();
                        if (typeof setActiveNavItem === 'function') setActiveNavItem();
                        if (typeof setupLoginEvents === 'function') setupLoginEvents();
                    }
                }
            }
        }
        
        ensureNavbarVisible();
        
        document.addEventListener('DOMContentLoaded', function() {
            ensureNavbarVisible();
            updateCurrentStatus();
        });
        
        window.onload = function() {
            ensureNavbarVisible();
            updateCurrentStatus();
        };
    })();
    
    // 更新当前状态显示
    function updateCurrentStatus() {
        const statusDiv = document.getElementById('currentStatus');
        if (typeof isLoggedIn !== 'undefined' && isLoggedIn && typeof currentUser !== 'undefined' && currentUser) {
            const modules = loginConfig.getModulesForUser(currentUser);
            const moduleNames = {
                'module1': '模块1（基地土地招租与订单种植）',
                'module2': '模块2（农产品品牌化运营）',
                'module3': '模块3（全过程监控系统）',
                'module4': '模块4（二级分销体系）',
                'module5': '模块5（农资农机交易系统）',
                'module6': '模块6（运营支撑体系）'
            };
            
            statusDiv.innerHTML = `
                <p><strong>已登录:</strong> ${currentUser.displayName} <span class="role-badge role-${currentUser.role}">${currentUser.role.toUpperCase()}</span></p>
                <small><strong>可见模块:</strong><br>${modules.map(m => moduleNames[m]).join('<br>')}</small>
            `;
        } else {
            statusDiv.innerHTML = `
                <p class="text-muted">未登录</p>
                <small>当前可见模块: 模块1（基地土地招租与订单种植）、模块2（农产品品牌化运营）</small>
            `;
        }
    }
    
    // 快速登录功能
    function quickLogin(username) {
        const user = loginConfig.validateUser(username, '123456');
        if (user) {
            if (typeof saveLoginState === 'function') {
                saveLoginState(user);
                if (typeof updateNavbarDisplay === 'function') updateNavbarDisplay();
                if (typeof updateLoginButton === 'function') updateLoginButton();
                updateCurrentStatus();
                
                // 显示成功消息
                const alert = document.createElement('div');
                alert.className = 'alert alert-success alert-dismissible fade show position-fixed';
                alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999;';
                alert.innerHTML = `
                    <strong>登录成功！</strong> 已切换到 ${user.displayName} 账号
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;
                document.body.appendChild(alert);
                
                setTimeout(() => {
                    if (alert.parentNode) alert.parentNode.removeChild(alert);
                }, 3000);
            }
        }
    }
    
    // 监听登录状态变化
    setInterval(updateCurrentStatus, 1000);
    </script>
</body>
</html>
