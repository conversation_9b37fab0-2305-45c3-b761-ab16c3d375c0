import os
import re

# 获取当前目录下所有的HTML文件
html_files = [f for f in os.listdir('.') if f.endswith('.html')]

# 更新每个HTML文件
for html_file in html_files:
    print(f"Processing {html_file}...")
    
    # 读取文件内容
    with open(html_file, 'r', encoding='utf-8', errors='ignore') as f:
        content = f.read()
    
    # 检查是否已经引用了components.js
    if 'components.js' not in content:
        # 添加components.js引用
        bootstrap_script = '<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>'
        if bootstrap_script in content:
            new_content = content.replace(
                bootstrap_script, 
                bootstrap_script + '\n<script src="js/components.js"></script>'
            )
            content = new_content
        else:
            # 如果没有找到Bootstrap脚本，在</head>前添加
            head_end = '</head>'
            if head_end in content:
                new_content = content.replace(
                    head_end, 
                    '<script src="js/components.js"></script>\n' + head_end
                )
                content = new_content
    
    # 替换导航栏
    # 查找导航栏的开始和结束位置
    navbar_start = content.find('<!-- 导航栏 -->')
    if navbar_start != -1:
        # 查找导航栏的结束位置
        navbar_end = content.find('<!-- 主要内容 -->', navbar_start)
        if navbar_end == -1:
            navbar_end = content.find('<div class="container', navbar_start)
        
        if navbar_end != -1:
            # 替换导航栏
            old_navbar = content[navbar_start:navbar_end]
            new_navbar = '<!-- 导航栏 -->\n<div id="navbar-container"></div>\n'
            content = content.replace(old_navbar, new_navbar)
            print(f"  - Replaced navbar in {html_file}")
    
    # 替换页脚
    # 查找页脚的开始和结束位置
    footer_start = content.find('<!-- 页脚 -->')
    if footer_start != -1:
        # 查找页脚的结束位置
        footer_end = content.find('<script', footer_start)
        
        if footer_end != -1:
            # 替换页脚
            old_footer = content[footer_start:footer_end]
            new_footer = '<!-- 页脚 -->\n<div id="footer-container"></div>\n'
            content = content.replace(old_footer, new_footer)
            print(f"  - Replaced footer in {html_file}")
    
    # 删除旧的导航栏和页脚加载代码
    old_loader_code = re.search(r'<script>\s*// 加载导航栏和页脚.*?</script>', content, re.DOTALL)
    if old_loader_code:
        content = content.replace(old_loader_code.group(0), '')
        print(f"  - Removed old loader code in {html_file}")
    
    # 保存更新后的文件
    with open(html_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"Completed processing {html_file}")

print('All HTML files have been updated to use the inline component system.')
