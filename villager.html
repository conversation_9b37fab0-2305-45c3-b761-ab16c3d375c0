<!DOCTYPE html> <html lang="zh-CN"> <head> <meta charset="UTF-8"> <meta name="viewport" content="width=device-width, initial-scale=1.0"> <title>村民专区 - 智慧农业平台</title> <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet"> <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet"> <link rel="stylesheet" href="css/common.css"> <link rel="stylesheet" href="css/navbar.css"> <style>
.villager-header {
background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80');
background-size: cover;
background-position: center;
height: 300px;
color: white;
display: flex;
align-items: center;
justify-content: center;
text-align: center;
}
.feature-card {
transition: transform 0.3s;
margin-bottom: 20px;
box-shadow: 0 0 10px rgba(0,0,0,0.1);
}
.feature-card:hover {
transform: translateY(-5px);
}
.feature-icon {
font-size: 2.5rem;
color: var(--primary-color);
margin-bottom: 15px;
}
.benefit-item {
padding: 15px;
border-radius: 10px;
background: #f8f9fa;
margin-bottom: 15px;
}
.benefit-item i {
color: var(--primary-color);
margin-right: 10px;
}
.land-card {
border-radius: 10px;
overflow: hidden;
margin-bottom: 20px;
box-shadow: 0 0 10px rgba(0,0,0,0.1);
}
.land-card img {
height: 200px;
object-fit: cover;
}
.land-card .card-body {
padding: 20px;
}
.land-card .badge {
margin-right: 5px;
}
.navbar-brand {
display: flex;
align-items: center;
gap: 10px;
}
.brand-logo {
width: 40px;
height: 40px;
}
.brand-logo img {
width: 100%;
min-height: auto;
object-fit: contain;
}
</style> </head> <body> <!-- 导航栏容器 --> <div id="navbar-container"></div> <!-- 导航栏 -->
<div id="navbar-container"></div>
<!-- 主要内容 --> <div class="container py-5"> <!-- 特色功能 --> <div class="row mb-5"> <div class="col-md-4"> <div class="card feature-card"> <div class="card-body text-center"> <i class="fas fa-search feature-icon"></i> <h4>土地搜索</h4> <p>快速找到适合的土地资源</p> </div> </div> </div> <div class="col-md-4"> <div class="card feature-card"> <div class="card-body text-center"> <i class="fas fa-seedling feature-icon"></i> <h4>种植指导</h4> <p>专业的种植技术指导</p> </div> </div> </div> <div class="col-md-4"> <div class="card feature-card"> <div class="card-body text-center"> <i class="fas fa-hand-holding-usd feature-icon"></i> <h4>收益保障</h4> <p>稳定的收益回报</p> </div> </div> </div> </div> <!-- 可用土地 --> <div class="row mb-5"> <div class="col-12"> <h3 class="mb-4">可用土地</h3> <div class="row"> <div class="col-md-4"> <div class="land-card"> <img src="https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80" class="card-img-top" alt="土地1"> <div class="card-body"> <h5 class="card-title">优质农田</h5> <p class="card-text">面积：50亩</p> <p class="card-text">位置：XX村</p> <p class="card-text">土壤类型：黑土</p> <div class="mb-3"> <span class="badge bg-success">可种植</span> <span class="badge bg-info">水源充足</span> </div> <a href="#" class="btn btn-primary">查看详情</a> </div> </div> </div> <div class="col-md-4"> <div class="land-card"> <img src="https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80" class="card-img-top" alt="土地2"> <div class="card-body"> <h5 class="card-title">果园用地</h5> <p class="card-text">面积：30亩</p> <p class="card-text">位置：XX村</p> <p class="card-text">土壤类型：红土</p> <div class="mb-3"> <span class="badge bg-success">可种植</span> <span class="badge bg-warning">需灌溉</span> </div> <a href="#" class="btn btn-primary">查看详情</a> </div> </div> </div> <div class="col-md-4"> <div class="land-card"> <img src="https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80" class="card-img-top" alt="土地3"> <div class="card-body"> <h5 class="card-title">蔬菜基地</h5> <p class="card-text">面积：20亩</p> <p class="card-text">位置：XX村</p> <p class="card-text">土壤类型：沙土</p> <div class="mb-3"> <span class="badge bg-success">可种植</span> <span class="badge bg-info">设施完善</span> </div> <a href="#" class="btn btn-primary">查看详情</a> </div> </div> </div> </div> </div> </div> <!-- 收益分析 --> <div class="row mb-5"> <div class="col-md-6"> <h3 class="mb-4">收益分析</h3> <div class="benefit-item"> <i class="fas fa-calculator"></i> <span>年收益：8-15万元/亩</span> </div> <div class="benefit-item"> <i class="fas fa-chart-pie"></i> <span>成本回收期：1-2年</span> </div> <div class="benefit-item"> <i class="fas fa-coins"></i> <span>额外收益：政策补贴、技术支持</span> </div> </div> <div class="col-md-6"> <div class="card"> <div class="card-body"> <h5 class="card-title">收益趋势图</h5> <canvas id="revenueChart"></canvas> </div> </div> </div> </div> <!-- 常见问题 --> <div class="row"> <div class="col-12"> <h3 class="mb-4">常见问题</h3> <div class="accordion" id="faqAccordion"> <div class="accordion-item"> <h2 class="accordion-header"> <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
如何申请土地？
</button> </h2> <div id="faq1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion"> <div class="accordion-body">
完成注册后，提交种植计划，通过审核即可申请土地。
</div> </div> </div> <div class="accordion-item"> <h2 class="accordion-header"> <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
收益如何计算？
</button> </h2> <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion"> <div class="accordion-body">
收益包括种植收益、政策补贴、技术支持等。
</div> </div> </div> <div class="accordion-item"> <h2 class="accordion-header"> <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
如何获得技术支持？
</button> </h2> <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion"> <div class="accordion-body">
平台提供专业的技术指导，包括种植技术、病虫害防治等。
</div> </div> </div> </div> </div> </div> </div> <!-- 页脚 -->
<div id="footer-container"></div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="js/login-config.js"></script>
<script src="js/components.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script> <script>
// 收益趋势图
const ctx = document.getElementById('revenueChart').getContext('2d');
const revenueChart = new Chart(ctx, {
type: 'line',
data: {
labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
datasets: [{
label: '月收益',
data: [8000, 9000, 8500, 10000, 9500, 11000],
borderColor: 'rgb(75, 192, 192)',
tension: 0.1
}]
},
options: {
responsive: true,
scales: {
y: {
beginAtZero: true
}
}
}
});
// 设置当前页面的导航链接为激活状态
document.querySelectorAll('.nav-link').forEach(link => {
const page = link.getAttribute('data-page');
if (page === 'distribution') {
link.classList.add('active');
}
});
</script> </body> </html> 