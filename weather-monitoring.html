<!DOCTYPE html> <html lang="zh-CN"> <head> <meta charset="UTF-8"> <meta name="viewport" content="width=device-width, initial-scale=1.0"> <title>气象监测 - 智慧农业平台</title> <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet"> <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet"> <link rel="stylesheet" href="css/common.css"> <link rel="stylesheet" href="css/navbar.css"> <script src="https://cdn.jsdelivr.net/npm/chart.js"></script> <style>
:root {
--primary-color: #28a745;
--secondary-color: #6c757d;
--light-bg: #f8f9fa;
--card-shadow: 0 4px 6px rgba(0,0,0,0.1);
--transition: all 0.3s ease;
}
body {
background-color: var(--light-bg);
font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}
.monitoring-header {
background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80');
background-size: cover;
background-position: center;
height: 200px;
color: white;
display: flex;
align-items: center;
margin-bottom: 30px;
border-radius: 10px;
}
.monitoring-card {
background: white;
padding: 20px;
border-radius: 10px;
box-shadow: var(--card-shadow);
margin-bottom: 30px;
min-height: auto;
}
.stat-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 20px;
transition: var(--transition);
}
.stat-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.stat-icon {
font-size: 2rem;
color: var(--primary-color);
margin-bottom: 10px;
}
.stat-value {
font-size: 1.5rem;
font-weight: bold;
color: var(--primary-color);
margin-bottom: 5px;
}
.stat-label {
font-size: 0.9rem;
color: var(--secondary-color);
}
.chart-container {
position: relative;
height: 300px;
width: 100%;
}
.map-container {
height: 400px;
border-radius: 10px;
overflow: hidden;
margin-bottom: 20px;
}
#map {
min-height: auto;
width: 100%;
}
.filter-btn {
padding: 5px 15px;
border-radius: 20px;
background-color: white;
border: 1px solid #ddd;
margin-right: 10px;
margin-bottom: 10px;
cursor: pointer;
transition: var(--transition);
}
.filter-btn.active {
background-color: var(--primary-color);
color: white;
border-color: var(--primary-color);
}
.filter-btn:hover:not(.active) {
background-color: #f0f0f0;
}
.weather-card {
display: flex;
align-items: center;
padding: 15px;
border-radius: 10px;
background: var(--light-bg);
margin-bottom: 15px;
transition: var(--transition);
}
.weather-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.weather-icon {
width: 50px;
height: 50px;
background-color: white;
border-radius: 50%;
display: flex;
align-items: center;
justify-content: center;
margin-right: 15px;
color: var(--primary-color);
font-size: 1.5rem;
}
.weather-info {
flex: 1;
}
.weather-name {
font-weight: bold;
margin-bottom: 5px;
}
.weather-status {
font-size: 0.9rem;
color: var(--secondary-color);
}
.weather-status.normal {
color: var(--primary-color);
}
.weather-status.warning {
color: #ffc107;
}
.weather-status.danger {
color: #dc3545;
}
.weather-actions {
margin-left: 15px;
}
.weather-data-card {
background: var(--light-bg);
padding: 15px;
border-radius: 10px;
margin-bottom: 15px;
transition: var(--transition);
}
.weather-data-card:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.weather-data-title {
font-weight: bold;
margin-bottom: 10px;
display: flex;
align-items: center;
}
.weather-data-icon {
margin-right: 10px;
color: var(--primary-color);
}
.weather-data-value {
font-size: 1.5rem;
font-weight: bold;
margin-bottom: 5px;
}
.weather-data-desc {
font-size: 0.9rem;
color: var(--secondary-color);
}
.weather-data-status {
display: inline-block;
padding: 3px 8px;
border-radius: 15px;
font-size: 0.8rem;
margin-left: 10px;
}
.status-normal {
background-color: rgba(40, 167, 69, 0.2);
color: var(--primary-color);
}
.status-warning {
background-color: rgba(255, 193, 7, 0.2);
color: #ffc107;
}
.status-danger {
background-color: rgba(220, 53, 69, 0.2);
color: #dc3545;
}
.recommendation-card {
background-color: rgba(40, 167, 69, 0.1);
border-left: 4px solid var(--primary-color);
padding: 15px;
margin-bottom: 15px;
border-radius: 0 5px 5px 0;
}
.recommendation-title {
font-weight: bold;
margin-bottom: 10px;
display: flex;
align-items: center;
}
.recommendation-icon {
color: var(--primary-color);
margin-right: 10px;
}
.recommendation-content {
color: var(--secondary-color);
}
.alert-item {
padding: 10px 15px;
border-radius: 5px;
margin-bottom: 10px;
display: flex;
align-items: center;
}
.alert-icon {
margin-right: 15px;
font-size: 1.2rem;
}
.alert-content {
flex: 1;
}
.alert-title {
font-weight: bold;
margin-bottom: 5px;
}
.alert-desc {
font-size: 0.9rem;
}
.alert-warning {
background-color: rgba(255, 193, 7, 0.2);
border-left: 4px solid #ffc107;
}
.alert-danger {
background-color: rgba(220, 53, 69, 0.2);
border-left: 4px solid #dc3545;
}
.alert-info {
background-color: rgba(13, 202, 240, 0.2);
border-left: 4px solid #0dcaf0;
}
.forecast-item {
display: flex;
align-items: center;
padding: 15px;
border-radius: 10px;
background: var(--light-bg);
margin-bottom: 15px;
transition: var(--transition);
}
.forecast-item:hover {
transform: translateY(-5px);
box-shadow: var(--card-shadow);
}
.forecast-icon {
width: 50px;
height: 50px;
background-color: white;
border-radius: 50%;
display: flex;
align-items: center;
justify-content: center;
margin-right: 15px;
font-size: 1.5rem;
}
.forecast-info {
flex: 1;
}
.forecast-date {
font-weight: bold;
margin-bottom: 5px;
}
.forecast-desc {
color: var(--secondary-color);
margin-bottom: 5px;
}
.forecast-temp {
font-weight: bold;
}
.history-item {
display: flex;
margin-bottom: 15px;
padding-bottom: 15px;
border-bottom: 1px solid #eee;
}
.history-icon {
width: 40px;
height: 40px;
background-color: var(--light-bg);
border-radius: 50%;
display: flex;
align-items: center;
justify-content: center;
margin-right: 15px;
color: var(--primary-color);
}
.history-content {
flex: 1;
}
.history-title {
font-weight: bold;
margin-bottom: 5px;
}
.history-time {
font-size: 0.9rem;
color: var(--secondary-color);
margin-bottom: 5px;
}
.history-desc {
font-size: 0.9rem;
}
</style> </head> <body> <!-- 导航栏 -->
<div id="navbar-container"></div>
<!-- 主要内容 --> <div class="container py-5"> <div class="monitoring-header"> <div class="container"> <h1>气象监测</h1> <p class="lead">实时监控温度、湿度、风速、降雨量等气象数据，为农业生产提供决策支持</p> </div> </div> <!-- 顶部统计卡片 --> <div class="row mb-4"> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-thermometer-half stat-icon"></i> <div class="stat-value">26.5°C</div> <div class="stat-label">当前温度</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-tint stat-icon"></i> <div class="stat-value">68%</div> <div class="stat-label">当前湿度</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-wind stat-icon"></i> <div class="stat-value">3.2 m/s</div> <div class="stat-label">当前风速</div> </div> </div> <div class="col-md-3"> <div class="stat-card text-center"> <i class="fas fa-cloud-rain stat-icon"></i> <div class="stat-value">0.0 mm</div> <div class="stat-label">今日降雨量</div> </div> </div> </div> <!-- 主要内容区域 --> <div class="row"> <!-- 左侧内容 --> <div class="col-md-8"> <!-- 气象站地图 --> <div class="monitoring-card"> <div class="d-flex justify-content-between align-items-center mb-4"> <h4>气象站分布地图</h4> <div> <button class="filter-btn active">全部</button> <button class="filter-btn">温湿度站</button> <button class="filter-btn">风速站</button> <button class="filter-btn">雨量站</button> </div> </div> <div class="map-container"> <div id="map"></div> </div> </div> <!-- 气象数据图表 --> <div class="monitoring-card"> <div class="d-flex justify-content-between align-items-center mb-4"> <h4>气象数据趋势</h4> <div> <button class="btn btn-sm btn-outline-success me-2">日</button> <button class="btn btn-sm btn-success me-2">周</button> <button class="btn btn-sm btn-outline-success">月</button> </div> </div> <div class="chart-container"> <canvas id="weatherChart"></canvas> </div> </div> <!-- 气象站状态 --> <div class="monitoring-card"> <div class="d-flex justify-content-between align-items-center mb-4"> <h4>气象站状态</h4> <a href="#" class="btn btn-sm btn-outline-success">查看全部</a> </div> <div class="weather-card"> <div class="weather-icon"> <i class="fas fa-thermometer-half"></i> </div> <div class="weather-info"> <div class="weather-name">温湿度站 #001</div> <div class="weather-status normal">正常 - 温度: 26.5°C, 湿度: 68% - 电池电量: 85%</div> </div> <div class="weather-actions"> <button class="btn btn-sm btn-outline-success">查看详情</button> </div> </div> <div class="weather-card"> <div class="weather-icon"> <i class="fas fa-wind"></i> </div> <div class="weather-info"> <div class="weather-name">风速站 #002</div> <div class="weather-status normal">正常 - 风速: 3.2 m/s, 风向: 东北 - 电池电量: 92%</div> </div> <div class="weather-actions"> <button class="btn btn-sm btn-outline-success">查看详情</button> </div> </div> <div class="weather-card"> <div class="weather-icon"> <i class="fas fa-cloud-rain"></i> </div> <div class="weather-info"> <div class="weather-name">雨量站 #003</div> <div class="weather-status normal">正常 - 今日降雨量: 0.0 mm - 电池电量: 78%</div> </div> <div class="weather-actions"> <button class="btn btn-sm btn-outline-success">查看详情</button> </div> </div> <div class="weather-card"> <div class="weather-icon"> <i class="fas fa-sun"></i> </div> <div class="weather-info"> <div class="weather-name">光照站 #004</div> <div class="weather-status warning">警告 - 光照传感器异常 - 电池电量: 65%</div> </div> <div class="weather-actions"> <button class="btn btn-sm btn-outline-warning">查看详情</button> </div> </div> </div> </div> <!-- 右侧内容 --> <div class="col-md-4"> <!-- 当前气象数据 --> <div class="monitoring-card"> <h4 class="mb-4">当前气象数据</h4> <div class="weather-data-card"> <div class="weather-data-title"> <i class="fas fa-thermometer-half weather-data-icon"></i> <span>温度</span> <span class="weather-data-status status-normal">正常</span> </div> <div class="weather-data-value">26.5°C</div> <div class="weather-data-desc">适宜范围：15°C - 30°C</div> </div> <div class="weather-data-card"> <div class="weather-data-title"> <i class="fas fa-tint weather-data-icon"></i> <span>湿度</span> <span class="weather-data-status status-normal">正常</span> </div> <div class="weather-data-value">68%</div> <div class="weather-data-desc">适宜范围：40% - 80%</div> </div> <div class="weather-data-card"> <div class="weather-data-title"> <i class="fas fa-wind weather-data-icon"></i> <span>风速</span> <span class="weather-data-status status-normal">正常</span> </div> <div class="weather-data-value">3.2 m/s</div> <div class="weather-data-desc">风向：东北</div> </div> <div class="weather-data-card"> <div class="weather-data-title"> <i class="fas fa-cloud-rain weather-data-icon"></i> <span>降雨量</span> <span class="weather-data-status status-normal">正常</span> </div> <div class="weather-data-value">0.0 mm</div> <div class="weather-data-desc">今日累计降雨量</div> </div> </div> <!-- 天气预报 --> <div class="monitoring-card"> <h4 class="mb-4">未来天气预报</h4> <div class="forecast-item"> <div class="forecast-icon"> <i class="fas fa-sun text-warning"></i> </div> <div class="forecast-info"> <div class="forecast-date">今天</div> <div class="forecast-desc">晴朗</div> <div class="forecast-temp">26°C / 18°C</div> </div> </div> <div class="forecast-item"> <div class="forecast-icon"> <i class="fas fa-cloud-sun text-secondary"></i> </div> <div class="forecast-info"> <div class="forecast-date">明天</div> <div class="forecast-desc">多云</div> <div class="forecast-temp">25°C / 17°C</div> </div> </div> <div class="forecast-item"> <div class="forecast-icon"> <i class="fas fa-cloud-rain text-info"></i> </div> <div class="forecast-info"> <div class="forecast-date">后天</div> <div class="forecast-desc">小雨</div> <div class="forecast-temp">22°C / 16°C</div> </div> </div> <div class="forecast-item"> <div class="forecast-icon"> <i class="fas fa-cloud-showers-heavy text-primary"></i> </div> <div class="forecast-info"> <div class="forecast-date">周五</div> <div class="forecast-desc">中雨</div> <div class="forecast-temp">20°C / 15°C</div> </div> </div> </div> <!-- 预警信息 --> <div class="monitoring-card"> <h4 class="mb-4">预警信息</h4> <div class="alert-item alert-warning"> <div class="alert-icon"> <i class="fas fa-exclamation-triangle"></i> </div> <div class="alert-content"> <div class="alert-title">强降雨预警</div> <div class="alert-desc">未来48小时可能有强降雨，请做好防涝准备</div> </div> </div> <div class="alert-item alert-info"> <div class="alert-icon"> <i class="fas fa-info-circle"></i> </div> <div class="alert-content"> <div class="alert-title">温度变化提醒</div> <div class="alert-desc">未来3天温度将下降5°C左右，请注意作物保温</div> </div> </div> </div> <!-- 智能建议 --> <div class="monitoring-card"> <h4 class="mb-4">智能建议</h4> <div class="recommendation-card"> <div class="recommendation-title"> <i class="fas fa-lightbulb recommendation-icon"></i> <span>防涝建议</span> </div> <div class="recommendation-content"> <p>根据天气预报，未来两天将有强降雨，建议提前检查排水系统，确保畅通。</p> <button class="btn btn-sm btn-success mt-2">查看防涝指南</button> </div> </div> <div class="recommendation-card"> <div class="recommendation-title"> <i class="fas fa-lightbulb recommendation-icon"></i> <span>作物保护建议</span> </div> <div class="recommendation-content"> <p>降雨后温度下降，建议做好作物保温措施，防止低温影响生长。</p> <button class="btn btn-sm btn-success mt-2">查看保温方案</button> </div> </div> </div> </div> </div> </div> <!-- 页脚 -->
<div id="footer-container"></div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="js/login-config.js"></script>
<script src="js/components.js"></script>
<script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script> <script>
// 使用 common.js 加载导航栏

// 初始化地图
var map = L.map('map').setView([30.7741, 120.7551], 14);
L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
attribution: '© OpenStreetMap contributors'
}).addTo(map);
// 添加气象站标记
const stations = [
{ id: 1, name: "温湿度站 #001", position: [30.7741, 120.7551], type: "temperature", status: "normal", value: "26.5°C, 68%" },
{ id: 2, name: "风速站 #002", position: [30.7841, 120.7651], type: "wind", status: "normal", value: "3.2 m/s, 东北风" },
{ id: 3, name: "雨量站 #003", position: [30.7641, 120.7451], type: "rain", status: "normal", value: "0.0 mm" },
{ id: 4, name: "光照站 #004", position: [30.7541, 120.7351], type: "light", status: "warning", value: "传感器异常" }
];
stations.forEach(station => {
const markerClass = station.status === 'normal' ? '' : station.status;
const marker = L.marker(station.position)
.bindPopup(`
<div class="text-center"> <h6>${station.name}</h6> <p class="mb-1">类型: ${station.type}</p> <p class="mb-1">状态: ${station.status}</p> <p class="mb-1">当前值: ${station.value}</p> <button class="btn btn-sm btn-success mt-2">查看详情</button> </div>
`)
.addTo(map);
});
// 初始化气象数据图表
var ctx = document.getElementById('weatherChart').getContext('2d');
var weatherChart = new Chart(ctx, {
type: 'line',
data: {
labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
datasets: [
{
label: '温度 (°C)',
data: [24, 25, 27, 26, 28, 27, 26],
borderColor: '#dc3545',
backgroundColor: 'rgba(220, 53, 69, 0.1)',
tension: 0.4,
fill: true
},
{
label: '湿度 (%)',
data: [65, 68, 62, 70, 65, 68, 68],
borderColor: '#0dcaf0',
backgroundColor: 'rgba(13, 202, 240, 0.1)',
tension: 0.4,
fill: true
},
{
label: '风速 (m/s)',
data: [2.5, 3.0, 3.5, 2.8, 3.2, 3.0, 3.2],
borderColor: '#6c757d',
backgroundColor: 'rgba(108, 117, 125, 0.1)',
tension: 0.4,
fill: true
},
{
label: '降雨量 (mm)',
data: [0, 0, 0, 0, 0, 0, 0],
borderColor: '#28a745',
backgroundColor: 'rgba(40, 167, 69, 0.1)',
tension: 0.4,
fill: true
}
]
},
options: {
responsive: true,
maintainAspectRatio: false,
plugins: {
legend: {
position: 'top',
},
tooltip: {
mode: 'index',
intersect: false
}
},
scales: {
y: {
beginAtZero: false
}
}
}
});
// 筛选按钮点击事件
document.querySelectorAll('.filter-btn').forEach(btn => {
btn.addEventListener('click', function() {
document.querySelectorAll('.filter-btn').forEach(b => {
b.classList.remove('active');
});
this.classList.add('active');
});
});
// 查看防涝指南按钮点击事件
document.querySelector('.recommendation-card .btn-success').addEventListener('click', function() {
alert('正在加载防涝指南...');
});

// 确保导航栏在页面加载完成后可见
window.onload = function() {
    // 确保导航栏可见
    const navbarContainer = document.getElementById('navbar-container');
    if (navbarContainer) {
        navbarContainer.style.display = '';
    }
};
</script> </body> </html>
