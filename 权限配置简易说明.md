# 权限配置简易说明

## 快速开始

### 1. 打开权限配置编辑器
访问 `permission-editor.html` 页面

### 2. 配置权限
1. **选择用户角色**：点击左侧的用户角色标签
2. **配置模块权限**：勾选要启用的模块
3. **配置页面权限**：选择模块下的具体页面
4. **使用快捷操作**：
   - 全选页面：为当前用户选择所有页面
   - 清空页面：清除当前用户的所有权限
   - 全部页面：为某个模块设置所有页面权限

### 3. 保存配置
1. 点击"生成配置文件"按钮
2. 浏览器会自动下载 `login-config.js` 文件
3. 将下载的文件替换项目中的 `js/login-config.js` 文件
4. 刷新页面，新的权限配置立即生效

## 配置示例

### 示例1：给普通用户增加权限
1. 选择"普通用户"标签
2. 启用"农产品品牌化运营"模块
3. 选择该模块下的所有页面
4. 生成并替换配置文件

### 示例2：为地主开放土地管理功能
1. 选择"地主"标签
2. 启用"基地土地招租与订单种植"模块
3. 选择土地相关的页面
4. 生成并替换配置文件

### 示例3：创建自定义权限组合
1. 选择任意用户角色
2. 根据需要启用不同模块
3. 精确选择每个模块下的页面
4. 生成并替换配置文件

## 重要提示

### ✅ 优点
- **直接修改配置文件**：生成的配置文件可以直接替换使用
- **立即生效**：替换文件后刷新页面即可看到效果
- **持久保存**：配置永久保存在文件中，不会丢失
- **版本控制友好**：可以将配置文件纳入版本控制

### ⚠️ 注意事项
- 每次修改权限后都需要下载新的配置文件并替换
- 替换文件前建议备份原始配置文件
- 配置文件包含完整的权限逻辑，请勿手动修改

### 🔧 故障排除
- **配置不生效**：确保已替换 js/login-config.js 文件并刷新页面
- **页面报错**：检查配置文件是否正确替换，语法是否有误
- **权限异常**：使用"重置为默认"功能恢复默认配置

## 文件说明

- **permission-editor.html**：权限配置编辑器主页面
- **js/login-config.js**：权限配置文件（需要替换的文件）
- **下载的 login-config.js**：新生成的配置文件

## 技术原理

权限配置编辑器会：
1. 读取当前的权限配置
2. 提供可视化界面进行修改
3. 生成包含完整权限逻辑的新配置文件
4. 用户下载并替换原配置文件
5. 页面刷新后加载新的权限配置

这种方式确保了配置的持久性和可靠性，避免了浏览器缓存或localStorage的限制。
