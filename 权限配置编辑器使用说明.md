# 权限配置编辑器使用说明

## 概述

权限配置编辑器是一个可视化的权限管理工具，允许您直接在网页上配置不同用户角色的权限，包括模块级和页面级的精细化权限控制。

## 访问方式

访问 `permission-editor.html` 页面即可使用权限配置编辑器。

## 功能特性

### 1. 可视化权限配置
- 直观的用户界面，支持拖拽式权限配置
- 实时预览权限变更效果
- 支持批量操作和快速配置

### 2. 多级权限控制
- **模块级权限**：控制用户是否可以访问整个功能模块
- **页面级权限**：精细控制用户可以访问模块下的哪些具体页面
- **特殊权限**：支持"全部页面"权限，简化管理员权限配置

### 3. 用户角色管理
- **未登录用户**：配置访客可以访问的页面
- **普通用户**：配置注册用户的基础权限
- **管理员**：配置管理员的完整权限
- **地主**：配置地主角色的专属权限
- **农民**：配置农民角色的专属权限

## 使用步骤

### 步骤1：选择用户角色
1. 在左侧面板中点击要配置的用户角色标签
2. 系统会显示该角色的当前权限配置
3. 右侧会显示该角色的详细权限配置界面

### 步骤2：配置模块权限
1. **启用/禁用模块**：勾选或取消勾选模块名称前的复选框
2. **全部页面权限**：点击"全部页面"按钮，给予该模块下所有页面的访问权限
3. **批量操作**：使用"全选"或"清空"按钮快速配置模块下的所有页面

### 步骤3：配置页面权限
1. 在启用的模块下，勾选或取消勾选具体的页面
2. 页面权限是基于模块权限的，只有启用模块后才能配置页面权限
3. 可以精确控制用户可以访问哪些具体功能页面

### 步骤4：保存配置
1. 修改权限后，系统会显示"配置已修改，请保存"的提示
2. 点击"保存配置"按钮应用更改
3. 保存成功后，权限立即生效

## 界面说明

### 左侧控制面板

#### 用户角色选择
- **未登录用户**：灰色标签，配置访客权限
- **普通用户**：蓝色标签，配置基础用户权限
- **管理员**：紫色标签，通常拥有所有权限
- **地主**：绿色标签，专注于监控系统权限
- **农民**：橙色标签，专注于分销系统权限

#### 快速操作
- **全选页面**：为当前用户选择所有可用页面
- **清空页面**：清除当前用户的所有页面权限
- **重置为默认**：恢复到系统默认的权限配置

#### 配置管理
- **保存配置**：应用当前的权限更改
- **导出配置**：将当前配置导出为JSON文件
- **预览更改**：查看配置的JSON格式预览

#### 当前用户信息
显示正在编辑的用户角色信息和权限统计。

### 右侧配置区域

#### 模块配置卡片
每个功能模块都有独立的配置卡片，包含：
- **模块开关**：控制是否启用该模块
- **快速操作按钮**：
  - 全部页面：设置为访问该模块的所有页面
  - 全选：选择该模块下的所有页面
  - 清空：清除该模块下的所有页面权限
- **页面列表**：该模块下所有可用页面的复选框列表

#### 权限状态指示
- **绿色提示**：显示"此用户可访问该模块下的所有页面"
- **灰色区域**：未启用的模块，页面选项不可操作
- **复选框状态**：
  - 选中：用户有权限访问该页面
  - 未选中：用户无权限访问该页面
  - 禁用：在"全部页面"模式下，单个页面选项被禁用

## 权限配置示例

### 示例1：配置普通用户权限
1. 选择"普通用户"标签
2. 启用"基地土地招租与订单种植"模块
3. 选择该模块下的前3个页面：
   - 动态地图分区浏览
   - 土地标签信息展示
   - 用户在线选地并收藏
4. 启用"农产品品牌化运营"模块
5. 选择该模块下的前2个页面：
   - 品牌故事展示模块
   - 产品溯源系统
6. 点击"保存配置"

### 示例2：配置管理员权限
1. 选择"管理员"标签
2. 点击"全选页面"按钮
3. 或者逐个模块点击"全部页面"按钮
4. 点击"保存配置"

### 示例3：配置专业角色权限
1. 选择"地主"标签
2. 启用"全过程监控系统"模块
3. 点击该模块的"全部页面"按钮
4. 点击"保存配置"

## 高级功能

### 配置导出和备份
1. 点击"导出配置"按钮
2. 系统会下载一个JSON格式的配置文件
3. 文件名格式：`permission-config-YYYY-MM-DD.json`
4. 可用于备份或在其他环境中导入配置

### 配置预览
1. 点击"预览更改"按钮
2. 在弹出的模态框中查看JSON格式的配置
3. 可以直接在预览界面应用配置

### 实时权限验证
- 配置保存后立即生效
- 当前登录用户的导航栏会实时更新
- 无权限的页面会被自动隐藏或禁用

## 注意事项

### 权限继承规则
1. **模块权限优先**：必须先启用模块，才能配置页面权限
2. **页面权限细化**：在模块权限基础上，可以精确控制页面访问
3. **全部页面模式**：设置为"全部页面"后，单个页面选项会被禁用

### 配置安全
1. **权限验证**：系统会验证权限配置的合理性
2. **默认权限**：新用户默认只有基础权限
3. **管理员权限**：建议至少保留一个管理员账号拥有完整权限

### 性能考虑
1. **实时更新**：权限更改会立即反映在用户界面上
2. **缓存机制**：配置会缓存在浏览器中，提高响应速度
3. **批量操作**：使用批量操作可以提高配置效率

## 故障排除

### 常见问题
1. **配置不生效**：确保点击了"保存配置"按钮
2. **页面无法选择**：检查是否已启用对应的模块
3. **权限丢失**：使用"重置为默认"功能恢复默认配置

### 调试信息
- 浏览器控制台会输出详细的权限配置信息
- 保存指示器会显示配置状态
- 成功/错误消息会在页面右上角显示

## 更新日志

### v1.0 (当前版本)
- 实现可视化权限配置界面
- 支持模块级和页面级权限控制
- 添加批量操作和快速配置功能
- 实现配置导出和预览功能
- 支持实时权限验证和更新
