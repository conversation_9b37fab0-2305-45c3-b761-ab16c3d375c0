# 智慧农业平台 - 用户角色权限系统说明

## 概述

智慧农业平台现已实现基于用户角色的权限控制系统，支持不同类型的用户访问不同的功能模块。系统包含4种预定义的用户角色，每种角色都有特定的权限范围。

## 用户角色说明

### 1. 普通用户 (user)
- **用户名**: `user`
- **密码**: `123456`
- **角色标识**: `user`
- **显示名称**: 普通用户
- **可访问模块**: 
  - 模块1：基地土地招租与订单种植
  - 模块2：农产品品牌化运营

### 2. 管理员 (admin)
- **用户名**: `admin`
- **密码**: `123456`
- **角色标识**: `admin`
- **显示名称**: 管理员
- **可访问模块**: 
  - 模块1：基地土地招租与订单种植
  - 模块2：农产品品牌化运营
  - 模块3：全过程监控系统
  - 模块4：二级分销体系
  - 模块5：农资农机交易系统
  - 模块6：运营支撑体系

### 3. 地主 (landlord)
- **用户名**: `landlord`
- **密码**: `123456`
- **角色标识**: `landlord`
- **显示名称**: 地主
- **可访问模块**: 
  - 模块3：全过程监控系统

### 4. 农民 (farmer)
- **用户名**: `farmer`
- **密码**: `123456`
- **角色标识**: `farmer`
- **显示名称**: 农民
- **可访问模块**: 
  - 模块4：二级分销体系

## 未登录用户权限

未登录的访客用户可以访问：
- 模块1：基地土地招租与订单种植
- 模块2：农产品品牌化运营

## 使用方法

### 登录步骤
1. 点击导航栏右上角的"登录"按钮
2. 在弹出的登录模态框中输入用户名和密码
3. 点击"登录"按钮完成登录
4. 登录成功后，导航栏会根据用户角色显示相应的模块

### 退出登录
1. 点击导航栏右上角显示用户名的按钮
2. 系统会自动退出登录并返回到未登录状态

### 测试页面
访问 `test-user-roles.html` 页面可以：
- 查看所有测试账号信息
- 快速切换不同角色登录
- 实时查看当前登录状态和权限
- 测试权限控制功能

## 技术实现

### 配置文件
- `js/login-config.js`: 包含用户账号配置和权限控制逻辑
- `js/components.js`: 包含登录处理和导航栏显示逻辑

### 权限控制机制
1. **用户验证**: 通过 `loginConfig.validateUser()` 方法验证用户名和密码
2. **模块权限**: 通过 `loginConfig.getModulesForUser()` 方法获取用户可访问的模块
3. **动态显示**: 根据用户权限动态显示/隐藏导航栏中的模块
4. **状态持久化**: 使用 localStorage 保存登录状态，页面刷新后保持登录

### 扩展性
系统设计具有良好的扩展性，可以轻松添加新的用户角色或修改现有角色的权限：

1. **添加新用户**: 在 `userAccounts` 对象中添加新的用户配置
2. **修改权限**: 调整用户配置中的 `modules` 数组
3. **添加新模块**: 在导航栏中添加新模块，并在权限配置中引用

## 安全说明

⚠️ **重要提示**: 当前实现仅用于演示和POC目的，包含以下安全限制：

1. **密码明文存储**: 所有密码都以明文形式存储在前端代码中
2. **前端验证**: 用户验证完全在前端进行，没有后端验证
3. **权限绕过**: 技术用户可以通过浏览器开发工具绕过权限限制

在生产环境中，应该：
- 使用后端API进行用户验证
- 实现密码加密和安全存储
- 在服务器端进行权限验证
- 使用JWT或其他安全令牌机制

## 故障排除

### 常见问题
1. **登录后导航栏不更新**: 检查浏览器控制台是否有JavaScript错误
2. **权限显示错误**: 清除浏览器localStorage并重新登录
3. **页面导航栏消失**: 刷新页面或检查网络连接

### 调试信息
系统会在浏览器控制台输出详细的调试信息，包括：
- 用户登录状态
- 当前用户权限
- 模块显示状态

## 更新日志

### v1.0 (当前版本)
- 实现基于角色的权限控制系统
- 支持4种预定义用户角色
- 添加登录/退出功能
- 实现动态导航栏显示
- 创建权限测试页面
